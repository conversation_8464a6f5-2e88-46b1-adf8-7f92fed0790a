
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VeasyFileManager.Api", "src\VeasyFileManager.Api\VeasyFileManager.Api.csproj", "{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VeasyFileManager.Application", "src\VeasyFileManager.Application\VeasyFileManager.Application.csproj", "{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VeasyFileManager.Domain", "src\VeasyFileManager.Domain\VeasyFileManager.Domain.csproj", "{16AB2AA0-D037-43D4-8000-7891D363D3DC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VeasyFileManager.Infrastructure", "src\VeasyFileManager.Infrastructure\VeasyFileManager.Infrastructure.csproj", "{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VeasyFileManager.UnitTests", "tests\VeasyFileManager.UnitTests\VeasyFileManager.UnitTests.csproj", "{892C32CB-B021-48CC-8001-6E6753E16B35}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "VeasyFileManager.IntegrationTests", "tests\VeasyFileManager.IntegrationTests\VeasyFileManager.IntegrationTests.csproj", "{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Debug|x64.Build.0 = Debug|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Debug|x86.Build.0 = Debug|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Release|Any CPU.Build.0 = Release|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Release|x64.ActiveCfg = Release|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Release|x64.Build.0 = Release|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Release|x86.ActiveCfg = Release|Any CPU
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA}.Release|x86.Build.0 = Release|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Debug|x64.Build.0 = Debug|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Debug|x86.Build.0 = Debug|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Release|Any CPU.Build.0 = Release|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Release|x64.ActiveCfg = Release|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Release|x64.Build.0 = Release|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Release|x86.ActiveCfg = Release|Any CPU
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D}.Release|x86.Build.0 = Release|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Debug|x64.Build.0 = Debug|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Debug|x86.Build.0 = Debug|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Release|Any CPU.Build.0 = Release|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Release|x64.ActiveCfg = Release|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Release|x64.Build.0 = Release|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Release|x86.ActiveCfg = Release|Any CPU
		{16AB2AA0-D037-43D4-8000-7891D363D3DC}.Release|x86.Build.0 = Release|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Debug|x64.Build.0 = Debug|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Debug|x86.ActiveCfg = Debug|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Debug|x86.Build.0 = Debug|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Release|Any CPU.Build.0 = Release|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Release|x64.ActiveCfg = Release|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Release|x64.Build.0 = Release|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Release|x86.ActiveCfg = Release|Any CPU
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A}.Release|x86.Build.0 = Release|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Debug|x64.ActiveCfg = Debug|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Debug|x64.Build.0 = Debug|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Debug|x86.ActiveCfg = Debug|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Debug|x86.Build.0 = Debug|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Release|Any CPU.Build.0 = Release|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Release|x64.ActiveCfg = Release|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Release|x64.Build.0 = Release|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Release|x86.ActiveCfg = Release|Any CPU
		{892C32CB-B021-48CC-8001-6E6753E16B35}.Release|x86.Build.0 = Release|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Debug|x64.Build.0 = Debug|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Debug|x86.Build.0 = Debug|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Release|Any CPU.Build.0 = Release|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Release|x64.ActiveCfg = Release|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Release|x64.Build.0 = Release|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Release|x86.ActiveCfg = Release|Any CPU
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{8FB5ECDA-E998-4D29-9A78-864E4A33C5EA} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{933AB474-6DC3-4CF5-826B-6E339CB4BB8D} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{16AB2AA0-D037-43D4-8000-7891D363D3DC} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{11D3C05E-6A84-4327-92B1-9C35D3FDE11A} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{892C32CB-B021-48CC-8001-6E6753E16B35} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{BAF83E32-A99C-4EA2-9CC6-852E4A7E86E5} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
	EndGlobalSection
EndGlobal
