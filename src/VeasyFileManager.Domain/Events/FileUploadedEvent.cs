using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Domain.Events;

/// <summary>
/// Domain event raised when a file is successfully uploaded
/// </summary>
public sealed record FileUploadedEvent(
    Guid FileId,
    string FileName,
    long FileSize,
    string MimeType,
    Guid OwnerId,
    Guid? ParentFolderId,
    DateTime UploadedAt) : IDomainEvent
{
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
} 