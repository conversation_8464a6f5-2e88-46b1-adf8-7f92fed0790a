using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Domain.Events;

/// <summary>
/// Domain event raised when a file is shared
/// </summary>
public sealed record FileSharedEvent(
    Guid FileId,
    string FileName,
    Guid SharedBy,
    ShareType ShareType,
    string ShareToken,
    DateTime? ExpiresAt) : IDomainEvent
{
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
} 