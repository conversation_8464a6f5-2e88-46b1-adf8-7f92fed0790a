using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Domain.Events;

/// <summary>
/// Domain event raised when a permission is granted to a file or folder
/// </summary>
public sealed record PermissionGrantedEvent(
    Guid ResourceId,
    string ResourceType, // "File" or "Folder"
    Guid? UserId,
    Guid? RoleId,
    PermissionType PermissionType,
    Guid GrantedBy,
    DateTime? ExpiresAt) : IDomainEvent
{
    public DateTime OccurredOn { get; init; } = DateTime.UtcNow;
} 