using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Domain.Entities;

public class Folder : AggregateRoot
{
    public string Name { get; private set; }
    public Guid? ParentFolderId { get; private set; }
    public Guid OwnerId { get; private set; }
    public string Path { get; private set; }
    public int Level { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime UpdatedAt { get; private set; }
    public DateTime? DeletedAt { get; private set; }
    public bool IsDeleted { get; private set; }

    private readonly List<FolderPermission> _permissions = new();
    public IReadOnlyList<FolderPermission> Permissions => _permissions.AsReadOnly();

    // Navigation properties for EF Core
    public virtual Folder? ParentFolder { get; private set; }
    public virtual ICollection<Folder> SubFolders { get; private set; } = new List<Folder>();
    public virtual ICollection<File> Files { get; private set; } = new List<File>();
    public virtual ICollection<FolderShare> Shares { get; private set; } = new List<FolderShare>();

    // EF Core constructor
    protected Folder() { }

    private Folder(
        string name,
        Guid ownerId,
        Guid? parentFolderId = null,
        string? parentPath = null)
    {
        Id = Guid.NewGuid();
        Name = name;
        OwnerId = ownerId;
        ParentFolderId = parentFolderId;
        
        // Calculate path and level
        if (parentFolderId == null)
        {
            Path = $"/{name}";
            Level = 0;
        }
        else
        {
            Path = $"{parentPath}/{name}";
            Level = parentPath?.Count(c => c == '/') ?? 0;
        }

        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        IsDeleted = false;
    }

    public static Folder Create(
        string name,
        Guid ownerId,
        Guid? parentFolderId = null,
        string? parentPath = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("Folder name cannot be empty.", nameof(name));

        if (ownerId == Guid.Empty)
            throw new ArgumentException("Owner ID cannot be empty.", nameof(ownerId));

        // Validate folder name
        var invalidChars = System.IO.Path.GetInvalidFileNameChars();
        if (name.Any(c => invalidChars.Contains(c)))
            throw new ArgumentException("Folder name contains invalid characters.", nameof(name));

        return new Folder(name, ownerId, parentFolderId, parentPath);
    }

    public void Rename(string newName)
    {
        if (string.IsNullOrWhiteSpace(newName))
            throw new ArgumentException("Folder name cannot be empty.", nameof(newName));

        var invalidChars = System.IO.Path.GetInvalidFileNameChars();
        if (newName.Any(c => invalidChars.Contains(c)))
            throw new ArgumentException("Folder name contains invalid characters.", nameof(newName));

        Name = newName;
        UpdatePath();
        UpdatedAt = DateTime.UtcNow;
    }

    public void Move(Guid? newParentFolderId, string? newParentPath)
    {
        if (ParentFolderId == newParentFolderId)
            return;

        ParentFolderId = newParentFolderId;
        
        if (newParentFolderId == null)
        {
            Path = $"/{Name}";
            Level = 0;
        }
        else
        {
            Path = $"{newParentPath}/{Name}";
            Level = newParentPath?.Count(c => c == '/') ?? 0;
        }

        UpdatedAt = DateTime.UtcNow;
        
        // Update paths of all subfolders
        UpdateSubFolderPaths();
    }

    private void UpdatePath()
    {
        if (ParentFolderId == null)
        {
            Path = $"/{Name}";
        }
        else if (ParentFolder != null)
        {
            Path = $"{ParentFolder.Path}/{Name}";
        }
    }

    private void UpdateSubFolderPaths()
    {
        foreach (var subFolder in SubFolders)
        {
            subFolder.Path = $"{Path}/{subFolder.Name}";
            subFolder.Level = Level + 1;
            subFolder.UpdateSubFolderPaths();
        }
    }

    public void Delete()
    {
        if (IsDeleted)
            return;

        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;

        // Cascade delete to subfolders and files
        foreach (var subFolder in SubFolders)
        {
            subFolder.Delete();
        }

        foreach (var file in Files)
        {
            file.Delete();
        }
    }

    public void Restore()
    {
        if (!IsDeleted)
            return;

        IsDeleted = false;
        DeletedAt = null;
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddPermission(FolderPermission permission)
    {
        if (_permissions.Any(p => p.UserId == permission.UserId && p.PermissionType == permission.PermissionType))
            return;

        _permissions.Add(permission);
        UpdatedAt = DateTime.UtcNow;
    }

    public void RemovePermission(Guid permissionId)
    {
        var permission = _permissions.FirstOrDefault(p => p.Id == permissionId);
        if (permission != null)
        {
            _permissions.Remove(permission);
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public void AddFile(File file)
    {
        if (file.ParentFolderId != Id)
            throw new InvalidOperationException("File parent folder ID does not match this folder.");

        Files.Add(file);
        UpdatedAt = DateTime.UtcNow;
    }

    public void RemoveFile(File file)
    {
        Files.Remove(file);
        UpdatedAt = DateTime.UtcNow;
    }

    public void AddSubFolder(Folder folder)
    {
        if (folder.ParentFolderId != Id)
            throw new InvalidOperationException("Subfolder parent folder ID does not match this folder.");

        SubFolders.Add(folder);
        UpdatedAt = DateTime.UtcNow;
    }

    public void RemoveSubFolder(Folder folder)
    {
        SubFolders.Remove(folder);
        UpdatedAt = DateTime.UtcNow;
    }

    public bool IsAncestorOf(Guid folderId)
    {
        foreach (var subFolder in SubFolders)
        {
            if (subFolder.Id == folderId || subFolder.IsAncestorOf(folderId))
                return true;
        }
        return false;
    }
} 