using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Domain.Entities;

public class FolderPermission : Entity
{
    public Guid FolderId { get; set; }
    public Guid? UserId { get; private set; }
    public Guid? RoleId { get; private set; }
    public PermissionType PermissionType { get; private set; }
    public Guid GrantedBy { get; private set; }
    public DateTime GrantedAt { get; private set; }
    public DateTime? ExpiresAt { get; set; }
    public bool IsActive { get; private set; }
    public bool InheritToChildren { get; set; }

    // EF Core constructor
    protected FolderPermission() { }

    private FolderPermission(
        Guid folderId,
        Guid? userId,
        Guid? roleId,
        PermissionType permissionType,
        Guid grantedBy,
        DateTime? expiresAt = null,
        bool inheritToChildren = true)
    {
        Id = Guid.NewGuid();
        FolderId = folderId;
        UserId = userId;
        RoleId = roleId;
        PermissionType = permissionType;
        GrantedBy = grantedBy;
        GrantedAt = DateTime.UtcNow;
        ExpiresAt = expiresAt;
        IsActive = true;
        InheritToChildren = inheritToChildren;
    }

    public static FolderPermission Create(
        Guid folderId,
        Guid? userId,
        Guid? roleId,
        PermissionType permissionType,
        Guid grantedBy,
        DateTime? expiresAt = null,
        bool inheritToChildren = true)
    {
        if (folderId == Guid.Empty)
            throw new ArgumentException("Folder ID cannot be empty.", nameof(folderId));

        if (userId == null && roleId == null)
            throw new ArgumentException("Either User ID or Role ID must be provided.");

        if (grantedBy == Guid.Empty)
            throw new ArgumentException("Granted by cannot be empty.", nameof(grantedBy));

        return new FolderPermission(folderId, userId, roleId, permissionType, grantedBy, expiresAt, inheritToChildren);
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
    }

    public bool IsValidFor(Guid userId, List<Guid>? userRoles = null)
    {
        if (!IsActive || IsExpired())
            return false;

        if (UserId.HasValue && UserId.Value == userId)
            return true;

        if (RoleId.HasValue && userRoles != null && userRoles.Contains(RoleId.Value))
            return true;

        return false;
    }

    public void UpdateInheritance(bool inheritToChildren)
    {
        InheritToChildren = inheritToChildren;
    }
} 