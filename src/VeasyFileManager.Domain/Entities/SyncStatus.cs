using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Domain.Entities;

public class SyncStatus : Entity
{
    public Guid FileId { get; private set; }
    public string Provider { get; private set; }
    public string? ExternalId { get; private set; }
    public DateTime? LastSyncAt { get; private set; }
    public Enums.SyncStatus Status { get; private set; }
    public string? ErrorMessage { get; private set; }
    public int RetryCount { get; private set; }

    // Navigation property
    public virtual File File { get; private set; }

    // EF Core constructor
    protected SyncStatus() { }

    private SyncStatus(
        Guid fileId,
        string provider,
        string? externalId = null)
    {
        Id = Guid.NewGuid();
        FileId = fileId;
        Provider = provider;
        ExternalId = externalId;
        Status = Enums.SyncStatus.Pending;
        RetryCount = 0;
    }

    public static SyncStatus Create(Guid fileId, string provider, string? externalId = null)
    {
        if (fileId == Guid.Empty)
            throw new ArgumentException("File ID cannot be empty.", nameof(fileId));

        if (string.IsNullOrWhiteSpace(provider))
            throw new ArgumentException("Provider cannot be empty.", nameof(provider));

        return new SyncStatus(fileId, provider, externalId);
    }

    public void StartSync()
    {
        Status = Enums.SyncStatus.Syncing;
        ErrorMessage = null;
    }

    public void CompleteSync(string externalId)
    {
        if (string.IsNullOrWhiteSpace(externalId))
            throw new ArgumentException("External ID cannot be empty.", nameof(externalId));

        Status = Enums.SyncStatus.Synced;
        ExternalId = externalId;
        LastSyncAt = DateTime.UtcNow;
        ErrorMessage = null;
        RetryCount = 0;
    }

    public void FailSync(string errorMessage)
    {
        Status = Enums.SyncStatus.Failed;
        ErrorMessage = errorMessage;
        RetryCount++;
    }

    public void ResetForRetry()
    {
        Status = Enums.SyncStatus.Pending;
        ErrorMessage = null;
    }

    public bool CanRetry(int maxRetries = 3)
    {
        return Status == Enums.SyncStatus.Failed && RetryCount < maxRetries;
    }

    public bool NeedsSync(TimeSpan syncInterval)
    {
        if (Status == Enums.SyncStatus.Failed || Status == Enums.SyncStatus.Pending)
            return true;

        if (LastSyncAt == null)
            return true;

        return DateTime.UtcNow - LastSyncAt.Value > syncInterval;
    }
} 