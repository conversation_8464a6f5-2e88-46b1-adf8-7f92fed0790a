using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Domain.Entities;

/// <summary>
/// Represents a deleted item (folder or file) that can be restored within 30 days
/// </summary>
public class DeletedItem : Entity
{
    /// <summary>
    /// Original ID of the deleted item (folder or file)
    /// </summary>
    public Guid OriginalId { get; private set; }

    /// <summary>
    /// Type of the deleted item (Folder or File)
    /// </summary>
    public DeletedItemType ItemType { get; private set; }

    /// <summary>
    /// Original name of the deleted item
    /// </summary>
    public string OriginalName { get; private set; }

    /// <summary>
    /// Original path of the deleted item
    /// </summary>
    public string OriginalPath { get; private set; }

    /// <summary>
    /// ID of the parent folder (null for root items)
    /// </summary>
    public Guid? ParentFolderId { get; private set; }

    /// <summary>
    /// ID of the user who deleted the item
    /// </summary>
    public Guid DeletedBy { get; private set; }

    /// <summary>
    /// Date and time when the item was deleted
    /// </summary>
    public DateTime DeletedAt { get; private set; }

    /// <summary>
    /// Date and time when the item was restored (null if not restored)
    /// </summary>
    public DateTime? RestoredAt { get; private set; }

    /// <summary>
    /// ID of the user who restored the item (null if not restored)
    /// </summary>
    public Guid? RestoredBy { get; private set; }

    /// <summary>
    /// Whether the item can still be restored (within 30 days and not yet restored)
    /// </summary>
    public bool CanRestore => RestoredAt == null && DeletedAt >= DateTime.UtcNow.AddDays(-30);

    /// <summary>
    /// Whether the item should be permanently deleted (older than 30 days)
    /// </summary>
    public bool ShouldPermanentlyDelete => DateTime.UtcNow > DeletedAt.AddDays(30);

    /// <summary>
    /// Original size in bytes (for files)
    /// </summary>
    public long? OriginalSize { get; private set; }

    /// <summary>
    /// Original content type (for files)
    /// </summary>
    public string? OriginalContentType { get; private set; }

    /// <summary>
    /// Serialized metadata for restoration
    /// </summary>
    public string? Metadata { get; private set; }

    /// <summary>
    /// Days remaining until permanent deletion
    /// </summary>
    public int DaysRemaining => Math.Max(0, 30 - (int)(DateTime.UtcNow - DeletedAt).TotalDays);

    // EF Core constructor
    protected DeletedItem() { }

    private DeletedItem(
        Guid originalId,
        DeletedItemType itemType,
        string originalName,
        string originalPath,
        Guid? parentFolderId,
        Guid deletedBy,
        long? originalSize = null,
        string? originalContentType = null,
        string? metadata = null)
    {
        Id = Guid.NewGuid();
        OriginalId = originalId;
        ItemType = itemType;
        OriginalName = originalName;
        OriginalPath = originalPath;
        ParentFolderId = parentFolderId;
        DeletedBy = deletedBy;
        DeletedAt = DateTime.UtcNow;
        OriginalSize = originalSize;
        OriginalContentType = originalContentType;
        Metadata = metadata;
    }

    /// <summary>
    /// Create a DeletedItem record for a deleted file
    /// </summary>
    /// <param name="file">The file that was deleted</param>
    /// <param name="deletedByUserId">User who deleted the file</param>
    /// <returns>New DeletedItem instance</returns>
    public static DeletedItem CreateForFile(File file, Guid deletedByUserId)
    {
        return new DeletedItem
        {
            Id = Guid.NewGuid(),
            OriginalId = file.Id,
            ItemType = DeletedItemType.File,
            OriginalName = file.Name,
            OriginalPath = file.FilePath ?? file.Name,
            ParentFolderId = file.ParentFolderId,
            DeletedBy = deletedByUserId,
            DeletedAt = DateTime.UtcNow,
            OriginalSize = file.FileSize,
            OriginalContentType = file.MimeType,
            RestoredAt = null,
            RestoredBy = null
        };
    }

    /// <summary>
    /// Create a DeletedItem record for a deleted folder
    /// </summary>
    /// <param name="folder">The folder that was deleted</param>
    /// <param name="deletedByUserId">User who deleted the folder</param>
    /// <returns>New DeletedItem instance</returns>
    public static DeletedItem CreateForFolder(Folder folder, Guid deletedByUserId)
    {
        return new DeletedItem
        {
            Id = Guid.NewGuid(),
            OriginalId = folder.Id,
            ItemType = DeletedItemType.Folder,
            OriginalName = folder.Name,
            OriginalPath = $"/{folder.Name}", // Could be enhanced with full path if needed
            ParentFolderId = folder.ParentFolderId,
            DeletedBy = deletedByUserId,
            DeletedAt = DateTime.UtcNow,
            OriginalSize = null, // Folders don't have size
            OriginalContentType = null,
            RestoredAt = null,
            RestoredBy = null
        };
    }

    /// <summary>
    /// Mark the item as restored
    /// </summary>
    /// <param name="restoredByUserId">User who restored the item</param>
    public void MarkAsRestored(Guid restoredByUserId)
    {
        RestoredAt = DateTime.UtcNow;
        RestoredBy = restoredByUserId;
    }
}
