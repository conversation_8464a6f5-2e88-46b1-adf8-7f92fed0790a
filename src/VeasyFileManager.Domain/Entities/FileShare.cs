using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Domain.Entities;

public class FileShare : Entity
{
    public Guid FileId { get; private set; }
    public string ShareToken { get; private set; }
    public Guid SharedBy { get; private set; }
    public ShareType ShareType { get; private set; }
    public string? PasswordHash { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public int? MaxDownloads { get; private set; }
    public int DownloadCount { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public bool IsActive { get; private set; }

    // EF Core constructor
    protected FileShare() { }

    private FileShare(
        Guid fileId,
        string shareToken,
        Guid sharedBy,
        ShareType shareType,
        string? passwordHash = null,
        DateTime? expiresAt = null,
        int? maxDownloads = null)
    {
        Id = Guid.NewGuid();
        FileId = fileId;
        ShareToken = shareToken;
        SharedBy = sharedBy;
        ShareType = shareType;
        PasswordHash = passwordHash;
        ExpiresAt = expiresAt;
        MaxDownloads = maxDownloads;
        DownloadCount = 0;
        CreatedAt = DateTime.UtcNow;
        IsActive = true;
    }

    public static FileShare Create(
        Guid fileId,
        Guid sharedBy,
        ShareType shareType,
        string? password = null,
        DateTime? expiresAt = null,
        int? maxDownloads = null)
    {
        if (fileId == Guid.Empty)
            throw new ArgumentException("File ID cannot be empty.", nameof(fileId));

        if (sharedBy == Guid.Empty)
            throw new ArgumentException("Shared by cannot be empty.", nameof(sharedBy));

        if (shareType == ShareType.Password && string.IsNullOrWhiteSpace(password))
            throw new ArgumentException("Password is required for password-protected shares.", nameof(password));

        var shareToken = GenerateShareToken();
        var passwordHash = shareType == ShareType.Password && !string.IsNullOrWhiteSpace(password)
            ? HashPassword(password)
            : null;

        return new FileShare(fileId, shareToken, sharedBy, shareType, passwordHash, expiresAt, maxDownloads);
    }

    private static string GenerateShareToken()
    {
        // Generate a secure random token
        var bytes = new byte[32];
        using (var rng = System.Security.Cryptography.RandomNumberGenerator.Create())
        {
            rng.GetBytes(bytes);
        }
        return Convert.ToBase64String(bytes)
            .Replace("+", "-")
            .Replace("/", "_")
            .Replace("=", "");
    }

    private static string HashPassword(string password)
    {
        // In a real implementation, use a proper password hashing library like BCrypt
        // This is a simplified version for demonstration
        using (var sha256 = System.Security.Cryptography.SHA256.Create())
        {
            var bytes = System.Text.Encoding.UTF8.GetBytes(password);
            var hash = sha256.ComputeHash(bytes);
            return Convert.ToBase64String(hash);
        }
    }

    public bool ValidatePassword(string password)
    {
        if (ShareType != ShareType.Password || string.IsNullOrWhiteSpace(PasswordHash))
            return true;

        var providedHash = HashPassword(password);
        return PasswordHash == providedHash;
    }

    public void IncrementDownloadCount()
    {
        DownloadCount++;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
    }

    public bool HasReachedDownloadLimit()
    {
        return MaxDownloads.HasValue && DownloadCount >= MaxDownloads.Value;
    }

    public bool IsValid(string? password = null)
    {
        if (!IsActive || IsExpired() || HasReachedDownloadLimit())
            return false;

        if (ShareType == ShareType.Password && !ValidatePassword(password ?? string.Empty))
            return false;

        return true;
    }
} 