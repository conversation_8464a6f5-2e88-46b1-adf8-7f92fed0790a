using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Domain.Entities;

public class FilePermission : Entity
{
    public Guid FileId { get; private set; }
    public Guid? UserId { get; private set; }
    public Guid? RoleId { get; private set; }
    public PermissionType PermissionType { get; private set; }
    public Guid GrantedBy { get; private set; }
    public DateTime GrantedAt { get; private set; }
    public DateTime? ExpiresAt { get; private set; }
    public bool IsActive { get; private set; }

    // EF Core constructor
    protected FilePermission()
    {
    }

    private FilePermission(
        Guid fileId,
        Guid? userId,
        Guid? roleId,
        PermissionType permissionType,
        Guid grantedBy,
        DateTime? expiresAt = null)
    {
        Id = Guid.NewGuid();
        FileId = fileId;
        UserId = userId;
        RoleId = roleId;
        PermissionType = permissionType;
        GrantedBy = grantedBy;
        GrantedAt = DateTime.UtcNow;
        ExpiresAt = expiresAt;
        IsActive = true;
    }

    public static FilePermission Create(
        Guid fileId,
        Guid? userId,
        Guid? roleId,
        PermissionType permissionType,
        Guid grantedBy,
        DateTime? expiresAt = null)
    {
        if (fileId == Guid.Empty)
            throw new ArgumentException("File ID cannot be empty.", nameof(fileId));

        if (userId == null && roleId == null)
            throw new ArgumentException("Either User ID or Role ID must be provided.");

        if (grantedBy == Guid.Empty)
            throw new ArgumentException("Granted by cannot be empty.", nameof(grantedBy));

        return new FilePermission(fileId, userId, roleId, permissionType, grantedBy, expiresAt);
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public bool IsExpired()
    {
        return ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;
    }

    public bool IsValidFor(Guid userId, List<Guid>? userRoles = null)
    {
        if (!IsActive || IsExpired())
            return false;

        if (UserId.HasValue && UserId.Value == userId)
            return true;

        if (RoleId.HasValue && userRoles != null && userRoles.Contains(RoleId.Value))
            return true;

        return false;
    }
}