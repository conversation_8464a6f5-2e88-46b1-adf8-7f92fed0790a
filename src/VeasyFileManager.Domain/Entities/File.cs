using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Events;
using VeasyFileManager.Domain.ValueObjects;

namespace VeasyFileManager.Domain.Entities;

public class File : AggregateRoot
{
    public string Name { get; private set; }
    public string DisplayName { get; private set; }
    public string? FilePath { get; private set; }
    public long FileSize { get; private set; }
    public string MimeType { get; private set; }
    public string HashMd5 { get; private set; }
    public string HashSha256 { get; private set; }
    public StorageProvider StorageProvider { get; private set; }
    public string? ExternalId { get; private set; }
    public Guid? ParentFolderId { get; private set; }
    public Guid OwnerId { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime UpdatedAt { get; private set; }
    public DateTime? DeletedAt { get; private set; }
    public bool IsDeleted { get; private set; }
    public int Version { get; private set; }

    private readonly List<FilePermission> _permissions = new();
    public IReadOnlyList<FilePermission> Permissions => _permissions.AsReadOnly();

    private readonly List<FileShare> _shares = new();
    public IReadOnlyList<FileShare> Shares => _shares.AsReadOnly();

    // EF Core constructor
    protected File() { }

    private File(
        string name,
        string displayName,
        long fileSize,
        string mimeType,
        string hashMd5,
        string hashSha256,
        Guid ownerId,
        Guid? parentFolderId = null)
    {
        Id = Guid.NewGuid();
        Name = name;
        DisplayName = displayName;
        FileSize = fileSize;
        MimeType = mimeType;
        HashMd5 = hashMd5;
        HashSha256 = hashSha256;
        OwnerId = ownerId;
        ParentFolderId = parentFolderId;
        StorageProvider = StorageProvider.R2; // Default to R2
        CreatedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        IsDeleted = false;
        Version = 1;
    }

    public static File Create(
        string name,
        string displayName,
        long fileSize,
        string mimeType,
        string hashMd5,
        string hashSha256,
        Guid ownerId,
        Guid? parentFolderId = null)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentException("File name cannot be empty.", nameof(name));

        if (string.IsNullOrWhiteSpace(displayName))
            throw new ArgumentException("Display name cannot be empty.", nameof(displayName));

        if (fileSize <= 0)
            throw new ArgumentException("File size must be greater than zero.", nameof(fileSize));

        if (string.IsNullOrWhiteSpace(mimeType))
            throw new ArgumentException("MIME type cannot be empty.", nameof(mimeType));

        if (ownerId == Guid.Empty)
            throw new ArgumentException("Owner ID cannot be empty.", nameof(ownerId));

        var file = new File(
            name,
            displayName,
            fileSize,
            mimeType,
            hashMd5,
            hashSha256,
            ownerId,
            parentFolderId);

        // Add domain event
        file.AddDomainEvent(new FileUploadedEvent(
            file.Id, 
            file.Name, 
            file.FileSize, 
            file.MimeType, 
            file.OwnerId, 
            file.ParentFolderId, 
            file.CreatedAt));

        return file;
    }

    public void UpdateMetadata(string displayName, string? mimeType = null)
    {
        if (string.IsNullOrWhiteSpace(displayName))
            throw new ArgumentException("Display name cannot be empty.", nameof(displayName));

        DisplayName = displayName;
        
        if (!string.IsNullOrWhiteSpace(mimeType))
            MimeType = mimeType;

        UpdatedAt = DateTime.UtcNow;
        Version++;
    }

    public void Move(Guid? newParentFolderId)
    {
        if (ParentFolderId == newParentFolderId)
            return;

        ParentFolderId = newParentFolderId;
        UpdatedAt = DateTime.UtcNow;
        Version++;
    }

    public void SetStoragePath(string filePath, StorageProvider provider)
    {
        FilePath = filePath;
        StorageProvider = provider;
        UpdatedAt = DateTime.UtcNow;
    }

    public void SetExternalId(string externalId)
    {
        ExternalId = externalId;
        UpdatedAt = DateTime.UtcNow;
    }

    public void Delete()
    {
        if (IsDeleted)
            return;

        IsDeleted = true;
        DeletedAt = DateTime.UtcNow;
        UpdatedAt = DateTime.UtcNow;
        Version++;

        AddDomainEvent(new FileDeletedEvent(Id, Name, OwnerId, OwnerId, true));
    }

    public void Restore()
    {
        if (!IsDeleted)
            return;

        IsDeleted = false;
        DeletedAt = null;
        UpdatedAt = DateTime.UtcNow;
        Version++;
    }

    public void AddPermission(FilePermission permission)
    {
        if (_permissions.Any(p => p.UserId == permission.UserId && p.PermissionType == permission.PermissionType))
            return;

        _permissions.Add(permission);
        UpdatedAt = DateTime.UtcNow;
    }

    public void RemovePermission(Guid permissionId)
    {
        var permission = _permissions.FirstOrDefault(p => p.Id == permissionId);
        if (permission != null)
        {
            _permissions.Remove(permission);
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public void AddShare(FileShare share)
    {
        _shares.Add(share);
        UpdatedAt = DateTime.UtcNow;

        AddDomainEvent(new FileSharedEvent(Id, Name, share.SharedBy, share.ShareType, share.ShareToken, share.ExpiresAt));
    }

    public void RemoveShare(string shareToken)
    {
        var share = _shares.FirstOrDefault(s => s.ShareToken == shareToken);
        if (share != null)
        {
            _shares.Remove(share);
            UpdatedAt = DateTime.UtcNow;
        }
    }
} 