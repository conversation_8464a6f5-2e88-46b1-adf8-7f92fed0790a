using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Domain.ValueObjects;

public class FilePath : ValueObject
{
    public string Value { get; private set; }

    private FilePath(string value)
    {
        Value = value;
    }

    public static FilePath Create(string value)
    {
        if (string.IsNullOrWhiteSpace(value))
            throw new ArgumentException("File path cannot be empty.", nameof(value));

        // Normalize path separators
        value = value.Replace('\\', '/').Trim();

        // Remove duplicate slashes
        while (value.Contains("//"))
        {
            value = value.Replace("//", "/");
        }

        // Validate path doesn't contain invalid characters
        var invalidChars = Path.GetInvalidPathChars();
        if (value.Any(c => invalidChars.Contains(c)))
            throw new ArgumentException("File path contains invalid characters.", nameof(value));

        return new FilePath(value);
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }

    public override string ToString() => Value;

    public static implicit operator string(FilePath filePath) => filePath.Value;
} 