using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Domain.ValueObjects;

public class FileHash : ValueObject
{
    public string Md5 { get; private set; }
    public string Sha256 { get; private set; }

    private FileHash(string md5, string sha256)
    {
        Md5 = md5;
        Sha256 = sha256;
    }

    public static FileHash Create(string md5, string sha256)
    {
        if (string.IsNullOrWhiteSpace(md5))
            throw new ArgumentException("MD5 hash cannot be empty.", nameof(md5));

        if (string.IsNullOrWhiteSpace(sha256))
            throw new ArgumentException("SHA256 hash cannot be empty.", nameof(sha256));

        // Validate MD5 format (32 hex characters)
        if (md5.Length != 32 || !IsHexString(md5))
            throw new ArgumentException("Invalid MD5 hash format.", nameof(md5));

        // Validate SHA256 format (64 hex characters)
        if (sha256.Length != 64 || !IsHexString(sha256))
            throw new ArgumentException("Invalid SHA256 hash format.", nameof(sha256));

        return new FileHash(md5.ToLowerInvariant(), sha256.ToLowerInvariant());
    }

    private static bool IsHexString(string value)
    {
        return value.All(c => (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F'));
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Md5;
        yield return Sha256;
    }

    public override string ToString() => $"MD5: {Md5}, SHA256: {Sha256}";
} 