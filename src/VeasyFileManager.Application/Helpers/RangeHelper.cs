using Microsoft.Extensions.Primitives;
using VeasyFileManager.Application.Interfaces;

namespace VeasyFileManager.Application.Helpers;

/// <summary>
/// Helper class for handling HTTP Range requests
/// </summary>
public static class RangeHelper
{
    /// <summary>
    /// Parse HTTP Range header into ByteRange object
    /// </summary>
    /// <param name="rangeHeader">Range header value (e.g., "bytes=0-1023")</param>
    /// <param name="fileSize">Total file size</param>
    /// <returns>Parsed ByteRange or null if invalid</returns>
    public static ByteRange? ParseRangeHeader(string? rangeHeader, long fileSize)
    {
        if (string.IsNullOrWhiteSpace(rangeHeader))
            return null;

        // Range header format: "bytes=start-end" or "bytes=start-" or "bytes=-suffix"
        if (!rangeHeader.StartsWith("bytes=", StringComparison.OrdinalIgnoreCase))
            return null;

        var rangeValue = rangeHeader["bytes=".Length..];
        var parts = rangeValue.Split('-', 2);

        if (parts.Length != 2)
            return null;

        var startPart = parts[0];
        var endPart = parts[1];

        long start;
        long? end = null;

        if (string.IsNullOrEmpty(startPart))
        {
            // Suffix range: "bytes=-1024" (last 1024 bytes)
            if (!long.TryParse(endPart, out var suffix) || suffix <= 0)
                return null;

            start = Math.Max(0, fileSize - suffix);
            end = fileSize - 1;
        }
        else if (string.IsNullOrEmpty(endPart))
        {
            // Start range: "bytes=1024-" (from byte 1024 to end)
            if (!long.TryParse(startPart, out start) || start < 0)
                return null;

            if (start >= fileSize)
                return null;

            end = fileSize - 1;
        }
        else
        {
            // Full range: "bytes=1024-2047"
            if (!long.TryParse(startPart, out start) || !long.TryParse(endPart, out var endValue))
                return null;

            if (start < 0 || endValue < start)
                return null;

            end = Math.Min(endValue, fileSize - 1);
        }

        var range = new ByteRange { Start = start, End = end };
        return range.IsValid(fileSize) ? range : null;
    }

    /// <summary>
    /// Parse range from HTTP headers
    /// </summary>
    /// <param name="headers">HTTP headers dictionary</param>
    /// <param name="fileSize">Total file size</param>
    /// <returns>Parsed ByteRange or null if invalid</returns>
    public static ByteRange? ParseRangeFromHeaders(IDictionary<string, StringValues> headers, long fileSize)
    {
        if (headers.TryGetValue("Range", out var rangeValues))
        {
            return ParseRangeHeader(rangeValues.FirstOrDefault(), fileSize);
        }

        return null;
    }

    /// <summary>
    /// Generate Content-Range header value
    /// </summary>
    /// <param name="range">Byte range</param>
    /// <param name="fileSize">Total file size</param>
    /// <returns>Content-Range header value</returns>
    public static string GenerateContentRangeHeader(ByteRange range, long fileSize)
    {
        var actualEnd = range.GetActualEnd(fileSize);
        return $"bytes {range.Start}-{actualEnd}/{fileSize}";
    }

    /// <summary>
    /// Check if the request accepts ranges
    /// </summary>
    /// <param name="headers">HTTP headers dictionary</param>
    /// <returns>True if range requests are accepted</returns>
    public static bool AcceptsRanges(IDictionary<string, StringValues> headers)
    {
        return !headers.ContainsKey("Range") ||
               headers.TryGetValue("Accept-Ranges", out var acceptRanges) &&
               acceptRanges.Any(v => v.Contains("bytes", StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// Calculate optimal chunk size for streaming based on file size
    /// </summary>
    /// <param name="fileSize">File size in bytes</param>
    /// <returns>Optimal chunk size for streaming</returns>
    public static int CalculateOptimalChunkSize(long fileSize)
    {
        // For small files (< 1MB), use 64KB chunks
        if (fileSize < 1024 * 1024)
            return 64 * 1024;

        // For medium files (1MB - 100MB), use 256KB chunks
        if (fileSize < 100 * 1024 * 1024)
            return 256 * 1024;

        // For large files (100MB - 1GB), use 1MB chunks
        if (fileSize < 1024 * 1024 * 1024)
            return 1024 * 1024;

        // For very large files (> 1GB), use 2MB chunks
        return 2 * 1024 * 1024;
    }

    /// <summary>
    /// Determine if a file type is streamable/viewable in browser
    /// </summary>
    /// <param name="contentType">MIME content type</param>
    /// <returns>True if the file can be streamed/viewed in browser</returns>
    public static bool IsStreamableContentType(string contentType)
    {
        if (string.IsNullOrWhiteSpace(contentType))
            return false;

        var type = contentType.ToLowerInvariant();

        // Images
        if (type.StartsWith("image/"))
            return true;

        // Videos
        if (type.StartsWith("video/"))
            return true;

        // Audio
        if (type.StartsWith("audio/"))
            return true;

        // Documents that can be viewed in browser
        if (type.Contains("pdf") ||
            type.Contains("text/") ||
            type.Contains("application/json") ||
            type.Contains("application/xml") ||
            type.Contains("application/javascript") ||
            type.Contains("application/typescript"))
            return true;

        return false;
    }

    /// <summary>
    /// Get appropriate cache headers for content type
    /// </summary>
    /// <param name="contentType">MIME content type</param>
    /// <param name="duration">Cache duration</param>
    /// <returns>Dictionary of cache headers</returns>
    public static Dictionary<string, string> GetCacheHeaders(string contentType, TimeSpan duration)
    {
        var headers = new Dictionary<string, string>();

        // Images and static content can be cached longer
        if (contentType.StartsWith("image/") || contentType.StartsWith("video/") || contentType.StartsWith("audio/"))
        {
            headers["Cache-Control"] = $"public, max-age={Math.Max(3600, (int)duration.TotalSeconds)}, immutable";
        }
        else
        {
            headers["Cache-Control"] = $"public, max-age={Math.Min(3600, (int)duration.TotalSeconds)}";
        }

        headers["Accept-Ranges"] = "bytes";

        return headers;
    }
}
