using AutoMapper;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Application.Mappings;

/// <summary>
/// AutoMapper profile for permission entities and DTOs
/// </summary>
public class PermissionProfile : Profile
{
    public PermissionProfile()
    {
        CreateMap<FilePermission, FilePermissionDto>()
            .ForMember(dest => dest.UserName, opt => opt.Ignore()) // Will be populated by service
            .ForMember(dest => dest.RoleName, opt => opt.Ignore()) // Will be populated by service
            .ForMember(dest => dest.GrantedByName, opt => opt.Ignore()); // Will be populated by service

        CreateMap<FolderPermission, FolderPermissionDto>()
            .ForMember(dest => dest.UserName, opt => opt.Ignore()) // Will be populated by service
            .ForMember(dest => dest.RoleName, opt => opt.Ignore()) // Will be populated by service
            .ForMember(dest => dest.GrantedByName, opt => opt.Ignore()) // Will be populated by service
            .ForMember(dest => dest.IsInherited, opt => opt.Ignore()) // Will be determined by service
            .ForMember(dest => dest.InheritedFromFolderId, opt => opt.Ignore()); // Will be determined by service

        CreateMap<Domain.Entities.FileShare, FileShareDto>()
            .ForMember(dest => dest.HasPassword, opt => opt.MapFrom(src => !string.IsNullOrEmpty(src.PasswordHash)));

        CreateMap<SyncStatus, SyncStatusDto>()
            .ForMember(dest => dest.NextRetryAt, opt => opt.Ignore()) // Will be calculated by service
            .ForMember(dest => dest.AutoSyncEnabled, opt => opt.Ignore()) // Will be determined by service configuration
            .ForMember(dest => dest.Priority, opt => opt.Ignore()); // Will be determined by service
    }
} 