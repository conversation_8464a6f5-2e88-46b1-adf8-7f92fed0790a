using AutoMapper;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Application.Mappings;

/// <summary>
/// AutoMapper profile for Folder entity mappings
/// </summary>
public class FolderProfile : Profile
{
    public FolderProfile()
    {
        CreateMap<Folder, FolderDto>()
            .ForMember(dest => dest.FileCount, opt => opt.Ignore())
            .ForMember(dest => dest.SubfolderCount, opt => opt.Ignore())
            .ForMember(dest => dest.Permissions, opt => opt.Ignore());

        CreateMap<FolderDto, Folder>()
            .ForMember(dest => dest.Permissions, opt => opt.Ignore())
            .ForMember(dest => dest.SubFolders, opt => opt.Ignore())
            .ForMember(dest => dest.Files, opt => opt.Ignore());
    }
} 