using AutoMapper;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Application.Mappings;

/// <summary>
/// AutoMapper profile for FolderShare entity mappings
/// </summary>
public class FolderShareProfile : Profile
{
    public FolderShareProfile()
    {
        CreateMap<FolderShare, FolderShareDto>()
            .ForMember(dest => dest.ShareUrl, opt => opt.Ignore()) // Will be set in handlers
            .ReverseMap();
    }
} 