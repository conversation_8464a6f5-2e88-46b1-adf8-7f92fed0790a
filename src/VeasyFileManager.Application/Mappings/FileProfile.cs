using AutoMapper;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Application.Mappings;

/// <summary>
/// AutoMapper profile for File entity mappings
/// </summary>
public class FileProfile : Profile
{
    public FileProfile()
    {
        CreateMap<Domain.Entities.File, FileDto>()
            .ForMember(dest => dest.StorageProvider, opt => opt.MapFrom(src => src.StorageProvider.ToString()))
            .ForMember(dest => dest.Permissions, opt => opt.Ignore())
            .ForMember(dest => dest.IsShared, opt => opt.Ignore());

        CreateMap<FileDto, Domain.Entities.File>()
            .ForMember(dest => dest.StorageProvider, opt => opt.Ignore())
            .ForMember(dest => dest.Permissions, opt => opt.Ignore())
            .ForMember(dest => dest.Shares, opt => opt.Ignore());
    }
} 