using AutoMapper;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Application.Mappings;

/// <summary>
/// AutoMapper profile for deleted item mappings
/// </summary>
public class DeletedItemProfile : Profile
{
    public DeletedItemProfile()
    {
        CreateMap<DeletedItem, DeletedItemDto>()
            .ForMember(dest => dest.CanRestore, opt => opt.MapFrom(src => src.CanRestore))
            .ForMember(dest => dest.DaysRemaining, opt => opt.MapFrom(src => CalculateDaysRemaining(src.DeletedAt)))
            .ForMember(dest => dest.FormattedSize, opt => opt.Ignore()); // Calculated in DTO
    }

    private static int CalculateDaysRemaining(DateTime deletedAt)
    {
        var daysSinceDeletion = (DateTime.UtcNow - deletedAt).Days;
        var daysRemaining = 30 - daysSinceDeletion;
        return Math.Max(0, daysRemaining);
    }
}
