namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// Data transfer object for Folder entity
/// </summary>
public class FolderDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public Guid? ParentFolderId { get; set; }
    public Guid OwnerId { get; set; }
    public string Path { get; set; } = string.Empty;
    public int Level { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int FileCount { get; set; }
    public int SubfolderCount { get; set; }
    public List<string> Permissions { get; set; } = new();
} 