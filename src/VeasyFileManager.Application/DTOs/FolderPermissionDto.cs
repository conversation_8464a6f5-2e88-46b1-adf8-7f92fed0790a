using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// DTO for folder permission information
/// </summary>
public class FolderPermissionDto
{
    /// <summary>
    /// Permission ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Folder ID this permission applies to
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// User ID (null if role-based permission)
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// User name (for display purposes)
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// Role ID (null if user-specific permission)
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Role name (for display purposes)
    /// </summary>
    public string? RoleName { get; set; }

    /// <summary>
    /// Type of permission granted
    /// </summary>
    public PermissionType PermissionType { get; set; }

    /// <summary>
    /// User who granted this permission
    /// </summary>
    public Guid GrantedBy { get; set; }

    /// <summary>
    /// Name of user who granted this permission (for display purposes)
    /// </summary>
    public string? GrantedByName { get; set; }

    /// <summary>
    /// When the permission was granted
    /// </summary>
    public DateTime GrantedAt { get; set; }

    /// <summary>
    /// When the permission expires (if any)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Whether the permission is currently active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether this permission inherits to child folders and files
    /// </summary>
    public bool InheritToChildren { get; set; }

    /// <summary>
    /// Whether this permission is inherited from a parent folder
    /// </summary>
    public bool IsInherited { get; set; }

    /// <summary>
    /// ID of the parent folder this permission was inherited from (if inherited)
    /// </summary>
    public Guid? InheritedFromFolderId { get; set; }
} 