using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// Data transfer object for file permissions
/// </summary>
public class FilePermissionDto
{
    /// <summary>
    /// Permission ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// File ID this permission applies to
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// User ID who has the permission (null if role-based)
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// User name for display (if UserId is provided)
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// Role ID that has the permission (null if user-specific)
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Role name for display (if RoleId is provided)
    /// </summary>
    public string? RoleName { get; set; }

    /// <summary>
    /// Type of permission granted
    /// </summary>
    public PermissionType PermissionType { get; set; }

    /// <summary>
    /// User ID who granted this permission
    /// </summary>
    public Guid GrantedBy { get; set; }

    /// <summary>
    /// Name of user who granted the permission
    /// </summary>
    public string? GrantedByName { get; set; }

    /// <summary>
    /// When the permission was granted
    /// </summary>
    public DateTime GrantedAt { get; set; }

    /// <summary>
    /// When the permission expires (null if no expiration)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Whether the permission is currently active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the permission is expired
    /// </summary>
    public bool IsExpired { get; set; }
} 