namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// DTO for file download response
/// </summary>
public class FileDownloadDto
{
    /// <summary>
    /// File ID
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// File name for download
    /// </summary>
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// File MIME type
    /// </summary>
    public string ContentType { get; set; } = string.Empty;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// File content stream (if not using presigned URL)
    /// </summary>
    public Stream? ContentStream { get; set; }

    /// <summary>
    /// Presigned URL for direct download (if using presigned URL)
    /// </summary>
    public string? PresignedUrl { get; set; }

    /// <summary>
    /// URL expiration time (if using presigned URL)
    /// </summary>
    public DateTime? UrlExpiration { get; set; }

    /// <summary>
    /// Whether this is a presigned URL response
    /// </summary>
    public bool IsPresignedUrl => !string.IsNullOrEmpty(PresignedUrl);
} 