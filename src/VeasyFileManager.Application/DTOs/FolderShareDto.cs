using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// DTO for folder share information
/// </summary>
public class FolderShareDto
{
    /// <summary>
    /// Share ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Folder ID being shared
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// Share token for accessing the shared folder
    /// </summary>
    public string ShareToken { get; set; } = null!;

    /// <summary>
    /// User ID who created the share
    /// </summary>
    public Guid SharedBy { get; set; }

    /// <summary>
    /// Type of share (Public, Password, UserSpecific)
    /// </summary>
    public ShareType ShareType { get; set; }

    /// <summary>
    /// Expiration date of the share (if any)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Maximum number of downloads allowed (if any)
    /// </summary>
    public int? MaxDownloads { get; set; }

    /// <summary>
    /// Current download count
    /// </summary>
    public int DownloadCount { get; set; }

    /// <summary>
    /// Whether the share is active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether to include subfolders in the share
    /// </summary>
    public bool IncludeSubfolders { get; set; }

    /// <summary>
    /// When the share was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Share URL for easy access
    /// </summary>
    public string ShareUrl { get; set; } = null!;

    /// <summary>
    /// Whether the share is expired
    /// </summary>
    public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;

    /// <summary>
    /// Whether the share has reached download limit
    /// </summary>
    public bool HasReachedDownloadLimit => MaxDownloads.HasValue && DownloadCount >= MaxDownloads.Value;
} 