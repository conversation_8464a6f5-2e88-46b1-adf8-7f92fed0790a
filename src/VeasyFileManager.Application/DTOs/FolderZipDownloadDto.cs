namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// DTO for folder ZIP download response
/// </summary>
public class FolderZipDownloadDto
{
    /// <summary>
    /// ZIP file stream
    /// </summary>
    public Stream ZipStream { get; set; } = null!;

    /// <summary>
    /// Suggested filename for the ZIP
    /// </summary>
    public string FileName { get; set; } = null!;

    /// <summary>
    /// Total size of the ZIP file in bytes
    /// </summary>
    public long TotalSize { get; set; }

    /// <summary>
    /// Number of files included in the ZIP
    /// </summary>
    public int FileCount { get; set; }

    /// <summary>
    /// Content type (should be application/zip)
    /// </summary>
    public string ContentType { get; set; } = "application/zip";

    /// <summary>
    /// Folder name being downloaded
    /// </summary>
    public string FolderName { get; set; } = null!;
} 