namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// DTO for Google Drive file information
/// </summary>
public class GoogleDriveFileDto
{
    /// <summary>
    /// Google Drive file ID
    /// </summary>
    public string Id { get; set; } = null!;

    /// <summary>
    /// File name
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// MIME type
    /// </summary>
    public string MimeType { get; set; } = null!;

    /// <summary>
    /// File size in bytes
    /// </summary>
    public long? Size { get; set; }

    /// <summary>
    /// Creation time
    /// </summary>
    public DateTime CreatedTime { get; set; }

    /// <summary>
    /// Last modified time
    /// </summary>
    public DateTime ModifiedTime { get; set; }

    /// <summary>
    /// Parent folder IDs
    /// </summary>
    public List<string> Parents { get; set; } = new();

    /// <summary>
    /// Whether this is a folder
    /// </summary>
    public bool IsFolder { get; set; }

    /// <summary>
    /// File description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Download URL
    /// </summary>
    public string? DownloadUrl { get; set; }

    /// <summary>
    /// MD5 checksum
    /// </summary>
    public string? Md5Checksum { get; set; }
} 