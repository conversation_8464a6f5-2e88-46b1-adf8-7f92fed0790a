namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// Data transfer object for File entity
/// </summary>
public class FileDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string DisplayName { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string MimeType { get; set; } = string.Empty;
    public string? FilePath { get; set; }
    public string? HashMd5 { get; set; }
    public string? HashSha256 { get; set; }
    public string StorageProvider { get; set; } = string.Empty;
    public string? ExternalId { get; set; }
    public Guid? ParentFolderId { get; set; }
    public Guid OwnerId { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int Version { get; set; }
    public List<string> Permissions { get; set; } = new();
    public bool IsShared { get; set; }
} 