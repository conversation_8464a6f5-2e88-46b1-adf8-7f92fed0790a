using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// DTO for file share information
/// </summary>
public class FileShareDto
{
    /// <summary>
    /// Share ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// File ID being shared
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// Share token for accessing the file
    /// </summary>
    public string ShareToken { get; set; } = null!;

    /// <summary>
    /// Type of share
    /// </summary>
    public ShareType ShareType { get; set; }

    /// <summary>
    /// User who created the share
    /// </summary>
    public Guid SharedBy { get; set; }

    /// <summary>
    /// When the share was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// When the share expires (if any)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Maximum number of downloads allowed
    /// </summary>
    public int? MaxDownloads { get; set; }

    /// <summary>
    /// Current download count
    /// </summary>
    public int DownloadCount { get; set; }

    /// <summary>
    /// Whether the share is currently active
    /// </summary>
    public bool IsActive { get; set; }

    /// <summary>
    /// Whether the share has a password
    /// </summary>
    public bool HasPassword { get; set; }
} 