using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// DTO for sync status information
/// </summary>
public class SyncStatusDto
{
    /// <summary>
    /// Sync status record ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// File ID being synced
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// Storage provider/service being synced with
    /// </summary>
    public string Provider { get; set; } = null!;

    /// <summary>
    /// External ID in the target system (e.g., Google Drive file ID)
    /// </summary>
    public string? ExternalId { get; set; }

    /// <summary>
    /// Last successful sync timestamp
    /// </summary>
    public DateTime? LastSyncAt { get; set; }

    /// <summary>
    /// Current sync status
    /// </summary>
    public SyncStatus SyncStatus { get; set; }

    /// <summary>
    /// Error message if sync failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// Next retry attempt timestamp (if applicable)
    /// </summary>
    public DateTime? NextRetryAt { get; set; }

    /// <summary>
    /// Whether automatic sync is enabled for this file
    /// </summary>
    public bool AutoSyncEnabled { get; set; }

    /// <summary>
    /// Priority level for sync operations (higher = more priority)
    /// </summary>
    public int Priority { get; set; }
} 