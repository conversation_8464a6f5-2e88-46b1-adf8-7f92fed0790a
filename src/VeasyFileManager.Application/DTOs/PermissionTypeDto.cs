using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// DTO for permission type information
/// </summary>
public class PermissionTypeDto
{
    /// <summary>
    /// Permission type value
    /// </summary>
    public PermissionType Value { get; set; }

    /// <summary>
    /// Display name for the permission
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Description of what this permission allows
    /// </summary>
    public string Description { get; set; } = null!;

    /// <summary>
    /// Whether this permission can be applied to files
    /// </summary>
    public bool ApplicableToFiles { get; set; }

    /// <summary>
    /// Whether this permission can be applied to folders
    /// </summary>
    public bool ApplicableToFolders { get; set; }

    /// <summary>
    /// Sort order for display
    /// </summary>
    public int SortOrder { get; set; }
} 