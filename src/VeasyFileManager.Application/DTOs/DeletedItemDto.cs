using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.DTOs;

/// <summary>
/// Data transfer object for deleted items (Thùng rác)
/// </summary>
public class DeletedItemDto
{
    /// <summary>
    /// Deleted item ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Original ID of the deleted item
    /// </summary>
    public Guid OriginalId { get; set; }

    /// <summary>
    /// Type of item (Folder or File)
    /// </summary>
    public DeletedItemType ItemType { get; set; }

    /// <summary>
    /// Display name for item type in Vietnamese
    /// </summary>
    public string ItemTypeDisplay => ItemType == DeletedItemType.Folder ? "Thư mục" : "Tệp tin";

    /// <summary>
    /// Icon class for the item type
    /// </summary>
    public string ItemIcon => ItemType == DeletedItemType.Folder ? "folder" : GetFileIcon();

    /// <summary>
    /// Original name of the item
    /// </summary>
    public string OriginalName { get; set; } = string.Empty;

    /// <summary>
    /// Original path of the item
    /// </summary>
    public string OriginalPath { get; set; } = string.Empty;

    /// <summary>
    /// Display path (shortened for UI)
    /// </summary>
    public string DisplayPath => OriginalPath.Length > 50 ? "..." + OriginalPath.Substring(OriginalPath.Length - 47) : OriginalPath;

    /// <summary>
    /// Parent folder ID (if any)
    /// </summary>
    public Guid? ParentFolderId { get; set; }

    /// <summary>
    /// User who deleted the item
    /// </summary>
    public Guid DeletedBy { get; set; }

    /// <summary>
    /// Email of user who deleted the item (for display)
    /// </summary>
    public string DeletedByEmail { get; set; } = string.Empty;

    /// <summary>
    /// When the item was deleted
    /// </summary>
    public DateTime DeletedAt { get; set; }

    /// <summary>
    /// Formatted deletion time for Vietnamese UI
    /// </summary>
    public string DeletedAtDisplay => DeletedAt.ToString("dd/MM/yyyy HH:mm");

    /// <summary>
    /// When the item was restored (if restored)
    /// </summary>
    public DateTime? RestoredAt { get; set; }

    /// <summary>
    /// User who restored the item (if restored)
    /// </summary>
    public Guid? RestoredBy { get; set; }

    /// <summary>
    /// Whether the item can still be restored
    /// </summary>
    public bool CanRestore { get; set; }

    /// <summary>
    /// Days remaining until permanent deletion
    /// </summary>
    public int DaysRemaining { get; set; }

    /// <summary>
    /// Status display for UI
    /// </summary>
    public string StatusDisplay => CanRestore ? $"Còn {DaysRemaining} ngày" : "Hết hạn";

    /// <summary>
    /// Status CSS class
    /// </summary>
    public string StatusClass => CanRestore ? (DaysRemaining <= 3 ? "warning" : "success") : "danger";

    /// <summary>
    /// Original file size (for files only)
    /// </summary>
    public long? OriginalSize { get; set; }

    /// <summary>
    /// Original content type (for files only)
    /// </summary>
    public string? OriginalContentType { get; set; }

    /// <summary>
    /// Human-readable file size
    /// </summary>
    public string? FormattedSize => OriginalSize.HasValue ? FormatFileSize(OriginalSize.Value) : "--";

    /// <summary>
    /// Size display for UI (shows -- for folders)
    /// </summary>
    public string SizeDisplay => ItemType == DeletedItemType.File ? (FormattedSize ?? "--") : "--";

    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private string GetFileIcon()
    {
        if (string.IsNullOrEmpty(OriginalContentType))
            return "file";

        return OriginalContentType.ToLower() switch
        {
            var ct when ct.StartsWith("image/") => "image",
            var ct when ct.StartsWith("video/") => "video",
            var ct when ct.StartsWith("audio/") => "audio",
            var ct when ct.Contains("pdf") => "pdf",
            var ct when ct.Contains("word") || ct.Contains("document") => "document",
            var ct when ct.Contains("excel") || ct.Contains("sheet") => "spreadsheet",
            var ct when ct.Contains("powerpoint") || ct.Contains("presentation") => "presentation",
            var ct when ct.Contains("zip") || ct.Contains("rar") || ct.Contains("archive") => "archive",
            _ => "file"
        };
    }
}
