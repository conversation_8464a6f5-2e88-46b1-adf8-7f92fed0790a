using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.Interfaces;

/// <summary>
/// Repository interface for File entity with file-specific operations
/// </summary>
public interface IFileRepository : IRepository<Domain.Entities.File>
{
    /// <summary>
    /// Get user files with pagination and filtering
    /// </summary>
    Task<(IEnumerable<Domain.Entities.File> Files, int TotalCount)> GetUserFilesAsync(
        Guid userId,
        Guid? parentFolderId = null,
        int page = 1,
        int pageSize = 20,
        string? searchTerm = null,
        string sortBy = "CreatedAt",
        string sortDirection = "DESC",
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file by MD5 hash
    /// </summary>
    Task<Domain.Entities.File?> GetByHashAsync(string md5Hash, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get files in a specific folder
    /// </summary>
    Task<IEnumerable<Domain.Entities.File>> GetByParentFolderAsync(Guid parentFolderId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if user has specific access to a file
    /// </summary>
    Task<bool> HasUserAccessAsync(Guid userId, Guid fileId, PermissionType permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get files with specific permission for user
    /// </summary>
    Task<IEnumerable<Domain.Entities.File>> GetFilesWithPermissionAsync(Guid userId, PermissionType permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get files by owner
    /// </summary>
    Task<IEnumerable<Domain.Entities.File>> GetByOwnerAsync(Guid ownerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Search files by name or content
    /// </summary>
    Task<(IEnumerable<Domain.Entities.File> Files, int TotalCount)> SearchFilesAsync(
        Guid userId,
        string searchTerm,
        int page = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get files by MIME type
    /// </summary>
    Task<IEnumerable<Domain.Entities.File>> GetByMimeTypeAsync(string mimeType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get recently modified files for user
    /// </summary>
    Task<IEnumerable<Domain.Entities.File>> GetRecentlyModifiedAsync(Guid userId, int count = 10, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file with its permissions and shares
    /// </summary>
    Task<Domain.Entities.File?> GetWithPermissionsAndSharesAsync(Guid fileId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file share by token
    /// </summary>
    Task<Domain.Entities.FileShare?> GetFileShareByTokenAsync(string shareToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all shares for a file
    /// </summary>
    Task<List<Domain.Entities.FileShare>> GetFileSharesAsync(Guid fileId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Add a new file share
    /// </summary>
    Task<Domain.Entities.FileShare> AddFileShareAsync(Domain.Entities.FileShare fileShare, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update file share
    /// </summary>
    Task UpdateFileShareAsync(Domain.Entities.FileShare fileShare, CancellationToken cancellationToken = default);
} 