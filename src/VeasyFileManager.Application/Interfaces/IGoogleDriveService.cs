using VeasyFileManager.Application.DTOs;

namespace VeasyFileManager.Application.Interfaces;

/// <summary>
/// Service interface for Google Drive operations
/// </summary>
public interface IGoogleDriveService
{
    /// <summary>
    /// Upload a file to Google Drive
    /// </summary>
    /// <param name="fileStream">File stream to upload</param>
    /// <param name="fileName">Name of the file</param>
    /// <param name="parentFolderId">Parent folder ID in Google Drive</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Google Drive file ID</returns>
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string? parentFolderId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Download a file from Google Drive
    /// </summary>
    /// <param name="fileId">Google Drive file ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File stream</returns>
    Task<Stream> DownloadFileAsync(string fileId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete a file from Google Drive
    /// </summary>
    /// <param name="fileId">Google Drive file ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task DeleteFileAsync(string fileId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file metadata from Google Drive
    /// </summary>
    /// <param name="fileId">Google Drive file ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Google Drive file DTO</returns>
    Task<GoogleDriveFileDto> GetFileAsync(string fileId, CancellationToken cancellationToken = default);

    /// <summary>
    /// List files from Google Drive
    /// </summary>
    /// <param name="parentFolderId">Parent folder ID to list files from</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of Google Drive files</returns>
    Task<List<GoogleDriveFileDto>> ListFilesAsync(string? parentFolderId = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync user files with Google Drive
    /// </summary>
    /// <param name="userId">User ID to sync files for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task SyncUserFilesAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Move a file in Google Drive
    /// </summary>
    /// <param name="fileId">Google Drive file ID</param>
    /// <param name="newParentId">New parent folder ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Updated Google Drive file ID</returns>
    Task<string> MoveFileAsync(string fileId, string newParentId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a folder in Google Drive
    /// </summary>
    /// <param name="name">Folder name</param>
    /// <param name="parentId">Parent folder ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created Google Drive folder DTO</returns>
    Task<GoogleDriveFileDto> CreateFolderAsync(string name, string? parentId = null, CancellationToken cancellationToken = default);
} 