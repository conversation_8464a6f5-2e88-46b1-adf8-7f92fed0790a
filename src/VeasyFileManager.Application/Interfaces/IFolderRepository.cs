using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.Interfaces;

/// <summary>
/// Repository interface for Folder entity with folder-specific operations
/// </summary>
public interface IFolderRepository : IRepository<Domain.Entities.Folder>
{
    /// <summary>
    /// Get user folders with pagination and filtering
    /// </summary>
    Task<(IEnumerable<Domain.Entities.Folder> Folders, int TotalCount)> GetUserFoldersAsync(
        Guid userId,
        Guid? parentFolderId = null,
        int page = 1,
        int pageSize = 20,
        string? searchTerm = null,
        string sortBy = "CreatedAt",
        string sortDirection = "DESC",
        string? uploaderEmail = null,
        string? folderType = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        bool includeShared = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get folder by path
    /// </summary>
    Task<Domain.Entities.Folder?> GetByPathAsync(string path, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get folder hierarchy (all children and subchildren)
    /// </summary>
    Task<IEnumerable<Domain.Entities.Folder>> GetFolderHierarchyAsync(Guid folderId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if user has specific access to a folder
    /// </summary>
    Task<bool> HasUserAccessAsync(Guid folderId, Guid userId, PermissionType permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get folders with specific permission for user
    /// </summary>
    Task<IEnumerable<Domain.Entities.Folder>> GetFoldersWithPermissionAsync(Guid userId, PermissionType permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get folders by owner
    /// </summary>
    Task<IEnumerable<Domain.Entities.Folder>> GetByOwnerAsync(Guid ownerId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get root folders for user (folders without parent)
    /// </summary>
    Task<IEnumerable<Domain.Entities.Folder>> GetRootFoldersAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get subfolders of a parent folder
    /// </summary>
    Task<IEnumerable<Domain.Entities.Folder>> GetSubfoldersAsync(Guid parentFolderId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get folder with its permissions
    /// </summary>
    Task<Domain.Entities.Folder?> GetWithPermissionsAsync(Guid folderId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if folder path is unique for user
    /// </summary>
    Task<bool> IsPathUniqueAsync(string path, Guid userId, Guid? excludeFolderId = null, CancellationToken cancellationToken = default);
}
