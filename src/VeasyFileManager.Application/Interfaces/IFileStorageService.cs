namespace VeasyFileManager.Application.Interfaces;

/// <summary>
/// Interface for file storage operations
/// </summary>
public interface IFileStorageService
{
    /// <summary>
    /// Upload a file to storage
    /// </summary>
    /// <param name="fileStream">File stream to upload</param>
    /// <param name="fileName">Name of the file</param>
    /// <param name="contentType">Content type of the file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File path in storage</returns>
    Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Download a file from storage
    /// </summary>
    /// <param name="filePath">Path to the file in storage</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File stream</returns>
    Task<Stream> DownloadFileAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Delete a file from storage - DISABLED FOR SAFETY
    /// This method is intentionally disabled to prevent any file deletion on R2 storage
    /// Files on R2 should never be deleted to ensure data preservation
    /// </summary>
    /// <param name="filePath">Path to the file in storage</param>
    /// <param name="cancellationToken">Cancellation token</param>
    // Task DeleteFileAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Copy a file within storage
    /// </summary>
    /// <param name="sourceFilePath">Source file path</param>
    /// <param name="destinationFilePath">Destination file path</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Destination file path</returns>
    Task<string> CopyFileAsync(string sourceFilePath, string destinationFilePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if a file exists in storage
    /// </summary>
    /// <param name="filePath">Path to the file in storage</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if file exists</returns>
    Task<bool> FileExistsAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate a presigned URL for file access
    /// </summary>
    /// <param name="filePath">Path to the file in storage</param>
    /// <param name="expiration">URL expiration time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Presigned URL</returns>
    Task<string> GeneratePresignedUrlAsync(string filePath, TimeSpan expiration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file metadata without downloading the content
    /// </summary>
    /// <param name="filePath">Path to the file in storage</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File metadata</returns>
    Task<StorageFileMetadata> GetFileMetadataAsync(string filePath, CancellationToken cancellationToken = default);

    /// <summary>
    /// Download a partial range of a file from storage
    /// </summary>
    /// <param name="filePath">Path to the file in storage</param>
    /// <param name="rangeStart">Start byte position (inclusive)</param>
    /// <param name="rangeEnd">End byte position (inclusive), null for end of file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Partial file stream and actual range information</returns>
    Task<PartialFileStream> DownloadFileRangeAsync(string filePath, long rangeStart, long? rangeEnd = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get file content for streaming with optional range support
    /// </summary>
    /// <param name="filePath">Path to the file in storage</param>
    /// <param name="range">Byte range for partial content, null for full file</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>File stream response</returns>
    Task<FileStreamResponse> GetFileStreamAsync(string filePath, ByteRange? range = null, CancellationToken cancellationToken = default);
}

/// <summary>
/// Metadata for a file in storage
/// </summary>
public class StorageFileMetadata
{
    public string FilePath { get; set; } = string.Empty;
    public long FileSize { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public DateTime LastModified { get; set; }
    public string ETag { get; set; } = string.Empty;
    public string OriginalFileName { get; set; } = string.Empty;
    public DateTime? UploadTimestamp { get; set; }
}

/// <summary>
/// Represents a byte range for partial file requests
/// </summary>
public class ByteRange
{
    public long Start { get; set; }
    public long? End { get; set; }

    /// <summary>
    /// Calculate the actual end position given the file size
    /// </summary>
    public long GetActualEnd(long fileSize) => End ?? fileSize - 1;

    /// <summary>
    /// Calculate the content length for this range
    /// </summary>
    public long GetContentLength(long fileSize) => GetActualEnd(fileSize) - Start + 1;

    /// <summary>
    /// Validate if the range is valid for the given file size
    /// </summary>
    public bool IsValid(long fileSize) => Start >= 0 && Start < fileSize && GetActualEnd(fileSize) < fileSize;
}

/// <summary>
/// Response for partial file downloads with range information
/// </summary>
public class PartialFileStream : IDisposable
{
    public Stream Stream { get; set; } = null!;
    public long RangeStart { get; set; }
    public long RangeEnd { get; set; }
    public long ContentLength { get; set; }
    public long TotalFileSize { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public string? ETag { get; set; }

    public void Dispose()
    {
        Stream?.Dispose();
    }
}

/// <summary>
/// Complete file stream response with metadata
/// </summary>
public class FileStreamResponse : IDisposable
{
    public Stream Stream { get; set; } = null!;
    public StorageFileMetadata Metadata { get; set; } = null!;
    public bool IsPartialContent { get; set; }
    public ByteRange? Range { get; set; }
    public long ContentLength { get; set; }

    public void Dispose()
    {
        Stream?.Dispose();
    }
}
