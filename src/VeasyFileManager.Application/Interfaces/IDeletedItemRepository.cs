using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.Interfaces;

/// <summary>
/// Repository interface for managing deleted items (recycle bin)
/// </summary>
public interface IDeletedItemRepository : IRepository<DeletedItem>
{
    /// <summary>
    /// Get deleted items for a user with pagination and filtering
    /// </summary>
    Task<(IEnumerable<DeletedItem> Items, int TotalCount)> GetDeletedItemsAsync(
        Guid userId,
        int page = 1,
        int pageSize = 20,
        DeletedItemType? itemType = null,
        string? searchTerm = null,
        string? uploaderEmail = null,
        DateTime? deletedAfter = null,
        DateTime? deletedBefore = null,
        bool onlyRestorable = true,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get deleted item by original ID
    /// </summary>
    Task<DeletedItem?> GetByOriginalIdAsync(
        Guid originalId,
        DeletedItemType itemType,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get deleted items that should be permanently deleted (older than 30 days)
    /// </summary>
    Task<IEnumerable<DeletedItem>> GetItemsForPermanentDeletionAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get restoration statistics for a user
    /// </summary>
    Task<object> GetRestorationStatisticsAsync(
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if an item can be restored
    /// </summary>
    Task<bool> CanRestoreItemAsync(
        Guid deletedItemId,
        Guid userId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get deleted items by parent folder
    /// </summary>
    Task<IEnumerable<DeletedItem>> GetByParentFolderAsync(
        Guid parentFolderId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Bulk delete expired items
    /// </summary>
    Task<int> DeleteExpiredItemsAsync(CancellationToken cancellationToken = default);
}
