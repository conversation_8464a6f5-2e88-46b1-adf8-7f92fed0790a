namespace VeasyFileManager.Application.Interfaces;

/// <summary>
/// Service for accessing current authenticated user information
/// </summary>
public interface ICurrentUserService
{
    /// <summary>
    /// Gets the current user's ID
    /// </summary>
    Guid? UserId { get; }

    /// <summary>
    /// Gets the current user's email
    /// </summary>
    string? Email { get; }

    /// <summary>
    /// Gets the current user's name
    /// </summary>
    string? Name { get; }

    /// <summary>
    /// Gets all claims for the current user
    /// </summary>
    IEnumerable<KeyValuePair<string, string>> Claims { get; }

    /// <summary>
    /// Checks if the user is authenticated
    /// </summary>
    bool IsAuthenticated { get; }

    /// <summary>
    /// Gets a specific claim value
    /// </summary>
    /// <param name="claimType">The claim type</param>
    /// <returns>The claim value or null if not found</returns>
    string? GetClaim(string claimType);
} 