namespace VeasyFileManager.Application.Interfaces;

/// <summary>
/// Repository interface for SyncStatus entity operations
/// </summary>
public interface ISyncStatusRepository : IRepository<Domain.Entities.SyncStatus>
{
    /// <summary>
    /// Get sync statuses for a specific user
    /// </summary>
    Task<List<Domain.Entities.SyncStatus>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get sync status for a specific file
    /// </summary>
    Task<Domain.Entities.SyncStatus?> GetByFileIdAsync(Guid fileId, string provider, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get sync statuses by provider
    /// </summary>
    Task<List<Domain.Entities.SyncStatus>> GetByProviderAsync(string provider, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get sync statuses by status
    /// </summary>
    Task<List<Domain.Entities.SyncStatus>> GetByStatusAsync(Domain.Enums.SyncStatus status, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get sync status summary for a user
    /// </summary>
    Task<(int Total, int Synced, int Pending, int Failed, int Syncing)> GetStatusSummaryAsync(
        Guid userId, 
        Guid? fileId = null, 
        string? provider = null, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get last successful sync time for a user
    /// </summary>
    Task<DateTime?> GetLastSyncTimeAsync(Guid userId, string? provider = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create or update sync status
    /// </summary>
    Task<Domain.Entities.SyncStatus> UpsertAsync(Guid fileId, string provider, string? externalId = null, CancellationToken cancellationToken = default);
} 