using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.Interfaces;

/// <summary>
/// Service for managing file permissions
/// </summary>
public interface IFilePermissionService
{
    /// <summary>
    /// Check if a user has specific permission on a file
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fileId">File ID</param>
    /// <param name="permission">Permission type to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user has the permission</returns>
    Task<bool> HasPermissionAsync(Guid userId, Guid fileId, PermissionType permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// Check if a user has specific permission on a folder
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="folderId">Folder ID</param>
    /// <param name="permission">Permission type to check</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if user has the permission</returns>
    Task<bool> HasFolderPermissionAsync(Guid userId, Guid folderId, PermissionType permission, CancellationToken cancellationToken = default);

    /// <summary>
    /// Grant permission to a user for a file
    /// </summary>
    /// <param name="fileId">File ID</param>
    /// <param name="userId">User ID to grant permission to</param>
    /// <param name="roleId">Role ID to grant permission to (alternative to userId)</param>
    /// <param name="permission">Permission type to grant</param>
    /// <param name="grantedBy">User ID who is granting the permission</param>
    /// <param name="expiresAt">Optional expiration date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Permission ID</returns>
    Task<Guid> GrantFilePermissionAsync(Guid fileId, Guid? userId, Guid? roleId, PermissionType permission, Guid grantedBy, DateTime? expiresAt = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Grant permission to a user for a folder
    /// </summary>
    /// <param name="folderId">Folder ID</param>
    /// <param name="userId">User ID to grant permission to</param>
    /// <param name="roleId">Role ID to grant permission to (alternative to userId)</param>
    /// <param name="permission">Permission type to grant</param>
    /// <param name="grantedBy">User ID who is granting the permission</param>
    /// <param name="inheritToChildren">Whether permission should inherit to child folders/files</param>
    /// <param name="expiresAt">Optional expiration date</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Permission ID</returns>
    Task<Guid> GrantFolderPermissionAsync(Guid folderId, Guid? userId, Guid? roleId, PermissionType permission, Guid grantedBy, bool inheritToChildren = true, DateTime? expiresAt = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Revoke a specific permission
    /// </summary>
    /// <param name="permissionId">Permission ID to revoke</param>
    /// <param name="revokedBy">User ID who is revoking the permission</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if permission was revoked</returns>
    Task<bool> RevokePermissionAsync(Guid permissionId, Guid revokedBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all permissions for a file
    /// </summary>
    /// <param name="fileId">File ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of file permissions</returns>
    Task<List<Domain.Entities.FilePermission>> GetFilePermissionsAsync(Guid fileId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all permissions for a folder
    /// </summary>
    /// <param name="folderId">Folder ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of folder permissions</returns>
    Task<List<Domain.Entities.FolderPermission>> GetFolderPermissionsAsync(Guid folderId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get effective permissions for a user on a file (including inherited permissions)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="fileId">File ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of effective permission types</returns>
    Task<List<PermissionType>> GetUserFilePermissionsAsync(Guid userId, Guid fileId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get effective permissions for a user on a folder (including inherited permissions)
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="folderId">Folder ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of effective permission types</returns>
    Task<List<PermissionType>> GetUserFolderPermissionsAsync(Guid userId, Guid folderId, CancellationToken cancellationToken = default);
} 