using VeasyFileManager.Application.DTOs;

namespace VeasyFileManager.Application.Interfaces;

/// <summary>
/// Service interface for file sharing operations
/// </summary>
public interface IFileSharingService
{
    /// <summary>
    /// Create a share link for a file
    /// </summary>
    /// <param name="fileId">File ID to share</param>
    /// <param name="request">Share creation request</param>
    /// <param name="createdBy">User creating the share</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Created file share DTO</returns>
    Task<FileShareDto> CreateShareAsync(Guid fileId, CreateShareRequest request, Guid createdBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get a shared file by token
    /// </summary>
    /// <param name="shareToken">Share token</param>
    /// <param name="password">Password if required</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Shared file DTO</returns>
    Task<FileDto> GetSharedFileAsync(string shareToken, string? password = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Disable a share link
    /// </summary>
    /// <param name="shareToken">Share token to disable</param>
    /// <param name="disabledBy">User disabling the share</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task DisableShareAsync(string shareToken, Guid disabledBy, CancellationToken cancellationToken = default);

    /// <summary>
    /// Increment download count for a share
    /// </summary>
    /// <param name="shareToken">Share token</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task IncrementDownloadCountAsync(string shareToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all shares for a file
    /// </summary>
    /// <param name="fileId">File ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of file shares</returns>
    Task<List<FileShareDto>> GetFileSharesAsync(Guid fileId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Request model for creating file shares
/// </summary>
public class CreateShareRequest
{
    /// <summary>
    /// Type of share (Public, Password, UserSpecific)
    /// </summary>
    public Domain.Enums.ShareType ShareType { get; set; }

    /// <summary>
    /// Password for password-protected shares
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Expiration date for the share
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Maximum number of downloads allowed
    /// </summary>
    public int? MaxDownloads { get; set; }
} 