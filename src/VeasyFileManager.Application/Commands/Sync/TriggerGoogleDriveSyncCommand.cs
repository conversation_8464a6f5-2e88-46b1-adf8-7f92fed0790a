using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Sync;

/// <summary>
/// Command to trigger Google Drive synchronization
/// </summary>
public class TriggerGoogleDriveSyncCommand : IRequest<BaseResponseModel<SyncJobDto>>
{
    /// <summary>
    /// User ID to sync files for
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Specific file ID to sync (null for all files)
    /// </summary>
    public Guid? FileId { get; set; }

    /// <summary>
    /// Force sync even if files appear to be up to date
    /// </summary>
    public bool ForceSync { get; set; }
}

/// <summary>
/// Response DTO for sync job information
/// </summary>
public class SyncJobDto
{
    /// <summary>
    /// Sync job ID
    /// </summary>
    public Guid JobId { get; set; }

    /// <summary>
    /// Current status of the sync job
    /// </summary>
    public string Status { get; set; } = "queued";

    /// <summary>
    /// Message describing the sync operation
    /// </summary>
    public string Message { get; set; } = null!;

    /// <summary>
    /// When the sync was started
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// Number of files to be synced
    /// </summary>
    public int? TotalFiles { get; set; }
} 