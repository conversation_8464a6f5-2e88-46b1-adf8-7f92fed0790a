using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Sync;

/// <summary>
/// Handler for triggering Google Drive synchronization
/// </summary>
public class TriggerGoogleDriveSyncCommandHandler(
    IGoogleDriveService googleDriveService,
    IFileRepository fileRepository,
    ILogger<TriggerGoogleDriveSyncCommandHandler> logger) : IRequestHandler<TriggerGoogleDriveSyncCommand, BaseResponseModel<SyncJobDto>>
{
    public async Task<BaseResponseModel<SyncJobDto>> Handle(TriggerGoogleDriveSyncCommand request, CancellationToken cancellationToken)
    {
        var jobId = Guid.NewGuid();
        logger.LogInformation("Triggering Google Drive sync for user {UserId}, FileId: {FileId}, Force: {ForceSync}, JobId: {JobId}",
            request.UserId, request.FileId, request.ForceSync, jobId);

        try
        {
            var syncJob = new SyncJobDto
            {
                JobId = jobId,
                Status = "queued",
                Message = request.FileId.HasValue 
                    ? $"Sync queued for specific file {request.FileId}"
                    : "Sync queued for all user files",
                StartedAt = DateTime.UtcNow
            };

            if (request.FileId.HasValue)
            {
                // Sync specific file
                var file = await fileRepository.GetByIdAsync(request.FileId.Value, cancellationToken);
                if (file == null)
                {
                    return BaseResponseModel<SyncJobDto>.CreateError($"File with ID {request.FileId} not found", 404, "FILE_NOT_FOUND");
                }

                if (file.OwnerId != request.UserId)
                {
                    return BaseResponseModel<SyncJobDto>.CreateError("You don't have permission to sync this file", 403, "PERMISSION_DENIED");
                }

                syncJob.TotalFiles = 1;
                syncJob.Status = "syncing";
                syncJob.Message = $"Syncing file: {file.Name}";

                // Trigger background sync for specific file
                // Note: In a real implementation, this would queue a background job
                // For now, we'll call the sync service directly
                await googleDriveService.SyncUserFilesAsync(request.UserId, cancellationToken);
                
                syncJob.Status = "completed";
                syncJob.Message = $"Successfully synced file: {file.Name}";
            }
            else
            {
                // Sync all user files
                var (userFiles, totalCount) = await fileRepository.GetUserFilesAsync(
                    request.UserId, 
                    cancellationToken: cancellationToken);

                syncJob.TotalFiles = totalCount;
                syncJob.Status = "syncing";
                syncJob.Message = $"Syncing {totalCount} files";

                // Trigger background sync for all files
                // Note: In a real implementation, this would queue a background job
                await googleDriveService.SyncUserFilesAsync(request.UserId, cancellationToken);
                
                syncJob.Status = "completed";
                syncJob.Message = $"Successfully synced {totalCount} files";
            }

            logger.LogInformation("Google Drive sync completed for user {UserId}, JobId: {JobId}", 
                request.UserId, jobId);

            return BaseResponseModel<SyncJobDto>.CreateSuccess(syncJob, "Sync operation completed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during Google Drive sync for user {UserId}, JobId: {JobId}", 
                request.UserId, jobId);

            var failedJob = new SyncJobDto
            {
                JobId = jobId,
                Status = "failed",
                Message = $"Sync failed: {ex.Message}",
                StartedAt = DateTime.UtcNow
            };

            return BaseResponseModel<SyncJobDto>.CreateError("Sync operation failed", failedJob, 500, "SYNC_FAILED");
        }
    }
} 