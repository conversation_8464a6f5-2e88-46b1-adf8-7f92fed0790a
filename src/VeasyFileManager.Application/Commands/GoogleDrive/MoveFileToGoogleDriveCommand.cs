using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.GoogleDrive;

/// <summary>
/// Command to move a file to/from Google Drive
/// </summary>
public class MoveFileToGoogleDriveCommand : IRequest<BaseResponseModel<Unit>>
{
    /// <summary>
    /// User ID performing the move
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// File ID to move
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// Move direction: "to-gdrive" or "from-gdrive"
    /// </summary>
    public string Direction { get; set; } = null!;

    /// <summary>
    /// Target folder ID in Google Drive (for to-gdrive direction)
    /// </summary>
    public string? TargetFolderId { get; set; }
}
