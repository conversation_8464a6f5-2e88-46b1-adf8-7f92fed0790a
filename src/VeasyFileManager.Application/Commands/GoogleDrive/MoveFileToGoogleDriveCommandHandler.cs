using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.GoogleDrive;

/// <summary>
/// Handler for moving files to/from Google Drive
/// </summary>
public class MoveFileToGoogleDriveCommandHandler(
    IGoogleDriveService googleDriveService,
    IFileRepository fileRepository,
    ISyncStatusRepository syncStatusRepository,
    ILogger<MoveFileToGoogleDriveCommandHandler> logger) : IRequestHandler<MoveFileToGoogleDriveCommand, BaseResponseModel<Unit>>
{
    public async Task<BaseResponseModel<Unit>> Handle(MoveFileToGoogleDriveCommand request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Moving file {FileId} {Direction} Google Drive for user {UserId}",
            request.FileId, request.Direction, request.UserId);

        try
        {
            // Get the file
            var file = await fileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (file == null)
            {
                return BaseResponseModel<Unit>.CreateError($"File with ID {request.FileId} not found", 404, "FILE_NOT_FOUND");
            }

            // Check if user has permission to move the file
            if (file.OwnerId != request.UserId)
            {
                return BaseResponseModel<Unit>.CreateError("You don't have permission to move this file", 403, "PERMISSION_DENIED");
            }

            switch (request.Direction.ToLower())
            {
                case "to-gdrive":
                    await MoveToGoogleDrive(file, request.TargetFolderId, cancellationToken);
                    break;
                
                case "from-gdrive":
                    await MoveFromGoogleDrive(file, cancellationToken);
                    break;
                
                default:
                    return BaseResponseModel<Unit>.CreateError($"Invalid direction: {request.Direction}. Must be \"to-gdrive\" or \"from-gdrive\"", 400, "INVALID_DIRECTION");
            }

            logger.LogInformation("Successfully moved file {FileId} {Direction} Google Drive",
                request.FileId, request.Direction);

            return BaseResponseModel<Unit>.CreateSuccess(Unit.Value, $"File {request.Direction} Google Drive completed successfully");
        }
        catch (UnauthorizedAccessException ex)
        {
            logger.LogWarning("Unauthorized access to Google Drive for user {UserId}: {Message}",
                request.UserId, ex.Message);
            return BaseResponseModel<Unit>.CreateError("You are not authorized to access Google Drive", 401, "GOOGLE_DRIVE_UNAUTHORIZED");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error moving file {FileId} {Direction} Google Drive for user {UserId}",
                request.FileId, request.Direction, request.UserId);
            return BaseResponseModel<Unit>.CreateError("An error occurred while moving the file", 500, "INTERNAL_ERROR");
        }
    }

    private async Task MoveToGoogleDrive(Domain.Entities.File file, string? targetFolderId, CancellationToken cancellationToken)
    {
        // TODO: In a real implementation, this would:
        // 1. Get the file stream from the current storage provider
        // 2. Upload to Google Drive
        // 3. Update the file entity to point to Google Drive
        // 4. Update sync status
        // 5. Optionally delete from previous storage

        logger.LogInformation("Moving file {FileId} to Google Drive folder {TargetFolderId}", 
            file.Id, targetFolderId);

        // For now, just create/update sync status
        var syncStatus = await syncStatusRepository.UpsertAsync(file.Id, "GoogleDrive", null, cancellationToken);
        syncStatus.StartSync();
        syncStatusRepository.Update(syncStatus);
        await syncStatusRepository.SaveChangesAsync(cancellationToken);
    }

    private async Task MoveFromGoogleDrive(Domain.Entities.File file, CancellationToken cancellationToken)
    {
        // TODO: In a real implementation, this would:
        // 1. Download the file from Google Drive
        // 2. Upload to the default storage provider
        // 3. Update the file entity
        // 4. Update sync status
        // 5. Optionally delete from Google Drive

        logger.LogInformation("Moving file {FileId} from Google Drive", file.Id);

        // For now, just update sync status
        var syncStatus = await syncStatusRepository.GetByFileIdAsync(file.Id, "GoogleDrive", cancellationToken);
        if (syncStatus != null)
        {
            syncStatus.StartSync();
            syncStatusRepository.Update(syncStatus);
            await syncStatusRepository.SaveChangesAsync(cancellationToken);
        }
    }
}
