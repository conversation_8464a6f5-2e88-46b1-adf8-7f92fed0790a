using FluentValidation;
using VeasyFileManager.Application.Commands.Files;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.Commands;

/// <summary>
/// Validator for grant file permission command
/// </summary>
public class GrantFilePermissionCommandValidator : AbstractValidator<GrantFilePermissionCommand>
{
    public GrantFilePermissionCommandValidator()
    {
        RuleFor(x => x.FileId)
            .NotEmpty()
            .WithMessage("File ID is required");

        RuleFor(x => x.GrantedBy)
            .NotEmpty()
            .WithMessage("Granted by user ID is required");

        RuleFor(x => x.Permission)
            .IsInEnum()
            .WithMessage("Valid permission type is required");

        // Either UserId or RoleId must be provided, but not both
        RuleFor(x => x)
            .Must(x => (x.UserId.HasValue && !x.RoleId.HasValue) || (!x.UserId.HasValue && x.RoleId.HasValue))
            .WithMessage("Either UserId or RoleId must be provided, but not both");

        RuleFor(x => x.UserId)
            .NotEqual(Guid.Empty)
            .When(x => x.UserId.HasValue)
            .WithMessage("Valid User ID is required when provided");

        RuleFor(x => x.RoleId)
            .NotEqual(Guid.Empty)
            .When(x => x.RoleId.HasValue)
            .WithMessage("Valid Role ID is required when provided");

        // Expiration date must be in the future if provided
        RuleFor(x => x.ExpiresAt)
            .GreaterThan(DateTime.UtcNow)
            .When(x => x.ExpiresAt.HasValue)
            .WithMessage("Expiration date must be in the future");

        // Validate permission type combinations
        RuleFor(x => x.Permission)
            .Must(BeValidPermissionType)
            .WithMessage("Invalid permission type specified");

        // User cannot grant permissions to themselves (except for admin permissions)
        RuleFor(x => x)
            .Must(x => x.UserId != x.GrantedBy || x.Permission == PermissionType.Admin)
            .When(x => x.UserId.HasValue)
            .WithMessage("Cannot grant permissions to yourself unless granting admin permissions");
    }

    private static bool BeValidPermissionType(PermissionType permissionType)
    {
        return Enum.IsDefined(typeof(PermissionType), permissionType);
    }
} 