using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands;

/// <summary>
/// Command to revoke a permission
/// </summary>
public class RevokePermissionCommand : IRequest<BaseResponseModel<bool>>
{
    /// <summary>
    /// Permission ID to revoke
    /// </summary>
    public Guid PermissionId { get; set; }

    /// <summary>
    /// User ID who is revoking the permission
    /// </summary>
    public Guid RevokedBy { get; set; }
} 