using MediatR;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Command to grant permission to a user on a file
/// </summary>
public class GrantFilePermissionCommand : IRequest<BaseResponseModel<Guid>>
{
    /// <summary>
    /// File ID to grant permission on
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// User ID to grant permission to (optional if RoleId is provided)
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// Role ID to grant permission to (optional if UserId is provided)
    /// </summary>
    public Guid? RoleId { get; set; }

    /// <summary>
    /// Type of permission to grant
    /// </summary>
    public PermissionType Permission { get; set; }

    /// <summary>
    /// Optional expiration date for the permission
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// User ID who is granting the permission
    /// </summary>
    public Guid GrantedBy { get; set; }
} 