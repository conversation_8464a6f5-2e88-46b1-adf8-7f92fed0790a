using MediatR;
using Microsoft.Extensions.Logging;
using AutoMapper;
using System.Security.Cryptography;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.ValueObjects;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Handler for uploading files
/// </summary>
public class UploadFileCommandHandler(
    IFileRepository fileRepository,
    IFolderRepository folderRepository,
    IFileStorageService storageService,
    IMapper mapper,
    ILogger<UploadFileCommandHandler> logger)
    : IRequestHandler<UploadFileCommand, BaseResponseModel<FileDto>>
{
    public async Task<BaseResponseModel<FileDto>> Handle(UploadFileCommand request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Starting file upload for user {UserId}, file: {FileName}", 
                request.UserId, request.File.FileName);

            // Validate parent folder exists and user has access
            if (request.ParentFolderId.HasValue)
            {
                var parentFolder = await folderRepository.GetByIdAsync(request.ParentFolderId.Value, cancellationToken);
                if (parentFolder == null)
                {
                    return BaseResponseModel<FileDto>.CreateError("Parent folder not found", 404, "FOLDER_NOT_FOUND");
                }

                var hasAccess = await folderRepository.HasUserAccessAsync(parentFolder.Id, request.UserId, PermissionType.Write, cancellationToken);
                if (!hasAccess)
                {
                    logger.LogWarning("User does not have write access to the parent folder");
                    return BaseResponseModel<FileDto>.CreateError("You don't have permission to upload files to this folder", 403, "PERMISSION_DENIED");
                }
            }

            // Copy file to memory stream to ensure we can read it multiple times
            // and avoid issues with non-seekable streams
            using var fileStream = request.File.OpenReadStream();
            using var memoryStream = new MemoryStream();
            await fileStream.CopyToAsync(memoryStream, cancellationToken);
            memoryStream.Position = 0;

            // Calculate file hashes
            var fileHashes = await CalculateFileHashesAsync(memoryStream, cancellationToken);

            // Check for duplicate files
            var existingFile = await fileRepository.GetByHashAsync(fileHashes.Md5Hash, cancellationToken);
            if (existingFile != null)
            {
                logger.LogInformation("Duplicate file found: {FileId}, returning existing file", existingFile.Id);
                var existingFileDto = mapper.Map<FileDto>(existingFile);
                return BaseResponseModel<FileDto>.CreateSuccess(existingFileDto, "File already exists - returning existing file");
            }

            // Reset stream position for upload
            memoryStream.Position = 0;

            // Upload file to storage
            var fileName = request.DisplayName ?? request.File.FileName;
            var filePath = await storageService.UploadFileAsync(
                memoryStream, 
                fileName, 
                request.File.ContentType ?? "application/octet-stream", 
                cancellationToken);

            // Create file entity
            var fileEntity = Domain.Entities.File.Create(
                name: fileName,
                displayName: fileName,
                fileSize: request.File.Length,
                mimeType: request.File.ContentType ?? "application/octet-stream",
                hashMd5: fileHashes.Md5Hash,
                hashSha256: fileHashes.Sha256Hash,
                ownerId: request.UserId,
                parentFolderId: request.ParentFolderId);

            // Set storage path after creation
            fileEntity.SetStoragePath(filePath, StorageProvider.R2);

            // Save to database
            await fileRepository.AddAsync(fileEntity, cancellationToken);
            await fileRepository.SaveChangesAsync(cancellationToken);

            logger.LogInformation("File uploaded successfully: {FileId} for user {UserId}", 
                fileEntity.Id, request.UserId);

            var resultDto = mapper.Map<FileDto>(fileEntity);
            return BaseResponseModel<FileDto>.CreateSuccess(resultDto, "File uploaded successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error uploading file {FileName} for user {UserId}", 
                request.File.FileName, request.UserId);
            return BaseResponseModel<FileDto>.CreateError("An error occurred while uploading the file", 500, "INTERNAL_ERROR");
        }
    }

    private async Task<(string Md5Hash, string Sha256Hash)> CalculateFileHashesAsync(Stream fileStream, CancellationToken cancellationToken)
    {
        using var md5 = MD5.Create();
        using var sha256 = SHA256.Create();
        
        var buffer = new byte[8192];
        int bytesRead;
        
        while ((bytesRead = await fileStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken)) > 0)
        {
            md5.TransformBlock(buffer, 0, bytesRead, buffer, 0);
            sha256.TransformBlock(buffer, 0, bytesRead, buffer, 0);
        }
        
        md5.TransformFinalBlock(buffer, 0, 0);
        sha256.TransformFinalBlock(buffer, 0, 0);
        
        var md5Hash = Convert.ToHexString(md5.Hash!).ToLowerInvariant();
        var sha256Hash = Convert.ToHexString(sha256.Hash!).ToLowerInvariant();
        
        return (md5Hash, sha256Hash);
    }
} 