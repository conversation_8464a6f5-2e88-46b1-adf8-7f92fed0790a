using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Handler for disabling share links
/// </summary>
public class DisableShareCommandHandler(
    IFileRepository fileRepository,
    IFilePermissionService permissionService,
    ILogger<DisableShareCommandHandler> logger) : IRequestHandler<DisableShareCommand, BaseResponseModel<Unit>>
{
    private readonly IFileRepository _fileRepository = fileRepository;
    private readonly IFilePermissionService _permissionService = permissionService;
    private readonly ILogger<DisableShareCommandHandler> _logger = logger;

    public async Task<BaseResponseModel<Unit>> Handle(DisableShareCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Disabling share with token {ShareToken} by user {UserId}", 
            request.ShareToken, request.DisabledBy);

        try
        {
            // Get the file share
            var fileShare = await _fileRepository.GetFileShareByTokenAsync(request.ShareToken, cancellationToken);
            if (fileShare == null)
            {
                _logger.LogWarning("File share with token {ShareToken} not found", request.ShareToken);
                return BaseResponseModel<Unit>.CreateError("Share link not found", 404, "SHARE_NOT_FOUND");
            }

            // Get the file to check permissions
            var file = await _fileRepository.GetByIdAsync(fileShare.FileId, cancellationToken);
            if (file == null)
            {
                _logger.LogError("File {FileId} not found for share {ShareToken}", fileShare.FileId, request.ShareToken);
                return BaseResponseModel<Unit>.CreateError("Associated file not found", 404, "FILE_NOT_FOUND");
            }

            // Check if user has permission to disable the share
            // User can disable if they are the file owner, share creator, or have admin permission
            var canDisable = file.OwnerId == request.DisabledBy ||
                            fileShare.SharedBy == request.DisabledBy ||
                            await _permissionService.HasPermissionAsync(request.DisabledBy, fileShare.FileId, PermissionType.Admin, cancellationToken);

            if (!canDisable)
            {
                _logger.LogWarning("User {UserId} does not have permission to disable share {ShareToken}", 
                    request.DisabledBy, request.ShareToken);
                return BaseResponseModel<Unit>.CreateError("You don't have permission to disable this share", 403, "PERMISSION_DENIED");
            }

            // Disable the share
            fileShare.Deactivate();
            await _fileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Share {ShareToken} disabled successfully by user {UserId}", 
                request.ShareToken, request.DisabledBy);

            return BaseResponseModel<Unit>.CreateSuccess(Unit.Value, "Share link disabled successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling share {ShareToken} by user {UserId}", 
                request.ShareToken, request.DisabledBy);
            return BaseResponseModel<Unit>.CreateError("An error occurred while disabling the share", 500, "INTERNAL_ERROR");
        }
    }
} 