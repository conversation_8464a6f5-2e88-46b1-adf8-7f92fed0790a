using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Command to disable a share link
/// </summary>
public class DisableShareCommand : IRequest<BaseResponseModel<Unit>>
{
    /// <summary>
    /// Share token to disable
    /// </summary>
    public string ShareToken { get; set; } = null!;

    /// <summary>
    /// User ID disabling the share
    /// </summary>
    public Guid DisabledBy { get; set; }
} 