using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Handler for DeleteFileCommand
/// </summary>
public class DeleteFileCommandHandler(
    IFileRepository fileRepository,
    IDeletedItemRepository deletedItemRepository,
    IFileStorageService storageService,
    ILogger<DeleteFileCommandHandler> logger) : IRequestHandler<DeleteFileCommand, BaseResponseModel<bool>>
{
    private readonly IFileRepository _fileRepository = fileRepository ?? throw new ArgumentNullException(nameof(fileRepository));
    private readonly IDeletedItemRepository _deletedItemRepository = deletedItemRepository ?? throw new ArgumentNullException(nameof(deletedItemRepository));
    private readonly IFileStorageService _storageService = storageService ?? throw new ArgumentNullException(nameof(storageService));
    private readonly ILogger<DeleteFileCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<BaseResponseModel<bool>> Handle(DeleteFileCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Deleting file {FileId} for user {UserId}, permanent: {PermanentDelete}",
            request.FileId, request.UserId, request.PermanentDelete);

        try
        {
            // Get the file
            var file = await _fileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (file == null)
            {
                _logger.LogWarning("File {FileId} not found", request.FileId);
                return BaseResponseModel<bool>.CreateError("File not found", 404, "FILE_NOT_FOUND");
            }

            // Check if user has permission to delete the file
            var hasPermission = await _fileRepository.HasUserAccessAsync(
                request.UserId,
                request.FileId,
                PermissionType.Delete,
                cancellationToken);

            if (!hasPermission && file.OwnerId != request.UserId)
            {
                return BaseResponseModel<bool>.CreateError("You don't have permission to delete this file", 403, "PERMISSION_DENIED");
            }

            if (request.PermanentDelete)
            {
                // Permanent delete: remove from database only
                // R2 storage files are NEVER deleted for data preservation
                try
                {
                    // STORAGE DELETION DISABLED FOR SAFETY
                    // Files on R2 are never deleted to ensure data preservation
                    if (!string.IsNullOrEmpty(file.FilePath))
                    {
                        _logger.LogInformation("File deletion from R2 storage is disabled for safety. File preserved at: {FilePath}", file.FilePath);
                        // await _storageService.DeleteFileAsync(file.FilePath, cancellationToken);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to delete file from storage: {FilePath}", file.FilePath);
                    // Continue with database deletion even if storage deletion fails
                }

                // Remove from database
                _fileRepository.Remove(file);
                await _fileRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("File {FileId} permanently deleted from database (R2 file preserved)", request.FileId);
                return BaseResponseModel<bool>.CreateSuccess(true, "File permanently deleted successfully (storage file preserved)");
            }
            else
            {
                // Soft delete: mark as deleted in database and create DeletedItem record
                file.Delete();
                _fileRepository.Update(file);

                // Create DeletedItem record for recycle bin
                var deletedItem = DeletedItem.CreateForFile(file, request.UserId);
                await _deletedItemRepository.AddAsync(deletedItem, cancellationToken);

                await _fileRepository.SaveChangesAsync(cancellationToken);
                await _deletedItemRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("File {FileId} soft deleted and added to recycle bin", request.FileId);
                return BaseResponseModel<bool>.CreateSuccess(true, "File moved to recycle bin successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file {FileId} for user {UserId}", request.FileId, request.UserId);
            return BaseResponseModel<bool>.CreateError("An error occurred while deleting the file", 500, "INTERNAL_ERROR");
        }
    }
}
