using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Command to copy a file to a new location
/// </summary>
public class CopyFileCommand : IRequest<BaseResponseModel<FileDto>>
{
    /// <summary>
    /// ID of the file to copy
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// Target folder ID (null for root)
    /// </summary>
    public Guid? TargetFolderId { get; set; }

    /// <summary>
    /// New name for the copied file (optional)
    /// </summary>
    public string? NewName { get; set; }

    /// <summary>
    /// User ID performing the copy
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Whether to sync the copy to Google Drive
    /// </summary>
    public bool SyncToGoogleDrive { get; set; } = true;
} 