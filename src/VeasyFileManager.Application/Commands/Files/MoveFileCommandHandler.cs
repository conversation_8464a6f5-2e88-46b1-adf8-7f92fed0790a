using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Handler for moving files
/// </summary>
public class MoveFileCommandHandler(
    IFileRepository fileRepository,
    IFolderRepository folderRepository,
    IFilePermissionService permissionService,
    ILogger<MoveFileCommandHandler> logger) : IRequestHandler<MoveFileCommand, BaseResponseModel<Unit>>
{
    private readonly IFileRepository _fileRepository = fileRepository;
    private readonly IFolderRepository _folderRepository = folderRepository;
    private readonly IFilePermissionService _permissionService = permissionService;
    private readonly ILogger<MoveFileCommandHandler> _logger = logger;

    public async Task<BaseResponseModel<Unit>> Handle(MoveFileCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Check if user has write permission on the source file
            var hasWritePermission = await _permissionService.HasPermissionAsync(
                request.UserId, request.FileId, PermissionType.Write, cancellationToken);
            
            if (!hasWritePermission)
            {
                _logger.LogWarning("User {UserId} attempted to move file {FileId} without write permission", 
                    request.UserId, request.FileId);
                return BaseResponseModel<Unit>.CreateError("You don't have permission to move this file", 403, "PERMISSION_DENIED");
            }

            // Get the file
            var file = await _fileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (file == null)
            {
                return BaseResponseModel<Unit>.CreateError($"File with ID {request.FileId} not found", 404, "FILE_NOT_FOUND");
            }

            // Check if target folder exists and user has write permission
            if (request.TargetFolderId.HasValue)
            {
                var targetFolder = await _folderRepository.GetByIdAsync(request.TargetFolderId.Value, cancellationToken);
                if (targetFolder == null)
                {
                    return BaseResponseModel<Unit>.CreateError($"Target folder with ID {request.TargetFolderId} not found", 404, "FOLDER_NOT_FOUND");
                }

                var hasTargetWritePermission = await _permissionService.HasFolderPermissionAsync(
                    request.UserId, request.TargetFolderId.Value, PermissionType.Write, cancellationToken);
                
                if (!hasTargetWritePermission)
                {
                    return BaseResponseModel<Unit>.CreateError("You don't have permission to move files to this folder", 403, "PERMISSION_DENIED");
                }
            }

            // Move the file
            file.Move(request.TargetFolderId);
            
            _fileRepository.Update(file);
            await _fileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("File {FileId} moved to folder {TargetFolderId} by user {UserId}", 
                request.FileId, request.TargetFolderId, request.UserId);

            return BaseResponseModel<Unit>.CreateSuccess(Unit.Value, "File moved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving file {FileId} to folder {TargetFolderId} by user {UserId}",
                request.FileId, request.TargetFolderId, request.UserId);
            return BaseResponseModel<Unit>.CreateError("An error occurred while moving the file", 500, "INTERNAL_ERROR");
        }
    }
} 