using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Command to update file metadata
/// </summary>
public class UpdateFileCommand : IRequest<BaseResponseModel<FileDto>>
{
    /// <summary>
    /// ID of the file to update
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// New display name for the file
    /// </summary>
    public string DisplayName { get; set; } = null!;

    /// <summary>
    /// New MIME type (optional)
    /// </summary>
    public string? MimeType { get; set; }

    /// <summary>
    /// User ID performing the update
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// New description for the file (optional)
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// New parent folder ID (optional, for moving the file)
    /// </summary>
    public Guid? ParentFolderId { get; set; }

    /// <summary>
    /// Tags for the file (optional)
    /// </summary>
    public List<string>? Tags { get; set; }
} 