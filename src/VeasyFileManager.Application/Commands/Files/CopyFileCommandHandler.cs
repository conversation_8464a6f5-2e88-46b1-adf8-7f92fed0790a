using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Handler for copying files
/// </summary>
public class CopyFileCommandHandler(
    IFileRepository fileRepository,
    IFolderRepository folderRepository,
    IFilePermissionService permissionService,
    IFileStorageService storageService,
    IMapper mapper,
    ILogger<CopyFileCommandHandler> logger) : IRequestHandler<CopyFileCommand, BaseResponseModel<FileDto>>
{
    private readonly IFileRepository _fileRepository = fileRepository;
    private readonly IFolderRepository _folderRepository = folderRepository;
    private readonly IFilePermissionService _permissionService = permissionService;
    private readonly IFileStorageService _storageService = storageService;
    private readonly IMapper _mapper = mapper;
    private readonly ILogger<CopyFileCommandHandler> _logger = logger;

    public async Task<BaseResponseModel<FileDto>> Handle(CopyFileCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Check if user has read permission on the source file
            var hasReadPermission = await _permissionService.HasPermissionAsync(
                request.UserId, request.FileId, PermissionType.Read, cancellationToken);
            
            if (!hasReadPermission)
            {
                _logger.LogWarning("User {UserId} attempted to copy file {FileId} without read permission", 
                    request.UserId, request.FileId);
                return BaseResponseModel<FileDto>.CreateError("You don't have permission to copy this file", 403, "PERMISSION_DENIED");
            }

            // Get the source file
            var sourceFile = await _fileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (sourceFile == null)
            {
                return BaseResponseModel<FileDto>.CreateError($"File with ID {request.FileId} not found", 404, "FILE_NOT_FOUND");
            }

            // Check if target folder exists and user has write permission
            if (request.TargetFolderId.HasValue)
            {
                var targetFolder = await _folderRepository.GetByIdAsync(request.TargetFolderId.Value, cancellationToken);
                if (targetFolder == null)
                {
                    return BaseResponseModel<FileDto>.CreateError($"Target folder with ID {request.TargetFolderId} not found", 404, "FOLDER_NOT_FOUND");
                }

                var hasWritePermission = await _permissionService.HasFolderPermissionAsync(
                    request.UserId, request.TargetFolderId.Value, PermissionType.Write, cancellationToken);
                
                if (!hasWritePermission)
                {
                    return BaseResponseModel<FileDto>.CreateError("You don't have permission to copy files to this folder", 403, "PERMISSION_DENIED");
                }
            }

            // Determine the new file name
            var newFileName = !string.IsNullOrWhiteSpace(request.NewName) ? request.NewName : sourceFile.Name;
            var displayName = !string.IsNullOrWhiteSpace(request.NewName) ? request.NewName : sourceFile.DisplayName;

            // Create the copied file entity
            var copiedFile = VeasyFileManager.Domain.Entities.File.Create(
                name: newFileName,
                displayName: displayName,
                fileSize: sourceFile.FileSize,
                mimeType: sourceFile.MimeType,
                hashMd5: sourceFile.HashMd5,
                hashSha256: sourceFile.HashSha256,
                ownerId: request.UserId,
                parentFolderId: request.TargetFolderId);

            // Copy the actual file content in storage
            if (!string.IsNullOrEmpty(sourceFile.FilePath))
            {
                try
                {
                    // Generate destination path for the copied file
                    var fileExtension = Path.GetExtension(newFileName);
                    var destinationPath = $"{request.UserId}/{copiedFile.Id}{fileExtension}";
                    
                    _logger.LogDebug("Copying file content from {SourcePath} to {DestinationPath}", 
                        sourceFile.FilePath, destinationPath);

                    // Copy the file content in storage
                    var copiedFilePath = await _storageService.CopyFileAsync(
                        sourceFile.FilePath, 
                        destinationPath, 
                        cancellationToken);

                    // Set the storage path on the copied file entity
                    copiedFile.SetStoragePath(copiedFilePath, sourceFile.StorageProvider);

                    _logger.LogInformation("File content copied successfully from {SourcePath} to {DestinationPath}", 
                        sourceFile.FilePath, copiedFilePath);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to copy file content for file {FileId}. Proceeding with metadata-only copy.", 
                        request.FileId);
                }
            }
            else
            {
                _logger.LogWarning("Source file {FileId} has no storage path. Creating metadata-only copy.", 
                    request.FileId);
            }
            
            await _fileRepository.AddAsync(copiedFile, cancellationToken);
            await _fileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("File {FileId} copied to {NewFileId} by user {UserId}", 
                request.FileId, copiedFile.Id, request.UserId);

            var result = _mapper.Map<FileDto>(copiedFile);
            return BaseResponseModel<FileDto>.CreateSuccess(result, "File copied successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error copying file {FileId} for user {UserId}", request.FileId, request.UserId);
            return BaseResponseModel<FileDto>.CreateError("An error occurred while copying the file", 500, "INTERNAL_ERROR");
        }
    }
}