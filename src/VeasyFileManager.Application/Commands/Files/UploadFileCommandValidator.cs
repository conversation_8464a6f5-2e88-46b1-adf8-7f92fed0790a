using FluentValidation;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Validator for UploadFileCommand
/// </summary>
public class UploadFileCommandValidator : AbstractValidator<UploadFileCommand>
{
    private readonly string[] _allowedExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".txt", ".rtf", ".csv", ".zip", ".rar", ".7z", ".mp4", ".avi", ".mov", ".wmv", ".mp3", ".wav", ".flac" };
    private const long MaxFileSize = 100 * 1024 * 1024; // 100MB
    private const long MinFileSize = 1; // 1 byte

    public UploadFileCommandValidator()
    {
        RuleFor(x => x.File)
            .NotNull()
            .WithMessage("File is required");

        RuleFor(x => x.File.Length)
            .GreaterThan(MinFileSize)
            .WithMessage("File must not be empty")
            .LessThanOrEqualTo(MaxFileSize)
            .WithMessage($"File size must not exceed {MaxFileSize / (1024 * 1024)}MB");

        RuleFor(x => x.File.FileName)
            .NotEmpty()
            .WithMessage("File name is required")
            .Must(BeValidFileName)
            .WithMessage("File name contains invalid characters")
            .Must(HaveAllowedExtension)
            .WithMessage($"File extension is not allowed. Allowed extensions: {string.Join(", ", _allowedExtensions)}");

        RuleFor(x => x.UserId)
            .NotEmpty()
            .WithMessage("User ID is required");

        RuleFor(x => x.DisplayName)
            .Must(BeValidFileName)
            .When(x => !string.IsNullOrEmpty(x.DisplayName))
            .WithMessage("Display name contains invalid characters");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description cannot exceed 1000 characters");

        RuleFor(x => x.Tags)
            .Must(HaveValidTags)
            .WithMessage("Tags must be non-empty and contain only alphanumeric characters and spaces");
    }

    private bool BeValidFileName(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
            return false;

        var invalidChars = Path.GetInvalidFileNameChars();
        return !fileName.Any(c => invalidChars.Contains(c));
    }

    private bool HaveAllowedExtension(string fileName)
    {
        if (string.IsNullOrEmpty(fileName))
            return false;

        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        return _allowedExtensions.Contains(extension);
    }

    private bool HaveValidTags(List<string> tags)
    {
        if (tags == null || !tags.Any())
            return true;

        return tags.All(tag => 
            !string.IsNullOrWhiteSpace(tag) && 
            tag.Length <= 50 && 
            tag.All(c => char.IsLetterOrDigit(c) || char.IsWhiteSpace(c)));
    }
} 