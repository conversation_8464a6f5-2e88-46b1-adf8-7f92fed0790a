using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Command to move a file to a new location
/// </summary>
public class MoveFileCommand : IRequest<BaseResponseModel<Unit>>
{
    /// <summary>
    /// ID of the file to move
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// Target folder ID (null for root)
    /// </summary>
    public Guid? TargetFolderId { get; set; }

    /// <summary>
    /// User ID performing the move
    /// </summary>
    public Guid UserId { get; set; }
} 