using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Handler for granting file permissions
/// </summary>
public class GrantFilePermissionCommandHandler(
    IFilePermissionService permissionService,
    ILogger<GrantFilePermissionCommandHandler> logger) : IRequestHandler<GrantFilePermissionCommand, BaseResponseModel<Guid>>
{
    private readonly IFilePermissionService _permissionService = permissionService;
    private readonly ILogger<GrantFilePermissionCommandHandler> _logger = logger;

    public async Task<BaseResponseModel<Guid>> Handle(GrantFilePermissionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Granting permission {Permission} on file {FileId} to user {UserId} by {GrantedBy}",
            request.Permission, request.FileId, request.UserId, request.GrantedBy);

        try
        {
            var permissionId = await _permissionService.GrantFilePermissionAsync(
                request.FileId,
                request.UserId,
                request.RoleId,
                request.Permission,
                request.GrantedBy,
                request.ExpiresAt,
                cancellationToken);

            _logger.LogInformation("Successfully granted permission {PermissionId} on file {FileId}",
                permissionId, request.FileId);

            return BaseResponseModel<Guid>.CreateSuccess(permissionId, "Permission granted successfully");
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning("Unauthorized attempt to grant permission on file {FileId} by user {GrantedBy}: {Message}",
                request.FileId, request.GrantedBy, ex.Message);
            return BaseResponseModel<Guid>.CreateError("You don't have permission to grant permissions on this file", 403, "PERMISSION_DENIED");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid argument when granting permission on file {FileId}: {Message}",
                request.FileId, ex.Message);
            return BaseResponseModel<Guid>.CreateError(ex.Message, 400, "INVALID_ARGUMENT");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error granting permission on file {FileId} by user {GrantedBy}",
                request.FileId, request.GrantedBy);
            return BaseResponseModel<Guid>.CreateError("An error occurred while granting permission", 500, "INTERNAL_ERROR");
        }
    }
} 