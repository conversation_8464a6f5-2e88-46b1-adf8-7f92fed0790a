using MediatR;
using Microsoft.AspNetCore.Http;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Command to upload a new file
/// </summary>
public class UploadFileCommand : IRequest<BaseResponseModel<FileDto>>
{
    /// <summary>
    /// The file to upload
    /// </summary>
    public IFormFile File { get; set; } = null!;

    /// <summary>
    /// Parent folder ID (null for root)
    /// </summary>
    public Guid? ParentFolderId { get; set; }

    /// <summary>
    /// Display name for the file (if different from original name)
    /// </summary>
    public string? DisplayName { get; set; }

    /// <summary>
    /// Whether to sync to Google Drive
    /// </summary>
    public bool SyncToGoogleDrive { get; set; } = true;

    /// <summary>
    /// User ID performing the upload
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Tags for the file
    /// </summary>
    public List<string> Tags { get; set; } = new();

    /// <summary>
    /// File description
    /// </summary>
    public string? Description { get; set; }
} 