using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Command to create a file share link
/// </summary>
public class CreateFileShareCommand : IRequest<BaseResponseModel<FileShareDto>>
{
    /// <summary>
    /// ID of the file to share
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// Type of share (Public, Password, UserSpecific)
    /// </summary>
    public ShareType ShareType { get; set; }

    /// <summary>
    /// Password for password-protected shares
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Expiration date for the share (optional)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Maximum number of downloads allowed (optional)
    /// </summary>
    public int? MaxDownloads { get; set; }

    /// <summary>
    /// User ID creating the share
    /// </summary>
    public Guid UserId { get; set; }
} 