using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Command to delete a file
/// </summary>
public class DeleteFileCommand : IRequest<BaseResponseModel<bool>>
{
    /// <summary>
    /// ID of the file to delete
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// User ID performing the deletion
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Whether to permanently delete the file (true) or soft delete (false)
    /// </summary>
    public bool PermanentDelete { get; set; } = false;
} 