using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Files;

/// <summary>
/// Hand<PERSON> for updating file metadata
/// </summary>
public class UpdateFileCommandHandler(
    IFileRepository fileRepository,
    IFilePermissionService permissionService,
    IMapper mapper,
    ILogger<UpdateFileCommandHandler> logger) : IRequestHandler<UpdateFileCommand, BaseResponseModel<FileDto>>
{
    private readonly IFileRepository _fileRepository = fileRepository;
    private readonly IFilePermissionService _permissionService = permissionService;
    private readonly IMapper _mapper = mapper;
    private readonly ILogger<UpdateFileCommandHandler> _logger = logger;

    public async Task<BaseResponseModel<FileDto>> Handle(UpdateFileCommand request, CancellationToken cancellationToken)
    {
        try
        {
            // Check if user has write permission on the file
            var hasWritePermission = await _permissionService.HasPermissionAsync(
                request.UserId, request.FileId, PermissionType.Write, cancellationToken);
            
            if (!hasWritePermission)
            {
                _logger.LogWarning("User {UserId} attempted to update file {FileId} without write permission", 
                    request.UserId, request.FileId);
                return BaseResponseModel<FileDto>.CreateError("You don't have permission to update this file", 403, "PERMISSION_DENIED");
            }

            // Get the file
            var file = await _fileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (file == null)
            {
                return BaseResponseModel<FileDto>.CreateError($"File with ID {request.FileId} not found", 404, "FILE_NOT_FOUND");
            }

            // Update the file metadata
            file.UpdateMetadata(request.DisplayName, request.MimeType);
            
            _fileRepository.Update(file);
            await _fileRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("File {FileId} updated by user {UserId}", 
                request.FileId, request.UserId);

            var result = _mapper.Map<FileDto>(file);
            return BaseResponseModel<FileDto>.CreateSuccess(result, "File updated successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating file {FileId} by user {UserId}",
                request.FileId, request.UserId);
            return BaseResponseModel<FileDto>.CreateError("An error occurred while updating the file", 500, "INTERNAL_ERROR");
        }
    }
} 