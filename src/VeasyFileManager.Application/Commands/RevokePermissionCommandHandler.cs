using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands;

/// <summary>
/// Handler for revoking permissions
/// </summary>
public class RevokePermissionCommandHandler : IRequestHandler<RevokePermissionCommand, BaseResponseModel<bool>>
{
    private readonly IFilePermissionService _permissionService;
    private readonly ILogger<RevokePermissionCommandHandler> _logger;

    public RevokePermissionCommandHandler(
        IFilePermissionService permissionService,
        ILogger<RevokePermissionCommandHandler> logger)
    {
        _permissionService = permissionService;
        _logger = logger;
    }

    public async Task<BaseResponseModel<bool>> Handle(RevokePermissionCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Revoking permission {PermissionId} by user {RevokedBy}",
            request.PermissionId, request.RevokedBy);

        try
        {
            var result = await _permissionService.RevokePermissionAsync(
                request.PermissionId,
                request.RevokedBy,
                cancellationToken);

            if (result)
            {
                _logger.LogInformation("Successfully revoked permission {PermissionId}",
                    request.PermissionId);
                return BaseResponseModel<bool>.CreateSuccess(true, "Permission revoked successfully");
            }
            else
            {
                _logger.LogWarning("Permission {PermissionId} not found or already revoked",
                    request.PermissionId);
                return BaseResponseModel<bool>.CreateError("Permission not found or already revoked", 404, "PERMISSION_NOT_FOUND");
            }
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning("Unauthorized attempt to revoke permission {PermissionId} by user {RevokedBy}: {Message}",
                request.PermissionId, request.RevokedBy, ex.Message);
            return BaseResponseModel<bool>.CreateError("You don't have permission to revoke this permission", 403, "PERMISSION_DENIED");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking permission {PermissionId} by user {RevokedBy}",
                request.PermissionId, request.RevokedBy);
            return BaseResponseModel<bool>.CreateError("An error occurred while revoking the permission", 500, "INTERNAL_ERROR");
        }
    }
} 