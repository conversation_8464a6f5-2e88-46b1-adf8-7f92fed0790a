using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.Commands.RecycleBin;

/// <summary>
/// Handler for permanently deleting items from recycle bin
/// </summary>
public class PermanentDeleteItemCommandHandler(
    IDeletedItemRepository deletedItemRepository,
    IFolderRepository folderRepository,
    IFileRepository fileRepository,
    ILogger<PermanentDeleteItemCommandHandler> logger) : IRequestHandler<PermanentDeleteItemCommand, BaseResponseModel<bool>>
{
    public async Task<BaseResponseModel<bool>> Handle(PermanentDeleteItemCommand request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Permanently deleting item {DeletedItemId} by user {UserId}",
            request.DeletedItemId, request.UserId);

        try
        {
            // Get the deleted item
            var deletedItem = await deletedItemRepository.GetByIdAsync(request.DeletedItemId, cancellationToken);
            if (deletedItem == null)
            {
                logger.LogWarning("Deleted item {DeletedItemId} not found", request.DeletedItemId);
                return BaseResponseModel<bool>.CreateError("Deleted item not found", 404, "NOT_FOUND");
            }

            // Check if user owns the deleted item
            if (deletedItem.DeletedBy != request.UserId)
            {
                logger.LogWarning("User {UserId} attempted to permanently delete item {DeletedItemId} they don't own",
                    request.UserId, request.DeletedItemId);
                return BaseResponseModel<bool>.CreateError("Unauthorized", 403, "FORBIDDEN");
            }

            // Permanently delete based on item type
            bool deleted = false;
            if (deletedItem.ItemType == DeletedItemType.Folder)
            {
                deleted = await PermanentDeleteFolderAsync(deletedItem, cancellationToken);
            }
            else if (deletedItem.ItemType == DeletedItemType.File)
            {
                deleted = await PermanentDeleteFileAsync(deletedItem, cancellationToken);
            }

            if (!deleted)
            {
                return BaseResponseModel<bool>.CreateError(
                    "Failed to permanently delete the item. The original item may not exist.",
                    400,
                    "DELETE_FAILED");
            }

            // Remove the deleted item record
            deletedItemRepository.Remove(deletedItem);
            await deletedItemRepository.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully permanently deleted {ItemType} {DeletedItemId} by user {UserId}",
                deletedItem.ItemType, request.DeletedItemId, request.UserId);

            return BaseResponseModel<bool>.CreateSuccess(true,
                $"{deletedItem.ItemType} '{deletedItem.OriginalName}' has been permanently deleted");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error permanently deleting item {DeletedItemId}", request.DeletedItemId);
            return BaseResponseModel<bool>.CreateError(
                "An error occurred while permanently deleting the item",
                500,
                "INTERNAL_ERROR");
        }
    }

    private async Task<bool> PermanentDeleteFolderAsync(
        Domain.Entities.DeletedItem deletedItem,
        CancellationToken cancellationToken)
    {
        try
        {
            // Find the soft-deleted folder and permanently delete it
            var folder = await folderRepository.GetByIdAsync(deletedItem.OriginalId, cancellationToken);

            if (folder != null)
            {
                // Permanently remove from database
                // Note: Files on R2 storage are preserved for safety
                folderRepository.Remove(folder);
                await folderRepository.SaveChangesAsync(cancellationToken);

                logger.LogInformation("Permanently deleted folder {FolderId} from database (R2 files preserved)",
                    deletedItem.OriginalId);
            }

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error permanently deleting folder {FolderId}", deletedItem.OriginalId);
            return false;
        }
    }

    private async Task<bool> PermanentDeleteFileAsync(
        Domain.Entities.DeletedItem deletedItem,
        CancellationToken cancellationToken)
    {
        try
        {
            // Find the soft-deleted file and permanently delete it
            var file = await fileRepository.GetByIdAsync(deletedItem.OriginalId, cancellationToken);

            if (file != null)
            {
                // Permanently remove from database
                // Note: Files on R2 storage are preserved for safety
                fileRepository.Remove(file);
                await fileRepository.SaveChangesAsync(cancellationToken);

                logger.LogInformation("Permanently deleted file {FileId} from database (R2 file preserved at: {FilePath})",
                    deletedItem.OriginalId, file.FilePath);
            }

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error permanently deleting file {FileId}", deletedItem.OriginalId);
            return false;
        }
    }
}
