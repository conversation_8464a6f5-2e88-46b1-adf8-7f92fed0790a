using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.RecycleBin;

/// <summary>
/// Command to permanently delete an item from recycle bin
/// </summary>
public class PermanentDeleteItemCommand : IRequest<BaseResponseModel<bool>>
{
    /// <summary>
    /// ID of the deleted item to permanently delete
    /// </summary>
    public Guid DeletedItemId { get; set; }

    /// <summary>
    /// User ID performing the permanent deletion
    /// </summary>
    public Guid UserId { get; set; }
}
