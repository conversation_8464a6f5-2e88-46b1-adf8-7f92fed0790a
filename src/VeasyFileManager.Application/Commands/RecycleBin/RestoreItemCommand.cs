using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.RecycleBin;

/// <summary>
/// Command to restore a deleted item from recycle bin
/// </summary>
public class RestoreItemCommand : IRequest<BaseResponseModel<bool>>
{
    /// <summary>
    /// ID of the deleted item to restore
    /// </summary>
    public Guid DeletedItemId { get; set; }

    /// <summary>
    /// User ID performing the restoration
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Optional new parent folder ID (if restoring to a different location)
    /// </summary>
    public Guid? NewParentFolderId { get; set; }
}
