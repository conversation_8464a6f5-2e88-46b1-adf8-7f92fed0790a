using FluentValidation;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Validator for CreateFolderCommand
/// </summary>
public class CreateFolderCommandValidator : AbstractValidator<CreateFolderCommand>
{
    public CreateFolderCommandValidator()
    {
        RuleFor(x => x.Name)
            .NotEmpty()
            .WithMessage("Folder name is required")
            .MaximumLength(255)
            .WithMessage("Folder name must not exceed 255 characters")
            .Must(BeValidFolderName)
            .WithMessage("Folder name contains invalid characters");

        RuleFor(x => x.Description)
            .MaximumLength(1000)
            .WithMessage("Description must not exceed 1000 characters")
            .When(x => !string.IsNullOrEmpty(x.Description));
    }

    private static bool BeValidFolderName(string folderName)
    {
        if (string.IsNullOrWhiteSpace(folderName))
            return false;

        // Check for invalid characters
        var invalidChars = System.IO.Path.GetInvalidFileNameChars();
        if (folderName.Any(c => invalidChars.Contains(c)))
            return false;

        // Check for reserved names (Windows)
        var reservedNames = new[] { "CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9" };
        if (reservedNames.Contains(folderName.ToUpperInvariant()))
            return false;

        // Check for names that start or end with spaces or dots
        if (folderName.StartsWith(' ') || folderName.EndsWith(' ') || folderName.StartsWith('.') || folderName.EndsWith('.'))
            return false;

        return true;
    }
} 