using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Handler for creating folder shares
/// </summary>
public class CreateFolderShareCommandHandler(
    IFolderRepository folderRepository,
    IMapper mapper,
    ILogger<CreateFolderShareCommandHandler> logger) : IRequestHandler<CreateFolderShareCommand, BaseResponseModel<FolderShareDto>>
{
    public async Task<BaseResponseModel<FolderShareDto>> Handle(CreateFolderShareCommand request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Creating share for folder {FolderId} by user {UserId}", 
            request.FolderId, request.UserId);

        try
        {
            // Verify folder exists
            var folder = await folderRepository.GetByIdAsync(request.FolderId, cancellationToken);
            if (folder == null)
            {
                return BaseResponseModel<FolderShareDto>.CreateError($"Folder with ID {request.FolderId} not found", 404, "FOLDER_NOT_FOUND");
            }

            // Check if user has permission to share the folder
            var hasPermission = await folderRepository.HasUserAccessAsync(
                request.FolderId,
                request.UserId,
                PermissionType.Share,
                cancellationToken);

            if (!hasPermission && folder.OwnerId != request.UserId)
            {
                return BaseResponseModel<FolderShareDto>.CreateError("You don't have permission to share this folder", 403, "PERMISSION_DENIED");
            }

            // Create the share
            var folderShare = FolderShare.Create(
                request.FolderId,
                request.UserId,
                request.ShareType,
                request.Password,
                request.ExpiresAt,
                request.MaxDownloads,
                request.IncludeSubfolders);

            // Add share to the folder (assuming we add a Shares collection to Folder entity)
            // For now, we'll save it through repository when we implement IFolderShareRepository

            // TODO: Add to repository when IFolderShareRepository is implemented
            // folderShareRepository.Add(folderShare);
            // await folderShareRepository.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Folder share {ShareId} created successfully with token {ShareToken}", 
                folderShare.Id, folderShare.ShareToken);

            // Map to DTO
            var dto = mapper.Map<FolderShareDto>(folderShare);
            dto.ShareUrl = $"/api/v1/shares/{folderShare.ShareToken}"; // Placeholder URL

            return BaseResponseModel<FolderShareDto>.CreateSuccess(dto, "Folder share created successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error creating share for folder {FolderId}", request.FolderId);
            return BaseResponseModel<FolderShareDto>.CreateError("An error occurred while creating the folder share", 500, "INTERNAL_ERROR");
        }
    }
} 