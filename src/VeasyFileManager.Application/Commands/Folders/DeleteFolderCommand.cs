using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Command to delete a folder
/// </summary>
public class DeleteFolderCommand : IRequest<BaseResponseModel<bool>>
{
    /// <summary>
    /// ID of the folder to delete
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// User ID performing the deletion
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Whether to force delete non-empty folder (true) or only empty folders (false)
    /// </summary>
    public bool Force { get; set; } = false;

    /// <summary>
    /// Whether to permanently delete the folder (true) or soft delete (false)
    /// </summary>
    public bool PermanentDelete { get; set; } = false;
} 