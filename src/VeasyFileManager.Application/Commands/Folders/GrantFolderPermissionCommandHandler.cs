using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Hand<PERSON> for granting folder permissions
/// </summary>
public class GrantFolderPermissionCommandHandler(
    IFolderRepository folderRepository,
    ILogger<GrantFolderPermissionCommandHandler> logger) : IRequestHandler<GrantFolderPermissionCommand, BaseResponseModel<Guid>>
{
    public async Task<BaseResponseModel<Guid>> Handle(GrantFolderPermissionCommand request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Granting {Permission} permission on folder {FolderId} to user {UserId}/role {RoleId} by user {GrantedBy}",
            request.Permission, request.FolderId, request.UserId, request.RoleId, request.GrantedBy);

        try
        {
            // Verify folder exists
            var folder = await folderRepository.GetByIdAsync(request.FolderId, cancellationToken);
            if (folder == null)
            {
                return BaseResponseModel<Guid>.CreateError($"Folder with ID {request.FolderId} not found", 404, "FOLDER_NOT_FOUND");
            }

            // Check if the granting user has permission to manage permissions
            var hasPermission = await folderRepository.HasUserAccessAsync(
                request.FolderId,
                request.GrantedBy,
                PermissionType.Share,
                cancellationToken);

            if (!hasPermission && folder.OwnerId != request.GrantedBy)
            {
                return BaseResponseModel<Guid>.CreateError("You don't have permission to grant permissions on this folder", 403, "PERMISSION_DENIED");
            }

            // Create the permission
            var permission = FolderPermission.Create(
                request.FolderId,
                request.UserId,
                request.RoleId,
                request.Permission,
                request.GrantedBy,
                request.ExpiresAt,
                request.InheritToChildren);

            // Add permission to folder
            folder.AddPermission(permission);

            // Save changes
            folderRepository.Update(folder);
            await folderRepository.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Permission {PermissionId} granted successfully on folder {FolderId}", 
                permission.Id, request.FolderId);

            return BaseResponseModel<Guid>.CreateSuccess(permission.Id, "Folder permission granted successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error granting permission on folder {FolderId}", request.FolderId);
            return BaseResponseModel<Guid>.CreateError("An error occurred while granting the folder permission", 500, "INTERNAL_ERROR");
        }
    }
} 