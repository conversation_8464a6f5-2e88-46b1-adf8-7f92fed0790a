using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Command to move a folder to a new location
/// </summary>
public class MoveFolderCommand : IRequest<BaseResponseModel<Unit>>
{
    /// <summary>
    /// ID of the folder to move
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// Target parent folder ID (null for root)
    /// </summary>
    public Guid? TargetParentFolderId { get; set; }

    /// <summary>
    /// User ID performing the move
    /// </summary>
    public Guid UserId { get; set; }
} 