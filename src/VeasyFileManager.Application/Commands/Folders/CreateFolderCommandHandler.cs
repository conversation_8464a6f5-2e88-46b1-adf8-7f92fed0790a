using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Handler for CreateFolderCommand
/// </summary>
public class CreateFolderCommandHandler(
    IFolderRepository folderRepository,
    ICurrentUserService currentUserService,
    IMapper mapper,
    ILogger<CreateFolderCommandHandler> logger)
    : IRequestHandler<CreateFolderCommand, BaseResponseModel<FolderDto>>
{
    private readonly IFolderRepository _folderRepository = folderRepository ?? throw new ArgumentNullException(nameof(folderRepository));
    private readonly ICurrentUserService _currentUserService = currentUserService ?? throw new ArgumentNullException(nameof(currentUserService));
    private readonly IMapper _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    private readonly ILogger<CreateFolderCommandHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<BaseResponseModel<FolderDto>> Handle(CreateFolderCommand request, CancellationToken cancellationToken)
    {
        // Ensure user is authenticated
        // if (!_currentUserService.IsAuthenticated)
        // {
        //     return BaseResponseModel<FolderDto>.CreateError("User must be authenticated to create folders", 401, "UNAUTHORIZED");
        // }

        // var currentUserId = _currentUserService.UserId!.Value;
        
        var currentUserId = Guid.NewGuid();
        _logger.LogInformation("Creating folder {FolderName} for user {UserId}", request.Name, currentUserId);

        try
        {
            if (request.ParentFolderId.HasValue)
            {
                var parentFolder = await _folderRepository.GetByIdAsync(request.ParentFolderId.Value, cancellationToken);
                if (parentFolder == null)
                {
                    return BaseResponseModel<FolderDto>.CreateError($"Parent folder with ID {request.ParentFolderId} not found", 404, "FOLDER_NOT_FOUND");
                }

                var hasAccess = await _folderRepository.HasUserAccessAsync(
                    request.ParentFolderId.Value, 
                    currentUserId, 
                    Domain.Enums.PermissionType.Write, 
                    cancellationToken);

                if (!hasAccess)
                {
                    return BaseResponseModel<FolderDto>.CreateError("You don't have permission to create folders in the specified parent folder", 403, "PERMISSION_DENIED");
                }
            }

            // Generate folder path
            string folderPath = await GenerateFolderPath(request.Name, request.ParentFolderId, currentUserId, cancellationToken);

            // Check if folder path is unique
            var isPathUnique = await _folderRepository.IsPathUniqueAsync(folderPath, currentUserId, null, cancellationToken);
            if (!isPathUnique)
            {
                return BaseResponseModel<FolderDto>.CreateError($"A folder with the name '{request.Name}' already exists in this location", 409, "FOLDER_EXISTS");
            }

            // Create folder entity
            var folder = Folder.Create(
                name: request.Name,
                ownerId: currentUserId,
                parentFolderId: request.ParentFolderId,
                parentPath: request.ParentFolderId.HasValue ? await GetParentPath(request.ParentFolderId.Value, cancellationToken) : null);

            // Save to repository
            await _folderRepository.AddAsync(folder, cancellationToken);
            await _folderRepository.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Folder {FolderName} created successfully with ID {FolderId}", request.Name, folder.Id);

            // Map to DTO and return
            var folderDto = _mapper.Map<FolderDto>(folder);
            return BaseResponseModel<FolderDto>.CreateSuccess(folderDto, "Folder created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating folder {FolderName} for user {UserId}", request.Name, currentUserId);
            return BaseResponseModel<FolderDto>.CreateError("An error occurred while creating the folder", 500, "INTERNAL_ERROR");
        }
    }

    private async Task<string> GenerateFolderPath(string folderName, Guid? parentFolderId, Guid userId, CancellationToken cancellationToken)
    {
        if (!parentFolderId.HasValue)
        {
            // Root folder
            return $"/{folderName}";
        }

        var parentFolder = await _folderRepository.GetByIdAsync(parentFolderId.Value, cancellationToken);
        if (parentFolder == null)
        {
            throw new ArgumentException($"Parent folder with ID {parentFolderId} not found");
        }

        return $"{parentFolder.Path}/{folderName}";
    }

    private async Task<string> GetParentPath(Guid parentFolderId, CancellationToken cancellationToken)
    {
        var parentFolder = await _folderRepository.GetByIdAsync(parentFolderId, cancellationToken);
        if (parentFolder == null)
        {
            throw new ArgumentException($"Parent folder with ID {parentFolderId} not found");
        }

        return parentFolder.Path;
    }
} 