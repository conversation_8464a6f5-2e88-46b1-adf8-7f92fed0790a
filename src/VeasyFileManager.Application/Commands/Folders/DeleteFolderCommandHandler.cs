using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Handler for DeleteFolderCommand
/// </summary>
public class DeleteFolderCommandHandler : IRequestHandler<DeleteFolderCommand, BaseResponseModel<bool>>
{
    private readonly IFolderRepository _folderRepository;
    private readonly IFileRepository _fileRepository;
    private readonly IDeletedItemRepository _deletedItemRepository;
    private readonly IFileStorageService _storageService;
    private readonly ILogger<DeleteFolderCommandHandler> _logger;

    public DeleteFolderCommandHandler(
        IFolderRepository folderRepository,
        IFileRepository fileRepository,
        IDeletedItemRepository deletedItemRepository,
        IFileStorageService storageService,
        ILogger<DeleteFolderCommandHandler> logger)
    {
        _folderRepository = folderRepository ?? throw new ArgumentNullException(nameof(folderRepository));
        _fileRepository = fileRepository ?? throw new ArgumentNullException(nameof(fileRepository));
        _deletedItemRepository = deletedItemRepository ?? throw new ArgumentNullException(nameof(deletedItemRepository));
        _storageService = storageService ?? throw new ArgumentNullException(nameof(storageService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<BaseResponseModel<bool>> Handle(DeleteFolderCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Deleting folder {FolderId} for user {UserId}, force: {Force}, permanent: {PermanentDelete}",
            request.FolderId, request.UserId, request.Force, request.PermanentDelete);

        try
        {
            // Get the folder
            var folder = await _folderRepository.GetByIdAsync(request.FolderId, cancellationToken);
            if (folder == null)
            {
                _logger.LogWarning("Folder {FolderId} not found", request.FolderId);
                return BaseResponseModel<bool>.CreateError("Folder not found", 404, "FOLDER_NOT_FOUND");
            }

            // Check if user has permission to delete the folder
            var hasPermission = await _folderRepository.HasUserAccessAsync(
                request.FolderId,
                request.UserId,
                PermissionType.Delete,
                cancellationToken);

            if (!hasPermission && folder.OwnerId != request.UserId)
            {
                return BaseResponseModel<bool>.CreateError("You don't have permission to delete this folder", 403, "PERMISSION_DENIED");
            }

            // Check if folder is empty (unless force delete is requested)
            if (!request.Force)
            {
                var files = await _fileRepository.GetByParentFolderAsync(request.FolderId, cancellationToken);
                var subfolders = await _folderRepository.GetUserFoldersAsync(
                    request.UserId,
                    request.FolderId,
                    1,
                    1,
                    cancellationToken: cancellationToken);

                if (files.Any() || subfolders.Folders.Any())
                {
                    return BaseResponseModel<bool>.CreateError("Cannot delete non-empty folder. Use force delete option to delete folder with contents.", 409, "FOLDER_NOT_EMPTY");
                }
            }

            if (request.PermanentDelete)
            {
                // Permanent delete: remove from database only
                // R2 storage files are NEVER deleted for data preservation
                // await DeleteFolderContentsFromStorage(request.FolderId, cancellationToken);
                _logger.LogInformation("Folder contents deletion from R2 storage is disabled for safety. All files preserved for folder: {FolderId}", request.FolderId);

                // Remove from database
                _folderRepository.Remove(folder);
                await _folderRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Folder {FolderId} permanently deleted from database (R2 files preserved)", request.FolderId);
                return BaseResponseModel<bool>.CreateSuccess(true, "Folder permanently deleted successfully (storage files preserved)");
            }
            else
            {
                // Soft delete: mark as deleted in database and create DeletedItem record
                folder.Delete();
                _folderRepository.Update(folder);

                // Create DeletedItem record for recycle bin
                var deletedItem = DeletedItem.CreateForFolder(folder, request.UserId);
                await _deletedItemRepository.AddAsync(deletedItem, cancellationToken);

                await _folderRepository.SaveChangesAsync(cancellationToken);
                await _deletedItemRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Folder {FolderId} soft deleted and added to recycle bin", request.FolderId);
                return BaseResponseModel<bool>.CreateSuccess(true, "Folder moved to recycle bin successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting folder {FolderId} for user {UserId}", request.FolderId, request.UserId);
            return BaseResponseModel<bool>.CreateError("An error occurred while deleting the folder", 500, "INTERNAL_ERROR");
        }
    }

    /// <summary>
    /// STORAGE DELETION METHOD - DISABLED FOR SAFETY
    /// This method is intentionally disabled to prevent any file deletion from R2 storage.
    /// Files on R2 should never be deleted to ensure data preservation.
    /// </summary>
    /*
    private async Task DeleteFolderContentsFromStorage(Guid folderId, CancellationToken cancellationToken)
    {
        try
        {
            // Get all files in this folder and delete them from storage
            var files = await _fileRepository.GetByParentFolderAsync(folderId, cancellationToken);

            foreach (var file in files)
            {
                if (!string.IsNullOrEmpty(file.FilePath))
                {
                    try
                    {
                        await _storageService.DeleteFileAsync(file.FilePath, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete file from storage: {FilePath}", file.FilePath);
                        // Continue deleting other files even if one fails
                    }
                }
            }

            // TODO: Recursively delete subfolders and their contents
            // This would require implementing a recursive deletion method
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error deleting folder contents from storage for folder {FolderId}", folderId);
            // Don't throw - continue with database deletion
        }
    }
    */
}
