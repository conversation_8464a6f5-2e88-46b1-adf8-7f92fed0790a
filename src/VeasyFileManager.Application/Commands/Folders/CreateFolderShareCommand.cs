using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Command to create a folder share link
/// </summary>
public class CreateFolderShareCommand : IRequest<BaseResponseModel<FolderShareDto>>
{
    /// <summary>
    /// ID of the folder to share
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// Type of share (Public, Password, UserSpecific)
    /// </summary>
    public ShareType ShareType { get; set; }

    /// <summary>
    /// Password for password-protected shares
    /// </summary>
    public string? Password { get; set; }

    /// <summary>
    /// Expiration date for the share (optional)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Maximum number of downloads allowed (optional)
    /// </summary>
    public int? MaxDownloads { get; set; }

    /// <summary>
    /// Whether to include subfolders in the share
    /// </summary>
    public bool IncludeSubfolders { get; set; } = true;

    /// <summary>
    /// User ID creating the share
    /// </summary>
    public Guid UserId { get; set; }
} 