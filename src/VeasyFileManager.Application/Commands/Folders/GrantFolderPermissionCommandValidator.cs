using FluentValidation;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Validator for GrantFolderPermissionCommand
/// </summary>
public class GrantFolderPermissionCommandValidator : AbstractValidator<GrantFolderPermissionCommand>
{
    public GrantFolderPermissionCommandValidator()
    {
        RuleFor(x => x.FolderId)
            .NotEmpty()
            .WithMessage("Folder ID is required");

        RuleFor(x => x.GrantedBy)
            .NotEmpty()
            .WithMessage("Granted by user ID is required");

        RuleFor(x => x)
            .Must(x => x.UserId.HasValue || x.RoleId.HasValue)
            .WithMessage("Either User ID or Role ID must be provided");

        RuleFor(x => x)
            .Must(x => !x.UserId.HasValue || !x.RoleId.HasValue)
            .WithMessage("Cannot specify both User ID and Role ID");

        RuleFor(x => x.ExpiresAt)
            .GreaterThan(DateTime.UtcNow)
            .When(x => x.ExpiresAt.HasValue)
            .WithMessage("Expiration date must be in the future");
    }
} 