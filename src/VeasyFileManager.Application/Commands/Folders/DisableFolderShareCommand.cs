using MediatR;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Command to disable a folder share link
/// </summary>
public class DisableFolderShareCommand : IRequest<BaseResponseModel<Unit>>
{
    /// <summary>
    /// Share token to disable
    /// </summary>
    public string ShareToken { get; set; } = null!;

    /// <summary>
    /// User ID disabling the share
    /// </summary>
    public Guid DisabledBy { get; set; }
} 