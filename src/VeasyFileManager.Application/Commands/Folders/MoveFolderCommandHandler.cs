using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Handler for moving folders to a new location
/// </summary>
public class MoveFolderCommandHandler(
    IFolderRepository folderRepository,
    ILogger<MoveFolderCommandHandler> logger) : IRequestHandler<MoveFolderCommand, BaseResponseModel<Unit>>
{
    public async Task<BaseResponseModel<Unit>> Handle(MoveFolderCommand request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Moving folder {FolderId} to parent {TargetParentFolderId} by user {UserId}",
            request.FolderId, request.TargetParentFolderId, request.UserId);

        try
        {
            // Get the folder to move
            var folder = await folderRepository.GetByIdAsync(request.FolderId, cancellationToken);
            if (folder == null)
            {
                return BaseResponseModel<Unit>.CreateError($"Folder with ID {request.FolderId} not found", 404, "FOLDER_NOT_FOUND");
            }

            // Check if user has permission to move the folder
            var hasPermission = await folderRepository.HasUserAccessAsync(
                request.FolderId,
                request.UserId,
                PermissionType.Write,
                cancellationToken);

            if (!hasPermission && folder.OwnerId != request.UserId)
            {
                return BaseResponseModel<Unit>.CreateError("You don't have permission to move this folder", 403, "PERMISSION_DENIED");
            }

            // Validate target parent folder if specified
            string? targetParentPath = null;
            if (request.TargetParentFolderId.HasValue)
            {
                var targetParent = await folderRepository.GetByIdAsync(request.TargetParentFolderId.Value, cancellationToken);
                if (targetParent == null)
                {
                    return BaseResponseModel<Unit>.CreateError($"Target parent folder with ID {request.TargetParentFolderId} not found", 404, "TARGET_FOLDER_NOT_FOUND");
                }

                // Check if user has permission to add folders to the target parent
                var hasTargetPermission = await folderRepository.HasUserAccessAsync(
                    request.TargetParentFolderId.Value,
                    request.UserId,
                    PermissionType.Write,
                    cancellationToken);

                if (!hasTargetPermission && targetParent.OwnerId != request.UserId)
                {
                    return BaseResponseModel<Unit>.CreateError("You don't have permission to move folders to the specified parent folder", 403, "PERMISSION_DENIED");
                }

                // Prevent moving folder into itself or its descendants
                if (request.TargetParentFolderId == request.FolderId)
                {
                    return BaseResponseModel<Unit>.CreateError("Cannot move folder into itself", 400, "INVALID_OPERATION");
                }

                // Check if target parent is a descendant of the folder being moved
                var hierarchy = await folderRepository.GetFolderHierarchyAsync(request.FolderId, cancellationToken);
                if (hierarchy.Any(f => f.Id == request.TargetParentFolderId.Value))
                {
                    return BaseResponseModel<Unit>.CreateError("Cannot move folder into its own subfolder", 400, "INVALID_OPERATION");
                }

                targetParentPath = targetParent.Path;
            }

            // Check for name conflicts in the target location
            var newPath = request.TargetParentFolderId.HasValue 
                ? $"{targetParentPath}/{folder.Name}"
                : $"/{request.UserId}/{folder.Name}";

            var isPathUnique = await folderRepository.IsPathUniqueAsync(newPath, request.UserId, request.FolderId, cancellationToken);
            if (!isPathUnique)
            {
                return BaseResponseModel<Unit>.CreateError($"A folder with the name '{folder.Name}' already exists in the target location", 409, "FOLDER_EXISTS");
            }

            // Perform the move
            folder.Move(request.TargetParentFolderId, targetParentPath);
            folderRepository.Update(folder);
            await folderRepository.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully moved folder {FolderId} to parent {TargetParentFolderId}",
                request.FolderId, request.TargetParentFolderId);

            return BaseResponseModel<Unit>.CreateSuccess(Unit.Value, "Folder moved successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error moving folder {FolderId} to parent {TargetParentFolderId} by user {UserId}",
                request.FolderId, request.TargetParentFolderId, request.UserId);
            return BaseResponseModel<Unit>.CreateError("An error occurred while moving the folder", 500, "INTERNAL_ERROR");
        }
    }
} 