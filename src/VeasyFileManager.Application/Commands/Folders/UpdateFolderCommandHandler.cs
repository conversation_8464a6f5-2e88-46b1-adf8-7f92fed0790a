using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Handler for UpdateFolderCommand
/// </summary>
public class UpdateFolderCommandHandler : IRequestHandler<UpdateFolderCommand, BaseResponseModel<FolderDto>>
{
    private readonly IFolderRepository _folderRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<UpdateFolderCommandHandler> _logger;

    public UpdateFolderCommandHandler(
        IFolderRepository folderRepository,
        IMapper mapper,
        ILogger<UpdateFolderCommandHandler> logger)
    {
        _folderRepository = folderRepository ?? throw new ArgumentNullException(nameof(folderRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<BaseResponseModel<FolderDto>> Handle(UpdateFolderCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Updating folder {FolderId} for user {UserId}", request.FolderId, request.UserId);

        try
        {
            // Get the folder
            var folder = await _folderRepository.GetByIdAsync(request.FolderId, cancellationToken);
            if (folder == null)
            {
                return BaseResponseModel<FolderDto>.CreateError($"Folder with ID {request.FolderId} not found", 404, "FOLDER_NOT_FOUND");
            }

            // Check if user has permission to update the folder
            var hasPermission = await _folderRepository.HasUserAccessAsync(
                request.FolderId, 
                request.UserId, 
                PermissionType.Write, 
                cancellationToken);

            if (!hasPermission && folder.OwnerId != request.UserId)
            {
                return BaseResponseModel<FolderDto>.CreateError("You don't have permission to update this folder", 403, "PERMISSION_DENIED");
            }

            // Validate new parent folder if moving
            if (request.ParentFolderId.HasValue && request.ParentFolderId != folder.ParentFolderId)
            {
                // Prevent moving folder into itself or its descendants
                if (request.ParentFolderId == request.FolderId)
                {
                    return BaseResponseModel<FolderDto>.CreateError("Cannot move folder into itself", 400, "INVALID_OPERATION");
                }

                // Check if target folder exists
                var targetFolder = await _folderRepository.GetByIdAsync(request.ParentFolderId.Value, cancellationToken);
                if (targetFolder == null)
                {
                    return BaseResponseModel<FolderDto>.CreateError($"Target folder with ID {request.ParentFolderId} not found", 404, "TARGET_FOLDER_NOT_FOUND");
                }

                // Check if user has write access to target folder
                var hasTargetFolderAccess = await _folderRepository.HasUserAccessAsync(
                    request.ParentFolderId.Value, 
                    request.UserId, 
                    PermissionType.Write, 
                    cancellationToken);

                if (!hasTargetFolderAccess)
                {
                    return BaseResponseModel<FolderDto>.CreateError("You don't have permission to move folder to the target location", 403, "PERMISSION_DENIED");
                }

                // TODO: Check for circular references (moving folder into its own descendant)
                // This would require a method to check folder hierarchy
            }

            // Update folder properties
            var hasChanges = false;

            if (!string.IsNullOrEmpty(request.Name) && request.Name != folder.Name)
            {
                folder.Rename(request.Name);
                hasChanges = true;
            }

            if (request.ParentFolderId.HasValue && request.ParentFolderId != folder.ParentFolderId)
            {
                // Get parent path for the move operation
                string? parentPath = null;
                if (request.ParentFolderId.HasValue)
                {
                    var parentFolder = await _folderRepository.GetByIdAsync(request.ParentFolderId.Value, cancellationToken);
                    parentPath = parentFolder?.Path;
                }

                folder.Move(request.ParentFolderId, parentPath);
                hasChanges = true;
            }

            // TODO: Handle description update when description field is added to Folder entity
            if (!string.IsNullOrEmpty(request.Description))
            {
                _logger.LogInformation("Description update requested for folder {FolderId}: {Description}", 
                    request.FolderId, request.Description);
            }

            if (hasChanges)
            {
                _folderRepository.Update(folder);
                await _folderRepository.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Folder {FolderId} updated successfully", request.FolderId);
            }
            else
            {
                _logger.LogInformation("No changes detected for folder {FolderId}", request.FolderId);
            }

            // Map to DTO and return
            var folderDto = _mapper.Map<FolderDto>(folder);
            folderDto.Permissions = new List<string>(); // Placeholder for now
            folderDto.FileCount = 0; // Placeholder for now
            folderDto.SubfolderCount = 0; // Placeholder for now

            return BaseResponseModel<FolderDto>.CreateSuccess(folderDto, hasChanges ? "Folder updated successfully" : "No changes were necessary");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating folder {FolderId} for user {UserId}", request.FolderId, request.UserId);
            return BaseResponseModel<FolderDto>.CreateError("An error occurred while updating the folder", 500, "INTERNAL_ERROR");
        }
    }
} 