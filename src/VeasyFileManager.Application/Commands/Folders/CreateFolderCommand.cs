using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Command to create a new folder
/// </summary>
public class CreateFolderCommand : IRequest<BaseResponseModel<FolderDto>>
{
    /// <summary>
    /// Name of the folder
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// Parent folder ID (null for root)
    /// </summary>
    public Guid? ParentFolderId { get; set; }

    /// <summary>
    /// Description of the folder
    /// </summary>
    public string? Description { get; set; }
} 