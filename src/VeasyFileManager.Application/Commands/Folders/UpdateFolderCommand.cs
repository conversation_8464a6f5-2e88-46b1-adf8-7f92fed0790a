using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Commands.Folders;

/// <summary>
/// Command to update folder metadata
/// </summary>
public class UpdateFolderCommand : IRequest<BaseResponseModel<FolderDto>>
{
    /// <summary>
    /// ID of the folder to update
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// User ID performing the update
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// New name for the folder (optional)
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// New parent folder ID (optional, for moving the folder)
    /// </summary>
    public Guid? ParentFolderId { get; set; }

    /// <summary>
    /// New description for the folder (optional)
    /// </summary>
    public string? Description { get; set; }
} 