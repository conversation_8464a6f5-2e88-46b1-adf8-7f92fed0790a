using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Query to get a shared file by token
/// </summary>
public class GetSharedFileQuery : IRequest<BaseResponseModel<FileDto?>>
{
    /// <summary>
    /// Share token to access the file
    /// </summary>
    public string ShareToken { get; set; } = null!;

    /// <summary>
    /// Password for password-protected shares
    /// </summary>
    public string? Password { get; set; }
} 