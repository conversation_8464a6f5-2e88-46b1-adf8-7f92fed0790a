using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Handler for GetUserFilesQuery
/// </summary>
public class GetUserFilesQueryHandler : IRequestHandler<GetUserFilesQuery, BaseResponseModel<PagedResult<FileDto>>>
{
    private readonly IFileRepository _fileRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetUserFilesQueryHandler> _logger;

    public GetUserFilesQueryHandler(
        IFileRepository fileRepository,
        IMapper mapper,
        ILogger<GetUserFilesQueryHandler> logger)
    {
        _fileRepository = fileRepository ?? throw new ArgumentNullException(nameof(fileRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<BaseResponseModel<PagedResult<FileDto>>> Handle(GetUserFilesQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting files for user {UserId}, page {Page}, size {PageSize}", 
            request.UserId, request.Page, request.PageSize);

        try
        {
            // Validate page and page size
            if (request.Page < 1)
                request.Page = 1;

            if (request.PageSize < 1 || request.PageSize > 100)
                request.PageSize = 20;

            // Get paginated files from repository
            var (files, totalCount) = await _fileRepository.GetUserFilesAsync(
                userId: request.UserId,
                parentFolderId: request.ParentFolderId,
                page: request.Page,
                pageSize: request.PageSize,
                searchTerm: request.SearchTerm,
                sortBy: request.SortBy,
                sortDirection: request.SortDirection,
                cancellationToken: cancellationToken);

            // Map to DTOs
            var fileDtos = _mapper.Map<List<FileDto>>(files);

            // TODO: Add permission information to each file DTO
            // This would require additional queries to check user permissions
            foreach (var fileDto in fileDtos)
            {
                fileDto.Permissions = new List<string>(); // Placeholder for now
                fileDto.IsShared = false; // Placeholder for now
            }

            var result = PagedResult<FileDto>.Create(
                items: fileDtos,
                totalCount: totalCount,
                page: request.Page,
                pageSize: request.PageSize);

            _logger.LogInformation("Retrieved {FileCount} files for user {UserId} (total: {TotalCount})", 
                fileDtos.Count, request.UserId, totalCount);

            return BaseResponseModel<PagedResult<FileDto>>.CreateSuccess(result, "Files retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting files for user {UserId}", request.UserId);
            return BaseResponseModel<PagedResult<FileDto>>.CreateError("An error occurred while retrieving files", 500, "INTERNAL_ERROR");
        }
    }
} 