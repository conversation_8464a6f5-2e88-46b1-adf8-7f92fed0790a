using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Query to get user files with pagination and filtering
/// </summary>
public class GetUserFilesQuery : IRequest<BaseResponseModel<PagedResult<FileDto>>>
{
    /// <summary>
    /// User ID to get files for
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Parent folder ID (null for root files)
    /// </summary>
    public Guid? ParentFolderId { get; set; }

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Search term for filtering files
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Field to sort by (Name, CreatedAt, UpdatedAt, FileSize)
    /// </summary>
    public string SortBy { get; set; } = "CreatedAt";

    /// <summary>
    /// Sort direction (ASC, DESC)
    /// </summary>
    public string SortDirection { get; set; } = "DESC";

    /// <summary>
    /// File type filter (MIME type)
    /// </summary>
    public string? FileType { get; set; }

    /// <summary>
    /// Minimum file size in bytes
    /// </summary>
    public long? MinSize { get; set; }

    /// <summary>
    /// Maximum file size in bytes
    /// </summary>
    public long? MaxSize { get; set; }
}

 