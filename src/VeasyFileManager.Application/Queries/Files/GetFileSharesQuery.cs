using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Query to get all shares for a file
/// </summary>
public class GetFileSharesQuery : IRequest<BaseResponseModel<List<FileShareDto>>>
{
    /// <summary>
    /// File ID to get shares for
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// User ID requesting the shares
    /// </summary>
    public Guid UserId { get; set; }
} 