using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Handler for streaming files with range support
/// </summary>
public class StreamFileQueryHandler(
    IFileRepository fileRepository,
    IFileStorageService storageService,
    ILogger<StreamFileQueryHandler> logger)
    : IRequestHandler<StreamFileQuery, BaseResponseModel<FileStreamResponse>>
{
    public async Task<BaseResponseModel<FileStreamResponse>> Handle(StreamFileQuery request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Starting file stream for user {UserId}, file: {FileId}, range: {Range}",
                request.UserId, request.FileId, request.Range != null ? $"{request.Range.Start}-{request.Range.End}" : "full");

            // Get file entity
            var file = await fileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (file == null)
            {
                return BaseResponseModel<FileStreamResponse>.CreateError($"File with ID {request.FileId} not found", 404, "FILE_NOT_FOUND");
            }

            // Check if user has read access to the file
            var hasAccess = await fileRepository.HasUserAccessAsync(request.UserId, file.Id, PermissionType.Read, cancellationToken);
            if (!hasAccess)
            {
                return BaseResponseModel<FileStreamResponse>.CreateError("You don't have permission to view this file", 403, "PERMISSION_DENIED");
            }

            // Validate range if provided
            if (request.Range != null && !request.Range.IsValid(file.FileSize))
            {
                return BaseResponseModel<FileStreamResponse>.CreateError(
                    $"Invalid range {request.Range.Start}-{request.Range.End} for file size {file.FileSize}",
                    416, "RANGE_NOT_SATISFIABLE");
            }

            // Get file stream with range support
            var streamResponse = await storageService.GetFileStreamAsync(file.FilePath!, request.Range, cancellationToken);

            logger.LogInformation("File stream prepared successfully: {FileId} for user {UserId}, partial: {IsPartial}",
                request.FileId, request.UserId, streamResponse.IsPartialContent);

            return BaseResponseModel<FileStreamResponse>.CreateSuccess(streamResponse, "File stream prepared successfully");
        }
        catch (ArgumentOutOfRangeException ex)
        {
            logger.LogWarning(ex, "Invalid range request for file {FileId} by user {UserId}",
                request.FileId, request.UserId);
            return BaseResponseModel<FileStreamResponse>.CreateError("Invalid range request", 416, "RANGE_NOT_SATISFIABLE");
        }
        catch (FileNotFoundException ex)
        {
            logger.LogWarning(ex, "File not found in storage: {FileId} for user {UserId}",
                request.FileId, request.UserId);
            return BaseResponseModel<FileStreamResponse>.CreateError("File not found in storage", 404, "FILE_NOT_FOUND_IN_STORAGE");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error streaming file {FileId} for user {UserId}",
                request.FileId, request.UserId);
            return BaseResponseModel<FileStreamResponse>.CreateError("An error occurred while preparing the file stream", 500, "INTERNAL_ERROR");
        }
    }
}
