using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Query to download a file
/// </summary>
public class DownloadFileQuery : IRequest<BaseResponseModel<FileDownloadDto>>
{
    /// <summary>
    /// ID of the file to download
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// User ID requesting the download
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Whether to generate a presigned URL instead of streaming the file
    /// </summary>
    public bool GeneratePresignedUrl { get; set; } = false;

    /// <summary>
    /// Expiration time for presigned URL (default 1 hour)
    /// </summary>
    public TimeSpan PresignedUrlExpiration { get; set; } = TimeSpan.FromHours(1);
} 