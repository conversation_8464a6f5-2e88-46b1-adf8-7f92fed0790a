using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Query to get detailed information about a file
/// </summary>
public class GetFileDetailsQuery : IRequest<BaseResponseModel<FileDto?>>
{
    /// <summary>
    /// ID of the file to retrieve
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// User ID requesting the file details
    /// </summary>
    public Guid UserId { get; set; }
} 