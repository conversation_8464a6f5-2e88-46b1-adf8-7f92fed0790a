using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Handler for getting file shares
/// </summary>
public class GetFileSharesQueryHandler : IRequestHandler<GetFileSharesQuery, BaseResponseModel<List<FileShareDto>>>
{
    private readonly IFileRepository _fileRepository;
    private readonly IFilePermissionService _permissionService;
    private readonly IMapper _mapper;
    private readonly ILogger<GetFileSharesQueryHandler> _logger;

    public GetFileSharesQueryHandler(
        IFileRepository fileRepository,
        IFilePermissionService permissionService,
        IMapper mapper,
        ILogger<GetFileSharesQueryHandler> logger)
    {
        _fileRepository = fileRepository;
        _permissionService = permissionService;
        _mapper = mapper;
        _logger = logger;
    }

    public async Task<BaseResponseModel<List<FileShareDto>>> Handle(GetFileSharesQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting shares for file {FileId} requested by user {UserId}", 
            request.FileId, request.UserId);

        try
        {
            // Check if user has permission to view shares for this file
            var hasPermission = await _permissionService.HasPermissionAsync(
                request.UserId, request.FileId, PermissionType.Share, cancellationToken);

            if (!hasPermission)
            {
                // Check if user is the file owner
                var file = await _fileRepository.GetByIdAsync(request.FileId, cancellationToken);
                if (file == null)
                {
                    _logger.LogWarning("File {FileId} not found", request.FileId);
                    return BaseResponseModel<List<FileShareDto>>.CreateError("File not found", 404, "FILE_NOT_FOUND");
                }

                if (file.OwnerId != request.UserId)
                {
                    _logger.LogWarning("User {UserId} does not have permission to view shares for file {FileId}", 
                        request.UserId, request.FileId);
                    return BaseResponseModel<List<FileShareDto>>.CreateError("You don't have permission to view shares for this file", 403, "PERMISSION_DENIED");
                }
            }

            // Get file shares
            var fileShares = await _fileRepository.GetFileSharesAsync(request.FileId, cancellationToken);

            // Map to DTOs
            var sharesDtos = fileShares.Select(share => new FileShareDto
            {
                Id = share.Id,
                FileId = share.FileId,
                ShareToken = share.ShareToken,
                ShareType = share.ShareType,
                SharedBy = share.SharedBy,
                CreatedAt = share.CreatedAt,
                ExpiresAt = share.ExpiresAt,
                MaxDownloads = share.MaxDownloads,
                DownloadCount = share.DownloadCount,
                IsActive = share.IsActive,
                HasPassword = share.ShareType == ShareType.Password
            }).ToList();

            _logger.LogInformation("Retrieved {Count} shares for file {FileId}", 
                sharesDtos.Count, request.FileId);

            return BaseResponseModel<List<FileShareDto>>.CreateSuccess(sharesDtos, "File shares retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shares for file {FileId}", request.FileId);
            return BaseResponseModel<List<FileShareDto>>.CreateError("An error occurred while retrieving file shares", 500, "INTERNAL_ERROR");
        }
    }
} 