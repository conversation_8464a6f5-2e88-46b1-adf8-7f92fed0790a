using MediatR;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Query to stream a file with optional range support for large files
/// </summary>
public class StreamFileQuery : IRequest<BaseResponseModel<FileStreamResponse>>
{
    /// <summary>
    /// ID of the file to stream
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// User ID requesting the stream
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Optional byte range for partial content (supports video/audio streaming and large files)
    /// </summary>
    public ByteRange? Range { get; set; }

    /// <summary>
    /// Whether to include cache headers for optimization
    /// </summary>
    public bool EnableCaching { get; set; } = true;

    /// <summary>
    /// Maximum duration to cache the content (default 1 hour)
    /// </summary>
    public TimeSpan CacheDuration { get; set; } = TimeSpan.FromHours(1);
}
