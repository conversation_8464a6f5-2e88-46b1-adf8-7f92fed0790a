using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Handler for getting shared files by token
/// </summary>
public class GetSharedFileQueryHandler(
    IFileRepository fileRepository,
    IMapper mapper,
    ILogger<GetSharedFileQueryHandler> logger)
    : IRequestHandler<GetSharedFileQuery, BaseResponseModel<FileDto?>>
{
    public async Task<BaseResponseModel<FileDto?>> Handle(GetSharedFileQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Accessing shared file with token {ShareToken}", request.ShareToken);

        try
        {
            // Get the file share by token
            var fileShare = await fileRepository.GetFileShareByTokenAsync(request.ShareToken, cancellationToken);
            if (fileShare == null)
            {
                logger.LogWarning("File share not found for token {ShareToken}", request.ShareToken);
                return BaseResponseModel<FileDto?>.CreateError("Share link not found", 404, "SHARE_NOT_FOUND");
            }

            // Check if share is active
            if (!fileShare.IsActive)
            {
                logger.LogWarning("File share with token {ShareToken} is not active", request.ShareToken);
                return BaseResponseModel<FileDto?>.CreateError("This share link is no longer active", 410, "SHARE_INACTIVE");
            }

            // Check if share has expired
            if (fileShare.ExpiresAt.HasValue && fileShare.ExpiresAt.Value < DateTime.UtcNow)
            {
                logger.LogWarning("File share with token {ShareToken} has expired", request.ShareToken);
                return BaseResponseModel<FileDto?>.CreateError("This share link has expired", 410, "SHARE_EXPIRED");
            }

            // Check download limit
            if (fileShare.MaxDownloads.HasValue && fileShare.DownloadCount >= fileShare.MaxDownloads.Value)
            {
                logger.LogWarning("File share with token {ShareToken} has reached download limit", request.ShareToken);
                return BaseResponseModel<FileDto?>.CreateError("This share link has reached its download limit", 410, "SHARE_LIMIT_REACHED");
            }

            // Validate password if required
            if (fileShare.ShareType == ShareType.Password)
            {
                if (string.IsNullOrWhiteSpace(request.Password) || !fileShare.ValidatePassword(request.Password))
                {
                    logger.LogWarning("Invalid password provided for password-protected share {ShareToken}", request.ShareToken);
                    return BaseResponseModel<FileDto?>.CreateError("Invalid password for this share", 401, "INVALID_PASSWORD");
                }
            }

            // Get the file
            var file = await fileRepository.GetByIdAsync(fileShare.FileId, cancellationToken);
            if (file == null)
            {
                logger.LogError("File {FileId} not found for share token {ShareToken}", fileShare.FileId, request.ShareToken);
                return BaseResponseModel<FileDto?>.CreateError("The shared file is no longer available", 404, "FILE_NOT_FOUND");
            }

            // Increment download count
            fileShare.IncrementDownloadCount();
            await fileRepository.SaveChangesAsync(cancellationToken);

            // Map to DTO
            var fileDto = mapper.Map<FileDto>(file);
            fileDto.IsShared = true;

            logger.LogInformation("Shared file {FileId} accessed successfully via token {ShareToken}", 
                file.Id, request.ShareToken);

            return BaseResponseModel<FileDto?>.CreateSuccess(fileDto, "Shared file accessed successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error accessing shared file with token {ShareToken}", request.ShareToken);
            return BaseResponseModel<FileDto?>.CreateError("An error occurred while accessing the shared file", 500, "INTERNAL_ERROR");
        }
    }
} 