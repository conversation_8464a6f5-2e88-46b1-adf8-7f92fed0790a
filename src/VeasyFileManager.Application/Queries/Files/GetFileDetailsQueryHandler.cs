using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Handler for GetFileDetailsQuery
/// </summary>
public class GetFileDetailsQueryHandler(
    IFileRepository fileRepository,
    IMapper mapper,
    ILogger<GetFileDetailsQueryHandler> logger)
    : IRequestHandler<GetFileDetailsQuery, BaseResponseModel<FileDto?>>
{
    private readonly IFileRepository _fileRepository = fileRepository ?? throw new ArgumentNullException(nameof(fileRepository));
    private readonly IMapper _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    private readonly ILogger<GetFileDetailsQueryHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

    public async Task<BaseResponseModel<FileDto?>> Handle(GetFileDetailsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting file details for file {FileId} and user {UserId}", 
            request.FileId, request.UserId);

        try
        {
            // Get the file with permissions and shares
            var file = await _fileRepository.GetWithPermissionsAndSharesAsync(request.FileId, cancellationToken);
            if (file == null)
            {
                _logger.LogWarning("File {FileId} not found", request.FileId);
                return BaseResponseModel<FileDto?>.CreateError("File not found", 404, "FILE_NOT_FOUND");
            }

            // Check if user has permission to view the file
            var hasPermission = await _fileRepository.HasUserAccessAsync(
                request.UserId, 
                request.FileId, 
                PermissionType.Read, 
                cancellationToken);

            if (!hasPermission && file.OwnerId != request.UserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to view file {FileId}", request.UserId, request.FileId);
                return BaseResponseModel<FileDto?>.CreateError("You don't have permission to view this file", 403, "PERMISSION_DENIED");
            }

            // Map to DTO
            var fileDto = _mapper.Map<FileDto>(file);

            // TODO: Add permission information to the DTO
            // This would require additional logic to map permissions and shares
            fileDto.Permissions = new List<string>(); // Placeholder for now
            fileDto.IsShared = file.Shares?.Any(s => s.IsActive) ?? false;

            _logger.LogInformation("File details retrieved successfully for file {FileId}", request.FileId);
            return BaseResponseModel<FileDto?>.CreateSuccess(fileDto, "File details retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file details for file {FileId} and user {UserId}", request.FileId, request.UserId);
            return BaseResponseModel<FileDto?>.CreateError("An error occurred while retrieving file details", 500, "INTERNAL_ERROR");
        }
    }
} 