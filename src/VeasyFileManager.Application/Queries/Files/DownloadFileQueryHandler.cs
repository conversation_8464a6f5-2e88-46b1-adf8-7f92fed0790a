using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Handler for downloading files
/// </summary>
public class DownloadFileQueryHandler(
    IFileRepository fileRepository,
    IFileStorageService storageService,
    ILogger<DownloadFileQueryHandler> logger)
    : IRequestHandler<DownloadFileQuery, BaseResponseModel<FileDownloadDto>>
{
    public async Task<BaseResponseModel<FileDownloadDto>> Handle(DownloadFileQuery request, CancellationToken cancellationToken)
    {
        try
        {
            logger.LogInformation("Starting file download for user {UserId}, file: {FileId}", 
                request.UserId, request.FileId);

            // Get file entity
            var file = await fileRepository.GetByIdAsync(request.FileId, cancellationToken);
            if (file == null)
            {
                return BaseResponseModel<FileDownloadDto>.CreateError($"File with ID {request.FileId} not found", 404, "FILE_NOT_FOUND");
            }

            // Check if user has read access to the file
            var hasAccess = await fileRepository.HasUserAccessAsync(request.UserId, file.Id, PermissionType.Read, cancellationToken);
            if (!hasAccess)
            {
                return BaseResponseModel<FileDownloadDto>.CreateError("You don't have permission to download this file", 403, "PERMISSION_DENIED");
            }

            var downloadDto = new FileDownloadDto
            {
                FileId = file.Id,
                FileName = file.Name,
                ContentType = file.MimeType,
                FileSize = file.FileSize
            };

            if (request.GeneratePresignedUrl)
            {
                // Generate presigned URL
                var presignedUrl = await storageService.GeneratePresignedUrlAsync(
                    file.FilePath!, 
                    request.PresignedUrlExpiration, 
                    cancellationToken);

                downloadDto.PresignedUrl = presignedUrl;
                downloadDto.UrlExpiration = DateTime.UtcNow.Add(request.PresignedUrlExpiration);

                logger.LogInformation("Presigned URL generated for file {FileId} for user {UserId}", 
                    request.FileId, request.UserId);
            }
            else
            {
                // Get file stream
                var fileStream = await storageService.DownloadFileAsync(file.FilePath!, cancellationToken);
                downloadDto.ContentStream = fileStream;

                logger.LogInformation("File stream prepared for download: {FileId} for user {UserId}", 
                    request.FileId, request.UserId);
            }

            // TODO: Log download activity for analytics
            // TODO: Increment download count if needed

            return BaseResponseModel<FileDownloadDto>.CreateSuccess(downloadDto, "File prepared for download successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error downloading file {FileId} for user {UserId}", 
                request.FileId, request.UserId);
            return BaseResponseModel<FileDownloadDto>.CreateError("An error occurred while preparing the file for download", 500, "INTERNAL_ERROR");
        }
    }
} 