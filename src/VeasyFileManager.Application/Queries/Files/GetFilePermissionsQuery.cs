using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Files;

/// <summary>
/// Query to get all permissions for a file
/// </summary>
public class GetFilePermissionsQuery : IRequest<BaseResponseModel<List<FilePermissionDto>>>
{
    /// <summary>
    /// File ID to get permissions for
    /// </summary>
    public Guid FileId { get; set; }

    /// <summary>
    /// User ID making the request (to check if they have permission to view permissions)
    /// </summary>
    public Guid UserId { get; set; }
} 