using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Folders;

/// <summary>
/// Query to get detailed information about a folder
/// </summary>
public class GetFolderDetailsQuery : IRequest<BaseResponseModel<FolderDto?>>
{
    /// <summary>
    /// ID of the folder to retrieve
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// User ID requesting the folder details
    /// </summary>
    public Guid UserId { get; set; }
} 