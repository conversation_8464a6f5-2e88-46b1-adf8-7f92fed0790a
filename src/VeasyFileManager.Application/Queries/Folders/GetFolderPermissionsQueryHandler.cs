using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Folders;

/// <summary>
/// Handler for getting folder permissions
/// </summary>
public class GetFolderPermissionsQueryHandler(
    IFolderRepository folderRepository,
    IMapper mapper,
    ILogger<GetFolderPermissionsQueryHandler> logger) : IRequestHandler<GetFolderPermissionsQuery, BaseResponseModel<List<FolderPermissionDto>>>
{
    public async Task<BaseResponseModel<List<FolderPermissionDto>>> Handle(GetFolderPermissionsQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Getting permissions for folder {FolderId} by user {RequesterId}", 
            request.FolderId, request.RequesterId);

        try
        {
            // Verify folder exists
            var folder = await folderRepository.GetByIdAsync(request.FolderId, cancellationToken);
            if (folder == null)
            {
                return BaseResponseModel<List<FolderPermissionDto>>.CreateError($"Folder with ID {request.FolderId} not found", 404, "FOLDER_NOT_FOUND");
            }

            // Check if user has permission to view permissions
            var hasPermission = await folderRepository.HasUserAccessAsync(
                request.FolderId,
                request.RequesterId,
                PermissionType.Read,
                cancellationToken);

            if (!hasPermission && folder.OwnerId != request.RequesterId)
            {
                return BaseResponseModel<List<FolderPermissionDto>>.CreateError("You don't have permission to view permissions on this folder", 403, "PERMISSION_DENIED");
            }

            // Get folder permissions
            var permissions = folder.Permissions.Where(p => p.IsActive).ToList();

            // Map to DTOs
            var permissionDtos = mapper.Map<List<FolderPermissionDto>>(permissions);

            logger.LogInformation("Retrieved {Count} permissions for folder {FolderId}", 
                permissionDtos.Count, request.FolderId);

            return BaseResponseModel<List<FolderPermissionDto>>.CreateSuccess(permissionDtos, "Folder permissions retrieved successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting permissions for folder {FolderId}", request.FolderId);
            return BaseResponseModel<List<FolderPermissionDto>>.CreateError("An error occurred while retrieving folder permissions", 500, "INTERNAL_ERROR");
        }
    }
} 