using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Folders;

/// <summary>
/// Query to get all shares for a folder
/// </summary>
public class GetFolderSharesQuery : IRequest<BaseResponseModel<List<FolderShareDto>>>
{
    /// <summary>
    /// Folder ID to get shares for
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// User ID requesting the shares
    /// </summary>
    public Guid UserId { get; set; }
} 