using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Folders;

/// <summary>
/// Handler for getting user folders with pagination and filtering
/// </summary>
public class GetUserFoldersQueryHandler(
    IFolderRepository folderRepository,
    IMapper mapper,
    ILogger<GetUserFoldersQueryHandler> logger) : IRequestHandler<GetUserFoldersQuery, BaseResponseModel<PagedResult<FolderDto>>>
{
    public async Task<BaseResponseModel<PagedResult<FolderDto>>> Handle(GetUserFoldersQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Getting folders for user {UserId}, parent: {ParentFolderId}, page: {Page}, pageSize: {PageSize}",
            request.UserId, request.ParentFolderId, request.Page, request.PageSize);

        try
        {
            // Validate pagination parameters
            if (request.Page < 1)
                request.Page = 1;

            if (request.PageSize < 1 || request.PageSize > 100)
                request.PageSize = 20;

            var (folders, totalCount) = await folderRepository.GetUserFoldersAsync(
                request.UserId,
                request.ParentFolderId,
                request.Page,
                request.PageSize,
                request.Search,
                request.SortBy,
                request.SortDirection,
                request.UploaderEmail,
                request.FolderType,
                request.CreatedAfter,
                request.CreatedBefore,
                request.IncludeShared,
                cancellationToken);

            var folderDtos = mapper.Map<List<FolderDto>>(folders);

            // TODO: Populate additional folder information like FileCount, SubfolderCount, and Permissions
            // This would require additional repository calls or enhanced mapping
            foreach (var folderDto in folderDtos)
            {
                folderDto.Permissions = new List<string>(); // Placeholder
                folderDto.FileCount = 0; // Placeholder - would need to query file repository
                folderDto.SubfolderCount = 0; // Placeholder - would need to query subfolder count
            }

            var result = PagedResult<FolderDto>.Create(
                folderDtos,
                totalCount,
                request.Page,
                request.PageSize);

            logger.LogInformation("Retrieved {Count} folders out of {TotalCount} for user {UserId}",
                folderDtos.Count, totalCount, request.UserId);

            return BaseResponseModel<PagedResult<FolderDto>>.CreateSuccess(result, "Folders retrieved successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting folders for user {UserId}", request.UserId);
            return BaseResponseModel<PagedResult<FolderDto>>.CreateError("An error occurred while retrieving folders", 500, "INTERNAL_ERROR");
        }
    }
}
