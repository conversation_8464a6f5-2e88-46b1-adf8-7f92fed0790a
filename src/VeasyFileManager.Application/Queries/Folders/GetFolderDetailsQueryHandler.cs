using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Folders;

/// <summary>
/// Handler for GetFolderDetailsQuery
/// </summary>
public class GetFolderDetailsQueryHandler : IRequestHandler<GetFolderDetailsQuery, BaseResponseModel<FolderDto?>>
{
    private readonly IFolderRepository _folderRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<GetFolderDetailsQueryHandler> _logger;

    public GetFolderDetailsQueryHandler(
        IFolderRepository folderRepository,
        IMapper mapper,
        ILogger<GetFolderDetailsQueryHandler> logger)
    {
        _folderRepository = folderRepository ?? throw new ArgumentNullException(nameof(folderRepository));
        _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<BaseResponseModel<FolderDto?>> Handle(GetFolderDetailsQuery request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Getting folder details for folder {FolderId} and user {UserId}", 
            request.FolderId, request.UserId);

        try
        {
            // Get the folder
            var folder = await _folderRepository.GetByIdAsync(request.FolderId, cancellationToken);
            if (folder == null)
            {
                _logger.LogWarning("Folder {FolderId} not found", request.FolderId);
                return BaseResponseModel<FolderDto?>.CreateError("Folder not found", 404, "FOLDER_NOT_FOUND");
            }

            // Check if user has permission to view the folder
            var hasPermission = await _folderRepository.HasUserAccessAsync(
                request.FolderId, 
                request.UserId, 
                PermissionType.Read, 
                cancellationToken);

            if (!hasPermission && folder.OwnerId != request.UserId)
            {
                _logger.LogWarning("User {UserId} does not have permission to view folder {FolderId}", request.UserId, request.FolderId);
                return BaseResponseModel<FolderDto?>.CreateError("You don't have permission to view this folder", 403, "PERMISSION_DENIED");
            }

            // Map to DTO
            var folderDto = _mapper.Map<FolderDto>(folder);

            // TODO: Add permission information and folder counts to the DTO
            // This would require additional logic to count files and subfolders
            folderDto.Permissions = new List<string>(); // Placeholder for now
            folderDto.FileCount = 0; // Placeholder - would need to count files
            folderDto.SubfolderCount = 0; // Placeholder - would need to count subfolders

            _logger.LogInformation("Folder details retrieved successfully for folder {FolderId}", request.FolderId);
            return BaseResponseModel<FolderDto?>.CreateSuccess(folderDto, "Folder details retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting folder details for folder {FolderId} and user {UserId}", request.FolderId, request.UserId);
            return BaseResponseModel<FolderDto?>.CreateError("An error occurred while retrieving folder details", 500, "INTERNAL_ERROR");
        }
    }
} 