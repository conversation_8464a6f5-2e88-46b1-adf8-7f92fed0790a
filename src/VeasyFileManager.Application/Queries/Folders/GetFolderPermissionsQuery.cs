using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Folders;

/// <summary>
/// Query to get all permissions for a folder
/// </summary>
public class GetFolderPermissionsQuery : IRequest<BaseResponseModel<List<FolderPermissionDto>>>
{
    /// <summary>
    /// Folder ID to get permissions for
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// User ID requesting the permissions
    /// </summary>
    public Guid RequesterId { get; set; }
} 