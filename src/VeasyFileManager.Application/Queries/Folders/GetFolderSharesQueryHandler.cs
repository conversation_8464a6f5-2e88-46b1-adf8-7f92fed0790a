using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Folders;

/// <summary>
/// Handler for getting folder shares
/// </summary>
public class GetFolderSharesQueryHandler(
    IFolderRepository folderRepository,
    IMapper mapper,
    ILogger<GetFolderSharesQueryHandler> logger) : IRequestHandler<GetFolderSharesQuery, BaseResponseModel<List<FolderShareDto>>>
{
    public async Task<BaseResponseModel<List<FolderShareDto>>> Handle(GetFolderSharesQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Getting shares for folder {FolderId} by user {UserId}", 
            request.FolderId, request.UserId);

        try
        {
            // Verify folder exists
            var folder = await folderRepository.GetByIdAsync(request.FolderId, cancellationToken);
            if (folder == null)
            {
                return BaseResponseModel<List<FolderShareDto>>.CreateError($"Folder with ID {request.FolderId} not found", 404, "FOLDER_NOT_FOUND");
            }

            // Check if user has permission to view shares
            var hasPermission = await folderRepository.HasUserAccessAsync(
                request.FolderId,
                request.UserId,
                PermissionType.Share,
                cancellationToken);

            if (!hasPermission && folder.OwnerId != request.UserId)
            {
                return BaseResponseModel<List<FolderShareDto>>.CreateError("You don't have permission to view shares on this folder", 403, "PERMISSION_DENIED");
            }

            // TODO: Get folder shares when IFolderShareRepository is implemented
            // For now, return empty list with a note
            var shares = new List<FolderShareDto>();

            logger.LogInformation("Retrieved {Count} shares for folder {FolderId}", 
                shares.Count, request.FolderId);

            return BaseResponseModel<List<FolderShareDto>>.CreateSuccess(shares, "Folder shares retrieved successfully (folder sharing not yet implemented)");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting shares for folder {FolderId}", request.FolderId);
            return BaseResponseModel<List<FolderShareDto>>.CreateError("An error occurred while retrieving folder shares", 500, "INTERNAL_ERROR");
        }
    }
} 