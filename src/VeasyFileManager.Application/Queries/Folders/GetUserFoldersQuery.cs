using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Folders;

/// <summary>
/// Query to get user folders with pagination and filtering
/// </summary>
public class GetUserFoldersQuery : IRequest<BaseResponseModel<PagedResult<FolderDto>>>
{
    /// <summary>
    /// User ID requesting the folders
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Parent folder ID (null for root folders)
    /// </summary>
    public Guid? ParentFolderId { get; set; }

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Search term for folder names
    /// </summary>
    public string? Search { get; set; }

    /// <summary>
    /// Sort field
    /// </summary>
    public string SortBy { get; set; } = "Name";

    /// <summary>
    /// Sort direction (ASC/DESC)
    /// </summary>
    public string SortDirection { get; set; } = "ASC";

    /// <summary>
    /// Filter by uploader email
    /// </summary>
    public string? UploaderEmail { get; set; }

    /// <summary>
    /// Filter by folder type or category
    /// </summary>
    public string? FolderType { get; set; }

    /// <summary>
    /// Filter folders created after this date
    /// </summary>
    public DateTime? CreatedAfter { get; set; }

    /// <summary>
    /// Filter folders created before this date
    /// </summary>
    public DateTime? CreatedBefore { get; set; }

    /// <summary>
    /// Include shared folders in results
    /// </summary>
    public bool IncludeShared { get; set; } = true;
}
