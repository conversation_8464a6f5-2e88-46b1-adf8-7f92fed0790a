using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Folders;

/// <summary>
/// Query to download a folder as a ZIP file
/// </summary>
public class DownloadFolderAsZipQuery : IRequest<BaseResponseModel<FolderZipDownloadDto>>
{
    /// <summary>
    /// Folder ID to download
    /// </summary>
    public Guid FolderId { get; set; }

    /// <summary>
    /// User ID requesting the download
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Whether to include subfolders
    /// </summary>
    public bool IncludeSubfolders { get; set; } = true;

    /// <summary>
    /// Maximum file size limit for the ZIP (in bytes)
    /// </summary>
    public long MaxZipSize { get; set; } = 1024 * 1024 * 1024; // 1GB default
} 