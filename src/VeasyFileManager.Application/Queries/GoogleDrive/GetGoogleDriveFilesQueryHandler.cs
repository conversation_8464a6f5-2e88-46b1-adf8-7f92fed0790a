using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.GoogleDrive;

/// <summary>
/// Handler for getting files from Google Drive
/// </summary>
public class GetGoogleDriveFilesQueryHandler(
    IGoogleDriveService googleDriveService,
    ILogger<GetGoogleDriveFilesQueryHandler> logger) : IRequestHandler<GetGoogleDriveFilesQuery, BaseResponseModel<PagedResult<GoogleDriveFileDto>>>
{
    public async Task<BaseResponseModel<PagedResult<GoogleDriveFileDto>>> Handle(GetGoogleDriveFilesQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Getting Google Drive files for user {UserId}, parent: {ParentFolderId}, page: {Page}, pageSize: {PageSize}",
            request.UserId, request.ParentFolderId, request.Page, request.PageSize);

        try
        {
            // Validate pagination parameters
            if (request.Page < 1)
                request.Page = 1;

            if (request.PageSize < 1 || request.PageSize > 100)
                request.PageSize = 20;

            // Get files from Google Drive service
            var files = await googleDriveService.ListFilesAsync(request.ParentFolderId, cancellationToken);

            // Apply pagination
            var totalCount = files.Count;
            var startIndex = (request.Page - 1) * request.PageSize;
            var pagedFiles = files.Skip(startIndex).Take(request.PageSize).ToList();

            var result = PagedResult<GoogleDriveFileDto>.Create(
                pagedFiles,
                totalCount,
                request.Page,
                request.PageSize);

            logger.LogInformation("Retrieved {Count} Google Drive files out of {TotalCount} for user {UserId}",
                pagedFiles.Count, totalCount, request.UserId);

            return BaseResponseModel<PagedResult<GoogleDriveFileDto>>.CreateSuccess(result, "Google Drive files retrieved successfully");
        }
        catch (UnauthorizedAccessException ex)
        {
            logger.LogWarning("User {UserId} is not authorized to access Google Drive: {Message}",
                request.UserId, ex.Message);
            return BaseResponseModel<PagedResult<GoogleDriveFileDto>>.CreateError("You are not authorized to access Google Drive", 401, "GOOGLE_DRIVE_UNAUTHORIZED");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting Google Drive files for user {UserId}", request.UserId);
            return BaseResponseModel<PagedResult<GoogleDriveFileDto>>.CreateError("An error occurred while retrieving Google Drive files", 500, "INTERNAL_ERROR");
        }
    }
} 