using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.GoogleDrive;

/// <summary>
/// Query to get files from Google Drive
/// </summary>
public class GetGoogleDriveFilesQuery : IRequest<BaseResponseModel<PagedResult<GoogleDriveFileDto>>>
{
    /// <summary>
    /// User ID requesting the files
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Parent folder ID in Google Drive (null for root)
    /// </summary>
    public string? ParentFolderId { get; set; }

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 20;
}