using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries;

/// <summary>
/// Query to get all available permission types
/// </summary>
public class GetAvailablePermissionsQuery : IRequest<BaseResponseModel<List<PermissionTypeDto>>>
{
    /// <summary>
    /// Filter by resource type (optional)
    /// </summary>
    public string? ResourceType { get; set; } // "file", "folder", or null for all
} 