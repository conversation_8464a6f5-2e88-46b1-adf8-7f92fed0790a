using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Sync;

/// <summary>
/// Query to get synchronization status for user files
/// </summary>
public class GetSyncStatusQuery : IRequest<BaseResponseModel<SyncStatusSummaryDto>>
{
    /// <summary>
    /// User ID to get sync status for
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Specific file ID to get status for (optional)
    /// </summary>
    public Guid? FileId { get; set; }

    /// <summary>
    /// Storage provider to filter by (optional)
    /// </summary>
    public string? Provider { get; set; }
}

/// <summary>
/// Summary DTO for sync status information
/// </summary>
public class SyncStatusSummaryDto
{
    /// <summary>
    /// Overall sync status
    /// </summary>
    public string OverallStatus { get; set; } = null!;

    /// <summary>
    /// Last successful sync timestamp
    /// </summary>
    public DateTime? LastSync { get; set; }

    /// <summary>
    /// Total number of files
    /// </summary>
    public int TotalFiles { get; set; }

    /// <summary>
    /// Number of successfully synced files
    /// </summary>
    public int SyncedFiles { get; set; }

    /// <summary>
    /// Number of files pending sync
    /// </summary>
    public int PendingFiles { get; set; }

    /// <summary>
    /// Number of files with sync failures
    /// </summary>
    public int FailedFiles { get; set; }

    /// <summary>
    /// Number of files currently syncing
    /// </summary>
    public int SyncingFiles { get; set; }

    /// <summary>
    /// Detailed sync status for individual files (if FileId specified)
    /// </summary>
    public List<SyncStatusDto>? FileStatuses { get; set; }
} 