using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.Sync;

/// <summary>
/// Handler for getting synchronization status
/// </summary>
public class GetSyncStatusQueryHandler(
    ISyncStatusRepository syncStatusRepository,
    IFileRepository fileRepository,
    IMapper mapper,
    ILogger<GetSyncStatusQueryHandler> logger) : IRequestHandler<GetSyncStatusQuery, BaseResponseModel<SyncStatusSummaryDto>>
{
    public async Task<BaseResponseModel<SyncStatusSummaryDto>> Handle(GetSyncStatusQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Getting sync status for user {UserId}, FileId: {FileId}, Provider: {Provider}",
            request.UserId, request.FileId, request.Provider);

        try
        {
            // Get sync status summary from repository
            var (totalFiles, syncedFiles, pendingFiles, failedFiles, syncingFiles) = 
                await syncStatusRepository.GetStatusSummaryAsync(
                    request.UserId, 
                    request.FileId, 
                    request.Provider, 
                    cancellationToken);

            // Determine overall status
            string overallStatus;
            if (failedFiles > 0)
            {
                overallStatus = "errors";
            }
            else if (syncingFiles > 0 || pendingFiles > 0)
            {
                overallStatus = "syncing";
            }
            else if (syncedFiles == totalFiles && totalFiles > 0)
            {
                overallStatus = "synced";
            }
            else
            {
                overallStatus = "pending";
            }

            // Get last successful sync time
            var lastSync = await syncStatusRepository.GetLastSyncTimeAsync(
                request.UserId, 
                request.Provider, 
                cancellationToken);

            var result = new SyncStatusSummaryDto
            {
                OverallStatus = overallStatus,
                LastSync = lastSync,
                TotalFiles = totalFiles,
                SyncedFiles = syncedFiles,
                PendingFiles = pendingFiles,
                FailedFiles = failedFiles,
                SyncingFiles = syncingFiles
            };

            // Include detailed file statuses if specific file requested
            if (request.FileId.HasValue)
            {
                var syncStatuses = await syncStatusRepository.GetByUserIdAsync(request.UserId, cancellationToken);
                var filteredStatuses = syncStatuses.Where(s => s.FileId == request.FileId.Value);
                
                if (!string.IsNullOrEmpty(request.Provider))
                {
                    filteredStatuses = filteredStatuses.Where(s => s.Provider == request.Provider);
                }
                
                result.FileStatuses = mapper.Map<List<SyncStatusDto>>(filteredStatuses.ToList());
            }

            logger.LogInformation("Retrieved sync status for user {UserId}: {OverallStatus}, {TotalFiles} total files",
                request.UserId, overallStatus, totalFiles);

            return BaseResponseModel<SyncStatusSummaryDto>.CreateSuccess(result, "Sync status retrieved successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting sync status for user {UserId}", request.UserId);
            return BaseResponseModel<SyncStatusSummaryDto>.CreateError("An error occurred while retrieving sync status", 500, "INTERNAL_ERROR");
        }
    }
} 