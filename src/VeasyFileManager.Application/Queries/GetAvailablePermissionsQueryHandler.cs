using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries;

/// <summary>
/// Handler for getting available permission types
/// </summary>
public class GetAvailablePermissionsQueryHandler(
    ILogger<GetAvailablePermissionsQueryHandler> logger) : IRequestHandler<GetAvailablePermissionsQuery, BaseResponseModel<List<PermissionTypeDto>>>
{
    public async Task<BaseResponseModel<List<PermissionTypeDto>>> Handle(GetAvailablePermissionsQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Getting available permissions for resource type: {ResourceType}", request.ResourceType ?? "all");

        try
        {
            var permissions = new List<PermissionTypeDto>
            {
                new()
                {
                    Value = PermissionType.Read,
                    Name = "Read",
                    Description = "View and download files/folders and their contents",
                    ApplicableToFiles = true,
                    ApplicableToFolders = true,
                    SortOrder = 1
                },
                new()
                {
                    Value = PermissionType.Write,
                    Name = "Write",
                    Description = "Create, edit, upload files and create subfolders",
                    ApplicableToFiles = true,
                    ApplicableToFolders = true,
                    SortOrder = 2
                },
                new()
                {
                    Value = PermissionType.Delete,
                    Name = "Delete",
                    Description = "Delete files and folders",
                    ApplicableToFiles = true,
                    ApplicableToFolders = true,
                    SortOrder = 3
                },
                new()
                {
                    Value = PermissionType.Share,
                    Name = "Share",
                    Description = "Create share links and manage permissions for files/folders",
                    ApplicableToFiles = true,
                    ApplicableToFolders = true,
                    SortOrder = 4
                }
            };

            // Filter by resource type if specified
            if (!string.IsNullOrEmpty(request.ResourceType))
            {
                switch (request.ResourceType.ToLowerInvariant())
                {
                    case "file":
                        permissions = permissions.Where(p => p.ApplicableToFiles).ToList();
                        break;
                    case "folder":
                        permissions = permissions.Where(p => p.ApplicableToFolders).ToList();
                        break;
                    default:
                        logger.LogWarning("Unknown resource type filter: {ResourceType}", request.ResourceType);
                        break;
                }
            }

            var result = permissions.OrderBy(p => p.SortOrder).ToList();
            logger.LogInformation("Returning {Count} available permissions", result.Count);

            return await Task.FromResult(BaseResponseModel<List<PermissionTypeDto>>.CreateSuccess(result, "Available permissions retrieved successfully"));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting available permissions for resource type: {ResourceType}", request.ResourceType);
            return BaseResponseModel<List<PermissionTypeDto>>.CreateError("An error occurred while retrieving available permissions", 500, "INTERNAL_ERROR");
        }
    }
} 