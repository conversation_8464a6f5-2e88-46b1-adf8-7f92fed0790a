using MediatR;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Common.Base;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.Queries.RecycleBin;

/// <summary>
/// Query to get deleted items (recycle bin) - T<PERSON>y vấn lấy danh sách thùng rác
/// </summary>
public class GetDeletedItemsQuery : IRequest<BaseResponseModel<PagedResult<DeletedItemDto>>>
{
    /// <summary>
    /// User ID requesting the deleted items
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    public int Page { get; set; } = 1;

    /// <summary>
    /// Number of items per page
    /// </summary>
    public int PageSize { get; set; } = 20;

    /// <summary>
    /// Filter by item type (Folder or File) - Lọ<PERSON> theo loại
    /// </summary>
    public DeletedItemType? ItemType { get; set; }

    /// <summary>
    /// Search term for item names or paths - <PERSON><PERSON><PERSON> kiếm theo tên
    /// </summary>
    public string? SearchTerm { get; set; }

    /// <summary>
    /// Filter by uploader email - Lọc theo người tạo
    /// </summary>
    public string? UploaderEmail { get; set; }

    /// <summary>
    /// Filter items deleted after this date - Từ ngày
    /// </summary>
    public DateTime? DeletedAfter { get; set; }

    /// <summary>
    /// Filter items deleted before this date - Đến ngày
    /// </summary>
    public DateTime? DeletedBefore { get; set; }

    /// <summary>
    /// Only show restorable items (within 30 days)
    /// </summary>
    public bool OnlyRestorable { get; set; } = true;
}
