using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Common.Base;

namespace VeasyFileManager.Application.Queries.RecycleBin;

/// <summary>
/// Handler for getting deleted items query
/// </summary>
public class GetDeletedItemsQueryHandler(
    IDeletedItemRepository deletedItemRepository,
    IMapper mapper,
    ILogger<GetDeletedItemsQueryHandler> logger) : IRequestHandler<GetDeletedItemsQuery, BaseResponseModel<PagedResult<DeletedItemDto>>>
{
    public async Task<BaseResponseModel<PagedResult<DeletedItemDto>>> Handle(GetDeletedItemsQuery request, CancellationToken cancellationToken)
    {
        logger.LogInformation("Getting deleted items for user {UserId}, page: {Page}, pageSize: {PageSize}",
            request.UserId, request.Page, request.PageSize);

        try
        {
            // Validate pagination parameters
            if (request.Page < 1)
                request.Page = 1;

            if (request.PageSize < 1 || request.PageSize > 100)
                request.PageSize = 20;

            var (deletedItems, totalCount) = await deletedItemRepository.GetDeletedItemsAsync(
                request.UserId,
                request.Page,
                request.PageSize,
                request.ItemType,
                request.SearchTerm,
                request.UploaderEmail,
                request.DeletedAfter,
                request.DeletedBefore,
                request.OnlyRestorable,
                cancellationToken);

            var deletedItemDtos = mapper.Map<List<DeletedItemDto>>(deletedItems);

            var result = PagedResult<DeletedItemDto>.Create(
                deletedItemDtos,
                totalCount,
                request.Page,
                request.PageSize);

            logger.LogInformation("Retrieved {Count} deleted items out of {TotalCount} for user {UserId}",
                deletedItemDtos.Count, totalCount, request.UserId);

            return BaseResponseModel<PagedResult<DeletedItemDto>>.CreateSuccess(
                result,
                "Deleted items retrieved successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting deleted items for user {UserId}", request.UserId);
            return BaseResponseModel<PagedResult<DeletedItemDto>>.CreateError(
                "An error occurred while retrieving deleted items",
                500,
                "INTERNAL_ERROR");
        }
    }
}
