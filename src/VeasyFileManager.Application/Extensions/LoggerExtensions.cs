using Microsoft.Extensions.Logging;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Application.Extensions;

/// <summary>
/// Extension methods for structured logging
/// </summary>
public static class LoggerExtensions
{
    #region File Operations

    /// <summary>
    /// Log file operation events
    /// </summary>
    public static void LogFileOperation(this ILogger logger, string operation, Guid fileId, Guid userId, string fileName, string? additionalInfo = null)
    {
        logger.LogInformation("File operation: {Operation} executed by user {UserId} on file {FileId} ({FileName}){AdditionalInfo}",
            operation, userId, fileId, fileName, string.IsNullOrEmpty(additionalInfo) ? "" : $" - {additionalInfo}");
    }

    /// <summary>
    /// Log file upload events
    /// </summary>
    public static void LogFileUpload(this ILogger logger, Guid fileId, Guid userId, string fileName, long fileSize, string mimeType, StorageProvider provider)
    {
        logger.LogInformation("File uploaded: {FileName} ({FileSize} bytes, {MimeType}) by user {UserId} to {StorageProvider} with ID {FileId}",
            fileName, fileSize, mimeType, userId, provider, fileId);
    }

    /// <summary>
    /// Log file download events
    /// </summary>
    public static void LogFileDownload(this ILogger logger, Guid fileId, Guid? userId, string fileName, string? shareToken = null)
    {
        if (!string.IsNullOrEmpty(shareToken))
        {
            logger.LogInformation("File downloaded via share: {FileName} (ID: {FileId}) using token {ShareToken}",
                fileName, fileId, shareToken);
        }
        else
        {
            logger.LogInformation("File downloaded: {FileName} (ID: {FileId}) by user {UserId}",
                fileName, fileId, userId);
        }
    }

    /// <summary>
    /// Log file deletion events
    /// </summary>
    public static void LogFileDelete(this ILogger logger, Guid fileId, Guid userId, string fileName, bool permanent = false)
    {
        logger.LogInformation("File {DeleteType}: {FileName} (ID: {FileId}) by user {UserId}",
            permanent ? "permanently deleted" : "deleted", fileName, fileId, userId);
    }

    #endregion

    #region Folder Operations

    /// <summary>
    /// Log folder operation events
    /// </summary>
    public static void LogFolderOperation(this ILogger logger, string operation, Guid folderId, Guid userId, string folderName, string? additionalInfo = null)
    {
        logger.LogInformation("Folder operation: {Operation} executed by user {UserId} on folder {FolderId} ({FolderName}){AdditionalInfo}",
            operation, userId, folderId, folderName, string.IsNullOrEmpty(additionalInfo) ? "" : $" - {additionalInfo}");
    }

    /// <summary>
    /// Log folder ZIP download events
    /// </summary>
    public static void LogFolderZipDownload(this ILogger logger, Guid folderId, Guid userId, string folderName, int fileCount, long totalSize)
    {
        logger.LogInformation("Folder ZIP download: {FolderName} (ID: {FolderId}) by user {UserId} - {FileCount} files, {TotalSize} bytes",
            folderName, folderId, userId, fileCount, totalSize);
    }

    #endregion

    #region Permission Operations

    /// <summary>
    /// Log permission changes
    /// </summary>
    public static void LogPermissionChange(this ILogger logger, Guid resourceId, string resourceType, Guid targetUserId, PermissionType permission, string action, Guid actionBy)
    {
        logger.LogInformation("Permission {Action}: {Permission} for user {TargetUserId} on {ResourceType} {ResourceId} by user {ActionBy}",
            action, permission, targetUserId, resourceType, resourceId, actionBy);
    }

    /// <summary>
    /// Log permission check results
    /// </summary>
    public static void LogPermissionCheck(this ILogger logger, Guid userId, Guid resourceId, string resourceType, PermissionType permission, bool hasAccess)
    {
        logger.LogDebug("Permission check: User {UserId} {AccessResult} {Permission} access to {ResourceType} {ResourceId}",
            userId, hasAccess ? "HAS" : "DENIED", permission, resourceType, resourceId);
    }

    #endregion

    #region Sharing Operations

    /// <summary>
    /// Log file sharing events
    /// </summary>
    public static void LogFileShare(this ILogger logger, Guid fileId, string fileName, Guid sharedBy, ShareType shareType, string shareToken, DateTime? expiresAt = null)
    {
        logger.LogInformation("File share created: {FileName} (ID: {FileId}) shared by user {SharedBy} as {ShareType} with token {ShareToken}{ExpirationInfo}",
            fileName, fileId, sharedBy, shareType, shareToken, 
            expiresAt.HasValue ? $" (expires: {expiresAt.Value:yyyy-MM-dd HH:mm:ss} UTC)" : "");
    }

    /// <summary>
    /// Log share access events
    /// </summary>
    public static void LogShareAccess(this ILogger logger, string shareToken, Guid fileId, string fileName, bool successful, string? reason = null)
    {
        if (successful)
        {
            logger.LogInformation("Share access successful: Token {ShareToken} for file {FileName} (ID: {FileId})",
                shareToken, fileName, fileId);
        }
        else
        {
            logger.LogWarning("Share access denied: Token {ShareToken} for file {FileName} (ID: {FileId}) - {Reason}",
                shareToken, fileName, fileId, reason ?? "Unknown reason");
        }
    }

    #endregion

    #region External Service Operations

    /// <summary>
    /// Log sync operations with external services
    /// </summary>
    public static void LogSyncOperation(this ILogger logger, string provider, Guid fileId, string fileName, string status, string? error = null, int retryCount = 0)
    {
        if (string.IsNullOrEmpty(error))
        {
            logger.LogInformation("Sync operation: {Provider} sync {Status} for file {FileName} (ID: {FileId}){RetryInfo}",
                provider, status, fileName, fileId, retryCount > 0 ? $" after {retryCount} retries" : "");
        }
        else
        {
            logger.LogError("Sync operation failed: {Provider} sync failed for file {FileName} (ID: {FileId}) with error: {Error}{RetryInfo}",
                provider, fileName, fileId, error, retryCount > 0 ? $" (retry {retryCount})" : "");
        }
    }

    /// <summary>
    /// Log storage operations
    /// </summary>
    public static void LogStorageOperation(this ILogger logger, string operation, StorageProvider provider, string fileName, bool successful, string? error = null, long? fileSize = null)
    {
        if (successful)
        {
            logger.LogInformation("Storage operation successful: {Operation} on {Provider} for file {FileName}{SizeInfo}",
                operation, provider, fileName, fileSize.HasValue ? $" ({fileSize.Value} bytes)" : "");
        }
        else
        {
            logger.LogError("Storage operation failed: {Operation} on {Provider} for file {FileName} - {Error}",
                operation, provider, fileName, error ?? "Unknown error");
        }
    }

    #endregion

    #region Performance and Metrics

    /// <summary>
    /// Log performance metrics for operations
    /// </summary>
    public static void LogPerformanceMetric(this ILogger logger, string operation, TimeSpan duration, string? additionalMetrics = null)
    {
        logger.LogInformation("Performance: {Operation} completed in {Duration}ms{AdditionalMetrics}",
            operation, duration.TotalMilliseconds, string.IsNullOrEmpty(additionalMetrics) ? "" : $" - {additionalMetrics}");
    }

    /// <summary>
    /// Log API endpoint access
    /// </summary>
    public static void LogApiAccess(this ILogger logger, string method, string path, Guid? userId, int statusCode, TimeSpan duration)
    {
        logger.LogInformation("API Access: {Method} {Path} by user {UserId} returned {StatusCode} in {Duration}ms",
            method, path, userId?.ToString() ?? "Anonymous", statusCode, duration.TotalMilliseconds);
    }

    #endregion

    #region Security Events

    /// <summary>
    /// Log security-related events
    /// </summary>
    public static void LogSecurityEvent(this ILogger logger, string eventType, Guid? userId, string details, string? ipAddress = null)
    {
        logger.LogWarning("Security Event: {EventType} by user {UserId} from IP {IpAddress} - {Details}",
            eventType, userId?.ToString() ?? "Unknown", ipAddress ?? "Unknown", details);
    }

    /// <summary>
    /// Log authentication events
    /// </summary>
    public static void LogAuthenticationEvent(this ILogger logger, Guid userId, bool successful, string? reason = null, string? ipAddress = null)
    {
        if (successful)
        {
            logger.LogInformation("Authentication successful: User {UserId} from IP {IpAddress}",
                userId, ipAddress ?? "Unknown");
        }
        else
        {
            logger.LogWarning("Authentication failed: User {UserId} from IP {IpAddress} - {Reason}",
                userId, ipAddress ?? "Unknown", reason ?? "Unknown reason");
        }
    }

    #endregion
} 