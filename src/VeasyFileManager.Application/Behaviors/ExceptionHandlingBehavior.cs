using MediatR;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Extensions;

namespace VeasyFileManager.Application.Behaviors;

/// <summary>
/// Pipeline behavior for handling exceptions in MediatR requests
/// </summary>
public class ExceptionHandlingBehavior<TRequest, TResponse>(
    ILogger<ExceptionHandlingBehavior<TRequest, TResponse>> logger)
    : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;

        try
        {
            return await next();
        }
        catch (ArgumentException ex)
        {
            logger.LogError(ex, "Validation error in {RequestName}: {Message}", requestName, ex.Message);
            throw;
        }
        catch (UnauthorizedAccessException ex)
        {
            logger.LogWarning(ex, "Unauthorized access in {RequestName}: {Message}", requestName, ex.Message);
            throw;
        }
        catch (FileNotFoundException ex)
        {
            logger.LogWarning(ex, "Resource not found in {RequestName}: {Message}", requestName, ex.Message);
            throw;
        }
        catch (InvalidOperationException ex)
        {
            logger.LogError(ex, "Invalid operation in {RequestName}: {Message}", requestName, ex.Message);
            throw;
        }
        catch (OperationCanceledException ex) when (cancellationToken.IsCancellationRequested)
        {
            logger.LogInformation("Request {RequestName} was cancelled by client", requestName);
            throw;
        }
        catch (TimeoutException ex)
        {
            logger.LogError(ex, "Timeout occurred in {RequestName}: {Message}", requestName, ex.Message);
            throw;
        }
        catch (Exception ex)
        {
            // Log the full exception for unexpected errors
            logger.LogError(ex, "Unhandled exception in {RequestName}: {Message}", requestName, ex.Message);

            // Log additional context for debugging
            LogRequestContext(request, requestName, logger);

            throw;
        }
    }

    private static void LogRequestContext(TRequest request, string requestName, ILogger logger)
    {
        try
        {
            // Safely extract useful information from the request
            var requestType = request.GetType();
            var properties = new List<string>();

            // Look for common properties that might be useful for debugging
            var userIdProperty = requestType.GetProperty("UserId");
            if (userIdProperty?.GetValue(request) is Guid userId)
            {
                properties.Add($"UserId: {userId}");
            }

            var fileIdProperty = requestType.GetProperty("FileId");
            if (fileIdProperty?.GetValue(request) is Guid fileId)
            {
                properties.Add($"FileId: {fileId}");
            }

            var folderIdProperty = requestType.GetProperty("FolderId");
            if (folderIdProperty?.GetValue(request) is Guid folderId)
            {
                properties.Add($"FolderId: {folderId}");
            }

            var shareTokenProperty = requestType.GetProperty("ShareToken");
            if (shareTokenProperty?.GetValue(request) is string shareToken)
            {
                properties.Add($"ShareToken: {shareToken[..Math.Min(8, shareToken.Length)]}...");
            }

            if (properties.Any())
            {
                logger.LogDebug("Request context for {RequestName}: {Context}", 
                    requestName, string.Join(", ", properties));
            }
        }
        catch (Exception ex)
        {
            logger.LogDebug(ex, "Failed to extract request context for {RequestName}", requestName);
        }
    }
} 