using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using VeasyFileManager.Application.Extensions;

namespace VeasyFileManager.Application.Behaviors;

/// <summary>
/// Pipeline behavior for logging performance metrics and tracking slow operations
/// </summary>
public class PerformanceLoggingBehavior<TRequest, TResponse>(
    ILogger<PerformanceLoggingBehavior<TRequest, TResponse>> logger)
    : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    private const int SlowOperationThresholdMs = 5000; // 5 seconds

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var response = await next();
            stopwatch.Stop();

            var duration = stopwatch.Elapsed;

            // Log performance metrics
            logger.LogPerformanceMetric(requestName, duration);

            // Warn about slow operations
            if (duration.TotalMilliseconds > SlowOperationThresholdMs)
            {
                logger.LogWarning("Slow operation detected: {RequestName} took {Duration}ms to complete",
                    requestName, duration.TotalMilliseconds);
            }

            // Additional metrics for specific operation types
            LogSpecificMetrics(requestName, duration, logger);

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;

            logger.LogError(ex, "Operation {RequestName} failed after {Duration}ms",
                requestName, duration.TotalMilliseconds);

            throw;
        }
    }

    private static void LogSpecificMetrics(string requestName, TimeSpan duration, ILogger logger)
    {
        var durationMs = duration.TotalMilliseconds;
        
        // File operation specific thresholds
        switch (requestName)
        {
            case var name when name.Contains("Upload") && durationMs > 30000: // 30 seconds
                logger.LogWarning("File upload operation took {Duration}ms - consider implementing chunked upload", durationMs);
                break;
                
            case var name when name.Contains("Download") && durationMs > 15000: // 15 seconds
                logger.LogWarning("File download operation took {Duration}ms - consider caching or CDN", durationMs);
                break;
                
            case var name when name.Contains("Copy") && durationMs > 20000: // 20 seconds
                logger.LogWarning("File copy operation took {Duration}ms - consider background processing", durationMs);
                break;
                
            case var name when name.Contains("Zip") && durationMs > 60000: // 1 minute
                logger.LogWarning("ZIP operation took {Duration}ms - consider size limits or background processing", durationMs);
                break;
                
            case var name when name.Contains("Permission") && durationMs > 2000: // 2 seconds
                logger.LogWarning("Permission operation took {Duration}ms - consider permission caching", durationMs);
                break;
                
            case var name when name.Contains("Search") && durationMs > 5000: // 5 seconds
                logger.LogWarning("Search operation took {Duration}ms - consider indexing optimization", durationMs);
                break;
        }
    }
} 