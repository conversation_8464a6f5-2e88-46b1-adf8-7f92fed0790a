using MediatR;
using Microsoft.Extensions.Logging;

namespace VeasyFileManager.Application.Behaviors;

/// <summary>
/// Pipeline behavior for logging requests and responses
/// </summary>
public class RequestResponseLoggingBehavior<TRequest, TResponse>(ILogger<RequestResponseLoggingBehavior<TRequest, TResponse>> logger)
    : IPipelineBehavior<TRequest, TResponse>
    where TRequest : notnull
{
    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        var requestName = typeof(TRequest).Name;
        var requestId = Guid.NewGuid();

        // Log request
        logger.LogInformation("Processing request {RequestName} with ID {RequestId}",
            requestName, requestId);

        if (logger.IsEnabled(LogLevel.Debug))
        {
            logger.LogDebug("Request {RequestName} ({RequestId}) details: {@Request}",
                requestName, requestId, request);
        }

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            var response = await next();
            stopwatch.Stop();

            // Log successful response
            logger.LogInformation("Request {RequestName} ({RequestId}) completed successfully in {Duration}ms",
                requestName, requestId, stopwatch.ElapsedMilliseconds);

            if (logger.IsEnabled(LogLevel.Debug) && response != null)
            {
                logger.LogDebug("Response for {RequestName} ({RequestId}): {@Response}",
                    requestName, requestId, response);
            }

            return response;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();

            // Log failed response
            logger.LogError(ex, "Request {RequestName} ({RequestId}) failed after {Duration}ms",
                requestName, requestId, stopwatch.ElapsedMilliseconds);

            throw;
        }
    }
} 