using Microsoft.EntityFrameworkCore;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Infrastructure.Data;

namespace VeasyFileManager.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for SyncStatus entity
/// </summary>
public class SyncStatusRepository : Repository<SyncStatus>, ISyncStatusRepository
{
    public SyncStatusRepository(StorageDbContext context) : base(context)
    {
    }

    public async Task<List<SyncStatus>> GetByUserIdAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.File)
            .Where(s => s.File.OwnerId == userId && !s.File.IsDeleted)
            .ToListAsync(cancellationToken);
    }

    public async Task<SyncStatus?> GetByFileIdAsync(Guid fileId, string provider, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .FirstOrDefaultAsync(s => s.FileId == fileId && s.Provider == provider, cancellationToken);
    }

    public async Task<List<SyncStatus>> GetByProviderAsync(string provider, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(s => s.Provider == provider)
            .ToListAsync(cancellationToken);
    }

    public async Task<List<SyncStatus>> GetByStatusAsync(Domain.Enums.SyncStatus status, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(s => s.Status == status)
            .ToListAsync(cancellationToken);
    }

    public async Task<(int Total, int Synced, int Pending, int Failed, int Syncing)> GetStatusSummaryAsync(
        Guid userId, 
        Guid? fileId = null, 
        string? provider = null, 
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Include(s => s.File)
            .Where(s => s.File.OwnerId == userId && !s.File.IsDeleted);

        if (fileId.HasValue)
        {
            query = query.Where(s => s.FileId == fileId.Value);
        }

        if (!string.IsNullOrEmpty(provider))
        {
            query = query.Where(s => s.Provider == provider);
        }

        var syncStatuses = await query.ToListAsync(cancellationToken);

        // Get total file count
        var totalFilesQuery = _context.Files.Where(f => f.OwnerId == userId && !f.IsDeleted);
        if (fileId.HasValue)
        {
            totalFilesQuery = totalFilesQuery.Where(f => f.Id == fileId.Value);
        }
        var totalFiles = await totalFilesQuery.CountAsync(cancellationToken);

        var synced = syncStatuses.Count(s => s.Status == Domain.Enums.SyncStatus.Synced);
        var pending = syncStatuses.Count(s => s.Status == Domain.Enums.SyncStatus.Pending);
        var failed = syncStatuses.Count(s => s.Status == Domain.Enums.SyncStatus.Failed);
        var syncing = syncStatuses.Count(s => s.Status == Domain.Enums.SyncStatus.Syncing);

        return (totalFiles, synced, pending, failed, syncing);
    }

    public async Task<DateTime?> GetLastSyncTimeAsync(Guid userId, string? provider = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Include(s => s.File)
            .Where(s => s.File.OwnerId == userId && 
                       s.Status == Domain.Enums.SyncStatus.Synced && 
                       s.LastSyncAt.HasValue);

        if (!string.IsNullOrEmpty(provider))
        {
            query = query.Where(s => s.Provider == provider);
        }

        return await query.MaxAsync(s => s.LastSyncAt, cancellationToken);
    }

    public async Task<SyncStatus> UpsertAsync(Guid fileId, string provider, string? externalId = null, CancellationToken cancellationToken = default)
    {
        var existingStatus = await GetByFileIdAsync(fileId, provider, cancellationToken);
        
        if (existingStatus != null)
        {
            // If external ID is provided and different, complete the sync with new external ID
            if (!string.IsNullOrEmpty(externalId) && existingStatus.ExternalId != externalId)
            {
                existingStatus.CompleteSync(externalId);
                Update(existingStatus);
            }
            return existingStatus;
        }

        var newStatus = SyncStatus.Create(fileId, provider, externalId);
        await AddAsync(newStatus, cancellationToken);
        return newStatus;
    }
}
