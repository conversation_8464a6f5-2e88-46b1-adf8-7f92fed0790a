using Microsoft.EntityFrameworkCore;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Infrastructure.Data;

namespace VeasyFileManager.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for Folder entity
/// </summary>
public class FolderRepository(StorageDbContext context) : Repository<Folder>(context), IFolderRepository
{
    public async Task<(IEnumerable<Folder> Folders, int TotalCount)> GetUserFoldersAsync(
        Guid userId,
        Guid? parentFolderId = null,
        int page = 1,
        int pageSize = 20,
        string? searchTerm = null,
        string sortBy = "CreatedAt",
        string sortDirection = "DESC",
        string? uploaderEmail = null,
        string? folderType = null,
        DateTime? createdAfter = null,
        DateTime? createdBefore = null,
        bool includeShared = true,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.AsQueryable();

        // Base filter for user's own folders
        if (includeShared)
        {
            // Include folders owned by user OR folders shared with user
            query = query.Where(f =>
                (f.OwnerId == userId && !f.IsDeleted) ||
                (!f.IsDeleted && f.Permissions.Any(p => p.UserId == userId &&
                                                       p.IsActive &&
                                                       (p.ExpiresAt == null || p.ExpiresAt > DateTime.UtcNow))));
        }
        else
        {
            // Only user's own folders
            query = query.Where(f => f.OwnerId == userId && !f.IsDeleted);
        }

        // Parent folder filter
        if (parentFolderId.HasValue)
        {
            query = query.Where(f => f.ParentFolderId == parentFolderId.Value);
        }
        else
        {
            query = query.Where(f => f.ParentFolderId == null);
        }

        // Search term filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(f => f.Name.Contains(searchTerm));
        }

        // Uploader email filter - would require joining with IdentityServer user data
        // This is commented out as it requires integration with the separate IdentityServer database
        // if (!string.IsNullOrWhiteSpace(uploaderEmail))
        // {
        //     // TODO: Implement user email filtering when user integration is available
        //     // This would require joining with IdentityServer user data
        // }

        // Folder type filter - would require adding a FolderType property to the Folder entity
        // This is commented out as the current entity doesn't have this property
        // if (!string.IsNullOrWhiteSpace(folderType))
        // {
        //     // TODO: Add FolderType property to Folder entity if categorization is needed
        // }

        // Date range filters
        if (createdAfter.HasValue)
        {
            query = query.Where(f => f.CreatedAt >= createdAfter.Value);
        }

        if (createdBefore.HasValue)
        {
            query = query.Where(f => f.CreatedAt <= createdBefore.Value);
        }

        // Apply sorting
        query = sortBy.ToLower() switch
        {
            "name" => sortDirection.ToUpper() == "ASC" ? query.OrderBy(f => f.Name) : query.OrderByDescending(f => f.Name),
            "updatedat" => sortDirection.ToUpper() == "ASC" ? query.OrderBy(f => f.UpdatedAt) : query.OrderByDescending(f => f.UpdatedAt),
            _ => sortDirection.ToUpper() == "ASC" ? query.OrderBy(f => f.CreatedAt) : query.OrderByDescending(f => f.CreatedAt),
        };

        var totalCount = await query.CountAsync(cancellationToken);
        var folders = await query.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync(cancellationToken);

        return (folders, totalCount);
    }

    public async Task<Folder?> GetByPathAsync(string path, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(f => f.Path == path && !f.IsDeleted, cancellationToken);
    }

    public async Task<IEnumerable<Folder>> GetFolderHierarchyAsync(Guid folderId, CancellationToken cancellationToken = default)
    {
        var folders = new List<Folder>();
        await GetChildFoldersRecursive(folderId, folders, cancellationToken);
        return folders;
    }

    public async Task<bool> HasUserAccessAsync(Guid folderId, Guid userId, PermissionType permission, CancellationToken cancellationToken = default)
    {
        // Check if user is owner
        var folder = await GetByIdAsync(folderId, cancellationToken);
        if (folder?.OwnerId == userId)
            return true;

        // Check explicit permissions
        return await _context.FolderPermissions
            .AnyAsync(fp => fp.FolderId == folderId &&
                           fp.UserId == userId &&
                           fp.PermissionType == permission &&
                           fp.IsActive &&
                           (fp.ExpiresAt == null || fp.ExpiresAt > DateTime.UtcNow),
                      cancellationToken);
    }

    public async Task<IEnumerable<Folder>> GetFoldersWithPermissionAsync(Guid userId, PermissionType permission, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.OwnerId == userId ||
                       f.Permissions.Any(p => p.UserId == userId &&
                                            p.PermissionType == permission &&
                                            p.IsActive &&
                                            (p.ExpiresAt == null || p.ExpiresAt > DateTime.UtcNow)))
            .Where(f => !f.IsDeleted)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Folder>> GetByOwnerAsync(Guid ownerId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.OwnerId == ownerId && !f.IsDeleted)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Folder>> GetRootFoldersAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.OwnerId == userId && f.ParentFolderId == null && !f.IsDeleted)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Folder>> GetSubfoldersAsync(Guid parentFolderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.ParentFolderId == parentFolderId && !f.IsDeleted)
            .ToListAsync(cancellationToken);
    }

    public async Task<Folder?> GetWithPermissionsAsync(Guid folderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(f => f.Permissions)
            .FirstOrDefaultAsync(f => f.Id == folderId && !f.IsDeleted, cancellationToken);
    }

    public async Task<bool> IsPathUniqueAsync(string path, Guid userId, Guid? excludeFolderId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(f => f.Path == path && f.OwnerId == userId && !f.IsDeleted);

        if (excludeFolderId.HasValue)
        {
            query = query.Where(f => f.Id != excludeFolderId.Value);
        }

        return !await query.AnyAsync(cancellationToken);
    }

    private async Task GetChildFoldersRecursive(Guid parentId, List<Folder> folders, CancellationToken cancellationToken)
    {
        var children = await _dbSet
            .Where(f => f.ParentFolderId == parentId && !f.IsDeleted)
            .ToListAsync(cancellationToken);

        folders.AddRange(children);

        foreach (var child in children)
        {
            await GetChildFoldersRecursive(child.Id, folders, cancellationToken);
        }
    }
}
