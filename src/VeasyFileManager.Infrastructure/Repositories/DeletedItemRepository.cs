using Microsoft.EntityFrameworkCore;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Infrastructure.Data;

namespace VeasyFileManager.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for DeletedItem entity
/// </summary>
public class DeletedItemRepository(StorageDbContext context) : Repository<DeletedItem>(context), IDeletedItemRepository
{
    public async Task<(IEnumerable<DeletedItem> Items, int TotalCount)> GetDeletedItemsAsync(
        Guid userId,
        int page = 1,
        int pageSize = 20,
        DeletedItemType? itemType = null,
        string? searchTerm = null,
        string? uploaderEmail = null,
        DateTime? deletedAfter = null,
        DateTime? deletedBefore = null,
        bool onlyRestorable = true,
        CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(d => d.DeletedBy == userId);

        if (itemType.HasValue)
        {
            query = query.Where(d => d.ItemType == itemType.Value);
        }

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            query = query.Where(d => d.OriginalName.Contains(searchTerm) ||
                                   d.OriginalPath.Contains(searchTerm));
        }

        // Note: UploaderEmail filtering would require integration with user management system
        // For now, we'll ignore this filter as we don't have user email data in DeletedItem
        // if (!string.IsNullOrWhiteSpace(uploaderEmail))
        // {
        //     // This would require joining with user data or storing email in DeletedItem
        // }

        // Date range filtering
        if (deletedAfter.HasValue)
        {
            query = query.Where(d => d.DeletedAt >= deletedAfter.Value);
        }

        if (deletedBefore.HasValue)
        {
            // Add one day to include the entire end date
            var endDate = deletedBefore.Value.Date.AddDays(1);
            query = query.Where(d => d.DeletedAt < endDate);
        }

        if (onlyRestorable)
        {
            // Only items that haven't been restored and are within 30 days
            query = query.Where(d => d.RestoredAt == null &&
                               d.DeletedAt >= DateTime.UtcNow.AddDays(-30));
        }

        // Order by deletion date descending (most recently deleted first)
        query = query.OrderByDescending(d => d.DeletedAt);

        var totalCount = await query.CountAsync(cancellationToken);
        var items = await query.Skip((page - 1) * pageSize).Take(pageSize).ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<DeletedItem?> GetByOriginalIdAsync(
        Guid originalId,
        DeletedItemType itemType,
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(d => d.OriginalId == originalId &&
                       d.ItemType == itemType &&
                       d.RestoredAt == null)
            .OrderByDescending(d => d.DeletedAt)
            .FirstOrDefaultAsync(cancellationToken);
    }

    public async Task<IEnumerable<DeletedItem>> GetItemsForPermanentDeletionAsync(
        CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-30);
        return await _dbSet
            .Where(d => d.DeletedAt <= cutoffDate && d.RestoredAt == null)
            .ToListAsync(cancellationToken);
    }

    public async Task<object> GetRestorationStatisticsAsync(
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        var userDeletedItems = _dbSet.Where(d => d.DeletedBy == userId);

        var statistics = new
        {
            totalDeleted = await userDeletedItems.CountAsync(cancellationToken),
            totalRestored = await userDeletedItems
                .Where(d => d.RestoredAt.HasValue)
                .CountAsync(cancellationToken),
            currentlyInRecycleBin = await userDeletedItems
                .Where(d => !d.RestoredAt.HasValue && d.DeletedAt >= DateTime.UtcNow.AddDays(-30))
                .CountAsync(cancellationToken),
            expiredItems = await userDeletedItems
                .Where(d => !d.RestoredAt.HasValue && d.DeletedAt < DateTime.UtcNow.AddDays(-30))
                .CountAsync(cancellationToken),
            deletedToday = await userDeletedItems
                .Where(d => d.DeletedAt.Date == DateTime.UtcNow.Date)
                .CountAsync(cancellationToken),
            deletedThisWeek = await userDeletedItems
                .Where(d => d.DeletedAt >= DateTime.UtcNow.AddDays(-7))
                .CountAsync(cancellationToken),
            deletedThisMonth = await userDeletedItems
                .Where(d => d.DeletedAt >= DateTime.UtcNow.AddDays(-30))
                .CountAsync(cancellationToken),
            folderCount = await userDeletedItems
                .Where(d => d.ItemType == DeletedItemType.Folder && !d.RestoredAt.HasValue)
                .CountAsync(cancellationToken),
            fileCount = await userDeletedItems
                .Where(d => d.ItemType == DeletedItemType.File && !d.RestoredAt.HasValue)
                .CountAsync(cancellationToken),
            totalSizeOfDeletedFiles = await userDeletedItems
                .Where(d => d.ItemType == DeletedItemType.File &&
                           !d.RestoredAt.HasValue &&
                           d.OriginalSize.HasValue)
                .SumAsync(d => d.OriginalSize ?? 0, cancellationToken)
        };

        return statistics;
    }

    public async Task<bool> CanRestoreItemAsync(
        Guid deletedItemId,
        Guid userId,
        CancellationToken cancellationToken = default)
    {
        var deletedItem = await _dbSet
            .FirstOrDefaultAsync(d => d.Id == deletedItemId && d.DeletedBy == userId, cancellationToken);

        return deletedItem?.CanRestore ?? false;
    }

    public async Task<IEnumerable<DeletedItem>> GetByParentFolderAsync(
        Guid parentFolderId,
        CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(d => d.ParentFolderId == parentFolderId && d.RestoredAt == null)
            .OrderByDescending(d => d.DeletedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<int> DeleteExpiredItemsAsync(CancellationToken cancellationToken = default)
    {
        var cutoffDate = DateTime.UtcNow.AddDays(-30);
        var expiredItems = await _dbSet
            .Where(d => d.DeletedAt <= cutoffDate && d.RestoredAt == null)
            .ToListAsync(cancellationToken);

        if (expiredItems.Any())
        {
            _dbSet.RemoveRange(expiredItems);
            await _context.SaveChangesAsync(cancellationToken);
        }

        return expiredItems.Count;
    }
}
