using Microsoft.EntityFrameworkCore;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Infrastructure.Data;

using VeasyFileManager.Application.Interfaces;

namespace VeasyFileManager.Infrastructure.Repositories;

/// <summary>
/// Repository implementation for File entity with specific file operations
/// </summary>
public class FileRepository(StorageDbContext context) : Repository<Domain.Entities.File>(context), IFileRepository
{
    public async Task<(IEnumerable<Domain.Entities.File> Files, int TotalCount)> GetUserFilesAsync(
        Guid userId,
        Guid? parentFolderId = null,
        int page = 1,
        int pageSize = 20,
        string? searchTerm = null,
        string sortBy = "CreatedAt",
        string sortDirection = "DESC",
        CancellationToken cancellationToken = default)
    {
        var queryable = _dbSet.Where(f => f.OwnerId == userId);

        if (parentFolderId.HasValue)
        {
            queryable = queryable.Where(f => f.ParentFolderId == parentFolderId.Value);
        }
        else
        {
            queryable = queryable.Where(f => f.ParentFolderId == null);
        }

        if (!string.IsNullOrEmpty(searchTerm))
        {
            queryable = queryable.Where(f => f.Name.Contains(searchTerm) || f.DisplayName.Contains(searchTerm));
        }

        // Apply sorting
        queryable = sortBy?.ToLower() switch
        {
            "name" => sortDirection?.ToUpper() == "DESC" ? queryable.OrderByDescending(f => f.Name) : queryable.OrderBy(f => f.Name),
            "size" => sortDirection?.ToUpper() == "DESC" ? queryable.OrderByDescending(f => f.FileSize) : queryable.OrderBy(f => f.FileSize),
            "createdat" => sortDirection?.ToUpper() == "DESC" ? queryable.OrderByDescending(f => f.CreatedAt) : queryable.OrderBy(f => f.CreatedAt),
            "updatedat" => sortDirection?.ToUpper() == "DESC" ? queryable.OrderByDescending(f => f.UpdatedAt) : queryable.OrderBy(f => f.UpdatedAt),
            _ => queryable.OrderByDescending(f => f.CreatedAt)
        };

        var totalCount = await queryable.CountAsync(cancellationToken);
        var items = await queryable
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<Domain.Entities.File?> GetByHashAsync(string md5Hash, CancellationToken cancellationToken = default)
    {
        return await _dbSet.FirstOrDefaultAsync(f => f.HashMd5 == md5Hash, cancellationToken);
    }

    public async Task<IEnumerable<Domain.Entities.File>> GetByParentFolderAsync(Guid parentFolderId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.ParentFolderId == parentFolderId)
            .OrderBy(f => f.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> HasUserAccessAsync(Guid userId, Guid fileId, PermissionType permission, CancellationToken cancellationToken = default)
    {
        // Check if user is the owner
        var file = await _dbSet.FirstOrDefaultAsync(f => f.Id == fileId, cancellationToken);
        if (file?.OwnerId == userId)
            return true;

        // Check explicit file permissions
        var hasPermission = await _context.FilePermissions
            .AnyAsync(fp => fp.FileId == fileId && 
                           fp.UserId == userId && 
                           fp.PermissionType == permission && 
                           fp.IsActive && 
                           (fp.ExpiresAt == null || fp.ExpiresAt > DateTime.UtcNow), 
                     cancellationToken);

        return hasPermission;
    }

    public async Task<IEnumerable<Domain.Entities.File>> GetFilesWithPermissionAsync(Guid userId, PermissionType permission, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.OwnerId == userId || f.Permissions.Any(fp => fp.UserId == userId && fp.PermissionType == permission && fp.IsActive))
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Domain.Entities.File>> GetByOwnerAsync(Guid ownerId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.OwnerId == ownerId)
            .OrderByDescending(f => f.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<(IEnumerable<Domain.Entities.File> Files, int TotalCount)> SearchFilesAsync(
        Guid userId,
        string searchTerm,
        int page = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var queryable = _dbSet.Where(f => f.OwnerId == userId && 
            (f.Name.Contains(searchTerm) || f.DisplayName.Contains(searchTerm)));

        var totalCount = await queryable.CountAsync(cancellationToken);
        var items = await queryable
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .OrderByDescending(f => f.UpdatedAt)
            .ToListAsync(cancellationToken);

        return (items, totalCount);
    }

    public async Task<IEnumerable<Domain.Entities.File>> GetByMimeTypeAsync(string mimeType, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.MimeType == mimeType)
            .OrderByDescending(f => f.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Domain.Entities.File>> GetRecentlyModifiedAsync(Guid userId, int count = 10, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(f => f.OwnerId == userId)
            .OrderByDescending(f => f.UpdatedAt)
            .Take(count)
            .ToListAsync(cancellationToken);
    }

    public async Task<Domain.Entities.File?> GetWithPermissionsAndSharesAsync(Guid fileId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(f => f.Permissions)
            .Include(f => f.Shares)
            .FirstOrDefaultAsync(f => f.Id == fileId, cancellationToken);
    }

    public async Task<Domain.Entities.FileShare?> GetFileShareByTokenAsync(string shareToken, CancellationToken cancellationToken = default)
    {
        return await _context.FileShares
            .FirstOrDefaultAsync(fs => fs.ShareToken == shareToken, cancellationToken);
    }

    public async Task<List<Domain.Entities.FileShare>> GetFileSharesAsync(Guid fileId, CancellationToken cancellationToken = default)
    {
        return await _context.FileShares
            .Where(fs => fs.FileId == fileId)
            .OrderByDescending(fs => fs.CreatedAt)
            .ToListAsync(cancellationToken);
    }

    public async Task<Domain.Entities.FileShare> AddFileShareAsync(Domain.Entities.FileShare fileShare, CancellationToken cancellationToken = default)
    {
        _context.FileShares.Add(fileShare);
        await _context.SaveChangesAsync(cancellationToken);
        return fileShare;
    }

    public async Task UpdateFileShareAsync(Domain.Entities.FileShare fileShare, CancellationToken cancellationToken = default)
    {
        _context.FileShares.Update(fileShare);
        await _context.SaveChangesAsync(cancellationToken);
    }
} 