namespace VeasyFileManager.Infrastructure.Configurations;

/// <summary>
/// Configuration options for JWT authentication
/// </summary>
public class JwtOptions
{
    public const string SectionName = "JwtOptions";

    /// <summary>
    /// JWT issuer (IdentityServer URL)
    /// </summary>
    public string Issuer { get; set; } = null!;

    /// <summary>
    /// JWT audience (API identifier)
    /// </summary>
    public string Audience { get; set; } = null!;

    /// <summary>
    /// Whether to require HTTPS for metadata (disable for development)
    /// </summary>
    public bool RequireHttpsMetadata { get; set; } = true;

    /// <summary>
    /// Authority URL for token validation (usually same as Issuer)
    /// </summary>
    public string Authority { get; set; } = null!;

    /// <summary>
    /// Whether to validate the issuer
    /// </summary>
    public bool ValidateIssuer { get; set; } = true;

    /// <summary>
    /// Whether to validate the audience
    /// </summary>
    public bool ValidateAudience { get; set; } = true;

    /// <summary>
    /// Whether to validate the token lifetime
    /// </summary>
    public bool ValidateLifetime { get; set; } = true;

    /// <summary>
    /// Clock skew tolerance for token validation
    /// </summary>
    public TimeSpan ClockSkew { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Validates that all required properties are set
    /// </summary>
    public bool IsValid()
    {
        return !string.IsNullOrEmpty(Issuer) &&
               !string.IsNullOrEmpty(Audience) &&
               !string.IsNullOrEmpty(Authority);
    }
} 