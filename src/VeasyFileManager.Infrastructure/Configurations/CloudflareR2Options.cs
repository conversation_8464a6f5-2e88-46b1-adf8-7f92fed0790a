namespace VeasyFileManager.Infrastructure.Configurations;

/// <summary>
/// Configuration options for Cloudflare R2 storage
/// </summary>
public class CloudflareR2Options
{
    public const string SectionName = "CloudflareR2";

    /// <summary>
    /// R2 Access Key ID
    /// </summary>
    public string AccessKeyId { get; set; } = string.Empty;

    /// <summary>
    /// R2 Secret Access Key
    /// </summary>
    public string SecretAccessKey { get; set; } = string.Empty;

    /// <summary>
    /// R2 Bucket Name
    /// </summary>
    public string BucketName { get; set; } = string.Empty;

    /// <summary>
    /// R2 Endpoint URL
    /// </summary>
    public string Endpoint { get; set; } = string.Empty;

    /// <summary>
    /// R2 Region (usually auto for R2)
    /// </summary>
    public string Region { get; set; } = "auto";

    /// <summary>
    /// Base path for file organization within bucket
    /// </summary>
    public string BasePath { get; set; } = "files";

    /// <summary>
    /// Maximum file size in bytes (default: 100MB)
    /// </summary>
    public long MaxFileSize { get; set; } = 100 * 1024 * 1024; // 100MB

    /// <summary>
    /// Multipart upload threshold in bytes (default: 10MB)
    /// </summary>
    public long MultipartThreshold { get; set; } = 10 * 1024 * 1024; // 10MB

    /// <summary>
    /// Default expiration time for presigned URLs
    /// </summary>
    public TimeSpan DefaultPresignedUrlExpiry { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// Validate the configuration
    /// </summary>
    /// <returns>True if configuration is valid</returns>
    public bool IsValid()
    {
        return !string.IsNullOrEmpty(AccessKeyId) &&
               !string.IsNullOrEmpty(SecretAccessKey) &&
               !string.IsNullOrEmpty(BucketName) &&
               !string.IsNullOrEmpty(Endpoint);
    }
}
