namespace VeasyFileManager.Infrastructure.Configurations;

/// <summary>
/// Configuration options for Google Drive integration
/// </summary>
public class GoogleDriveOptions
{
    /// <summary>
    /// Configuration section name
    /// </summary>
    public const string SectionName = "GoogleDrive";

    /// <summary>
    /// Google Drive client ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// Google Drive client secret
    /// </summary>
    public string ClientSecret { get; set; } = string.Empty;

    /// <summary>
    /// OAuth2 redirect URI
    /// </summary>
    public string RedirectUri { get; set; } = string.Empty;

    /// <summary>
    /// Path to service account key file
    /// </summary>
    public string ServiceAccountKeyPath { get; set; } = string.Empty;

    /// <summary>
    /// Google Drive API scopes
    /// </summary>
    public List<string> Scopes { get; set; } = new()
    {
        "https://www.googleapis.com/auth/drive",
        "https://www.googleapis.com/auth/drive.file"
    };

    /// <summary>
    /// Application name for Google Drive API
    /// </summary>
    public string ApplicationName { get; set; } = "VeasyFileManager";

    /// <summary>
    /// Rate limiting settings
    /// </summary>
    public int RequestsPerSecond { get; set; } = 10;

    /// <summary>
    /// Maximum retry attempts for failed requests
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Validate the configuration
    /// </summary>
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(ClientId) &&
               !string.IsNullOrWhiteSpace(ClientSecret) &&
               !string.IsNullOrWhiteSpace(ApplicationName) &&
               ((!string.IsNullOrWhiteSpace(RedirectUri)) || 
                (!string.IsNullOrWhiteSpace(ServiceAccountKeyPath)));
    }
} 