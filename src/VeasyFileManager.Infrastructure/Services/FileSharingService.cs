using System.Security.Cryptography;
using System.Text;
using AutoMapper;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Application.Extensions;

namespace VeasyFileManager.Infrastructure.Services;

/// <summary>
/// Service for managing file sharing functionality
/// </summary>
public class FileSharingService(
    IFileRepository fileRepository,
    IFilePermissionService permissionService,
    IMapper mapper,
    ILogger<FileSharingService> logger) : IFileSharingService
{
    private const int ShareTokenLength = 32;
    private const int SaltLength = 16;

    /// <summary>
    /// Create a new file share
    /// </summary>
    public async Task<FileShareDto> CreateShareAsync(Guid fileId, CreateShareRequest request, Guid createdBy, CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Creating share for file {FileId} by user {CreatedBy}", fileId, createdBy);

        // Verify file exists
        var file = await fileRepository.GetByIdAsync(fileId, cancellationToken);
        if (file == null)
        {
            logger.LogWarning("File {FileId} not found when creating share", fileId);
            throw new FileNotFoundException($"File with ID {fileId} not found");
        }

        // Check if user has SHARE permission
        if (!await permissionService.HasPermissionAsync(createdBy, fileId, PermissionType.Share, cancellationToken))
        {
            logger.LogWarning("User {CreatedBy} attempted to share file {FileId} without SHARE permission", createdBy, fileId);
            throw new UnauthorizedAccessException("You don't have permission to share this file");
        }

        // Create file share entity
        var fileShare = Domain.Entities.FileShare.Create(
            fileId: fileId,
            sharedBy: createdBy,
            shareType: request.ShareType,
            password: request.Password,
            expiresAt: request.ExpiresAt,
            maxDownloads: request.MaxDownloads
        );

        await fileRepository.AddFileShareAsync(fileShare, cancellationToken);

        logger.LogInformation("Created share {ShareToken} for file {FileId} by user {CreatedBy} - ShareType: {ShareType}, Expires: {ExpiresAt}, MaxDownloads: {MaxDownloads}",
            fileShare.ShareToken[..8] + "...", fileId, createdBy, request.ShareType, request.ExpiresAt, request.MaxDownloads);

        // Map to DTO
        var shareDto = mapper.Map<FileShareDto>(fileShare);
        
        return shareDto;
    }

    /// <summary>
    /// Get shared file information by token
    /// </summary>
    public async Task<FileDto> GetSharedFileAsync(string shareToken, string? password = null, CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Accessing shared file with token {ShareToken}", shareToken[..8] + "...");

        // Get file share by token
        var fileShare = await fileRepository.GetFileShareByTokenAsync(shareToken, cancellationToken);
        if (fileShare == null)
        {
            logger.LogWarning("Invalid share token used: {ShareToken}", shareToken[..8] + "...");
            throw new UnauthorizedAccessException("Invalid or expired share link");
        }

        // Check if share is active
        if (!fileShare.IsActive)
        {
            logger.LogWarning("Inactive share token accessed: {ShareToken}", shareToken[..8] + "...");
            throw new UnauthorizedAccessException("This share link has been disabled");
        }

        // Check expiration
        if (fileShare.ExpiresAt.HasValue && fileShare.ExpiresAt < DateTime.UtcNow)
        {
            logger.LogWarning("Expired share token accessed: {ShareToken}, expired at {ExpiresAt}", 
                shareToken[..8] + "...", fileShare.ExpiresAt);
            throw new UnauthorizedAccessException("This share link has expired");
        }

        // Check download limit
        if (fileShare.MaxDownloads.HasValue && fileShare.DownloadCount >= fileShare.MaxDownloads)
        {
            logger.LogWarning("Download limit exceeded for share token: {ShareToken}, count: {DownloadCount}, limit: {MaxDownloads}", 
                shareToken[..8] + "...", fileShare.DownloadCount, fileShare.MaxDownloads);
            throw new UnauthorizedAccessException("Download limit has been exceeded for this share link");
        }

        // Check password if required
        if (!string.IsNullOrEmpty(fileShare.PasswordHash))
        {
            if (string.IsNullOrEmpty(password))
            {
                logger.LogWarning("Password required but not provided for share token: {ShareToken}", shareToken[..8] + "...");
                throw new UnauthorizedAccessException("Password is required to access this file");
            }

            if (!VerifyPassword(password, fileShare.PasswordHash))
            {
                logger.LogWarning("Invalid password provided for share token: {ShareToken}", shareToken[..8] + "...");
                throw new UnauthorizedAccessException("Invalid password");
            }
        }

        // Get the file
        var file = await fileRepository.GetByIdAsync(fileShare.FileId, cancellationToken);
        if (file == null || file.IsDeleted)
        {
            logger.LogWarning("Shared file {FileId} not found or deleted for token {ShareToken}", 
                fileShare.FileId, shareToken[..8] + "...");
            throw new FileNotFoundException("The shared file is no longer available");
        }

        logger.LogInformation("Shared file accessed - ShareToken: {ShareToken}, FileId: {FileId}, DownloadCount: {DownloadCount}",
            shareToken[..8] + "...", fileShare.FileId, fileShare.DownloadCount);

        return mapper.Map<FileDto>(file);
    }

    /// <summary>
    /// Disable a file share
    /// </summary>
    public async Task DisableShareAsync(string shareToken, Guid disabledBy, CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Disabling share token {ShareToken} by user {DisabledBy}", 
            shareToken[..8] + "...", disabledBy);

        var fileShare = await fileRepository.GetFileShareByTokenAsync(shareToken, cancellationToken);
        if (fileShare == null)
        {
            logger.LogWarning("Share token not found when trying to disable: {ShareToken}", shareToken[..8] + "...");
            throw new FileNotFoundException("Share not found");
        }

        // Check if user has permission to disable the share
        if (fileShare.SharedBy != disabledBy)
        {
            // Check if user has admin permission on the file
            if (!await permissionService.HasPermissionAsync(disabledBy, fileShare.FileId, PermissionType.Admin, cancellationToken))
            {
                logger.LogWarning("User {DisabledBy} attempted to disable share {ShareToken} without permission", 
                    disabledBy, shareToken[..8] + "...");
                throw new UnauthorizedAccessException("You don't have permission to disable this share");
            }
        }

        fileShare.Deactivate();
        await fileRepository.UpdateFileShareAsync(fileShare, cancellationToken);

        logger.LogInformation("Share disabled - ShareToken: {ShareToken}, FileId: {FileId}, DisabledBy: {DisabledBy}",
            shareToken[..8] + "...", fileShare.FileId, disabledBy);
    }

    /// <summary>
    /// Increment download count for a share
    /// </summary>
    public async Task IncrementDownloadCountAsync(string shareToken, CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Incrementing download count for share token {ShareToken}", shareToken[..8] + "...");

        var fileShare = await fileRepository.GetFileShareByTokenAsync(shareToken, cancellationToken);
        if (fileShare == null)
        {
            return; // Silently ignore if share not found
        }

        fileShare.IncrementDownloadCount();
        await fileRepository.SaveChangesAsync(cancellationToken);

        logger.LogDebug("Download count incremented to {DownloadCount} for share token {ShareToken}", 
            fileShare.DownloadCount, shareToken[..8] + "...");
    }

    /// <summary>
    /// Get all shares for a file
    /// </summary>
    public async Task<List<FileShareDto>> GetFileSharesAsync(Guid fileId, CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Getting shares for file {FileId}", fileId);

        var fileShares = await fileRepository.GetFileSharesAsync(fileId, cancellationToken);
        
        return fileShares.Select(share => mapper.Map<FileShareDto>(share)).ToList();
    }

    /// <summary>
    /// Generate a cryptographically secure random token
    /// </summary>
    private static string GenerateSecureToken()
    {
        using var rng = RandomNumberGenerator.Create();
        var tokenBytes = new byte[ShareTokenLength];
        rng.GetBytes(tokenBytes);
        
        // Convert to URL-safe base64
        return Convert.ToBase64String(tokenBytes)
            .Replace('+', '-')
            .Replace('/', '_')
            .TrimEnd('=');
    }

    /// <summary>
    /// Hash a password using PBKDF2
    /// </summary>
    private static string HashPassword(string password)
    {
        using var rng = RandomNumberGenerator.Create();
        var salt = new byte[SaltLength];
        rng.GetBytes(salt);

        using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000, HashAlgorithmName.SHA256);
        var hash = pbkdf2.GetBytes(32);

        // Combine salt and hash
        var hashBytes = new byte[SaltLength + 32];
        Array.Copy(salt, 0, hashBytes, 0, SaltLength);
        Array.Copy(hash, 0, hashBytes, SaltLength, 32);

        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// Verify a password against its hash
    /// </summary>
    private static bool VerifyPassword(string password, string hashedPassword)
    {
        try
        {
            var hashBytes = Convert.FromBase64String(hashedPassword);
            if (hashBytes.Length != SaltLength + 32)
                return false;

            var salt = new byte[SaltLength];
            Array.Copy(hashBytes, 0, salt, 0, SaltLength);

            using var pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000, HashAlgorithmName.SHA256);
            var hash = pbkdf2.GetBytes(32);

            for (int i = 0; i < 32; i++)
            {
                if (hashBytes[i + SaltLength] != hash[i])
                    return false;
            }

            return true;
        }
        catch
        {
            return false;
        }
    }
} 