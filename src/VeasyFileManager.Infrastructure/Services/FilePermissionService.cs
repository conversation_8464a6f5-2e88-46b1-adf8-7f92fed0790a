using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Infrastructure.Data;

namespace VeasyFileManager.Infrastructure.Services;

/// <summary>
/// Service for managing file and folder permissions
/// </summary>
public class FilePermissionService(StorageDbContext context, ILogger<FilePermissionService> logger) : IFilePermissionService
{
    /// <summary>
    /// Check if a user has specific permission on a file
    /// </summary>
    public async Task<bool> HasPermissionAsync(Guid userId, Guid fileId, PermissionType permission, CancellationToken cancellationToken = default)
    {
        if (userId == Guid.Empty)
        {
            logger.LogWarning("Invalid userId provided: {UserId}", userId);
            return false;
        }

        if (fileId == Guid.Empty)
        {
            logger.LogWarning("Invalid fileId provided: {FileId}", fileId);
            return false;
        }

        logger.LogDebug("Checking permission {Permission} for user {UserId} on file {FileId}", 
            permission, userId, fileId);

        // Check if user is the owner of the file
        var file = await context.Files
            .AsNoTracking()
            .FirstOrDefaultAsync(f => f.Id == fileId, cancellationToken);

        if (file == null)
        {
            logger.LogWarning("File not found: {FileId}", fileId);
            return false;
        }

        // Owner has all permissions
        if (file.OwnerId == userId)
        {
            logger.LogDebug("User {UserId} is owner of file {FileId}, granting {Permission}", 
                userId, fileId, permission);
            return true;
        }

        // Check direct file permissions
        var hasDirectPermission = await context.FilePermissions
            .AsNoTracking()
            .AnyAsync(fp => fp.FileId == fileId &&
                           fp.UserId == userId &&
                           fp.PermissionType >= permission &&
                           fp.IsActive &&
                           (fp.ExpiresAt == null || fp.ExpiresAt > DateTime.UtcNow),
                     cancellationToken);

        if (hasDirectPermission)
        {
            logger.LogDebug("User {UserId} has direct permission {Permission} on file {FileId}", 
                userId, permission, fileId);
            return true;
        }

        // Check inherited permissions from parent folders
        if (file.ParentFolderId.HasValue)
        {
            var hasInheritedPermission = await HasInheritedFolderPermissionAsync(userId, file.ParentFolderId.Value, permission, cancellationToken);
            if (hasInheritedPermission)
            {
                logger.LogDebug("User {UserId} has inherited permission {Permission} on file {FileId} from parent folder", 
                    userId, permission, fileId);
            }
            return hasInheritedPermission;
        }

        logger.LogDebug("User {UserId} does not have permission {Permission} on file {FileId}", 
            userId, permission, fileId);
        return false;
    }

    /// <summary>
    /// Check if a user has specific permission on a folder
    /// </summary>
    public async Task<bool> HasFolderPermissionAsync(Guid userId, Guid folderId, PermissionType permission, CancellationToken cancellationToken = default)
    {
        if (userId == Guid.Empty)
        {
            logger.LogWarning("Invalid userId provided: {UserId}", userId);
            return false;
        }

        if (folderId == Guid.Empty)
        {
            logger.LogWarning("Invalid folderId provided: {FolderId}", folderId);
            return false;
        }

        logger.LogDebug("Checking folder permission {Permission} for user {UserId} on folder {FolderId}", 
            permission, userId, folderId);

        // Check if user is the owner of the folder
        var folder = await context.Folders
            .AsNoTracking()
            .FirstOrDefaultAsync(f => f.Id == folderId, cancellationToken);

        if (folder == null)
        {
            logger.LogWarning("Folder not found: {FolderId}", folderId);
            return false;
        }

        // Owner has all permissions
        if (folder.OwnerId == userId)
        {
            logger.LogDebug("User {UserId} is owner of folder {FolderId}, granting {Permission}", 
                userId, folderId, permission);
            return true;
        }

        // Check direct folder permissions
        var hasDirectPermission = await context.FolderPermissions
            .AsNoTracking()
            .AnyAsync(fp => fp.FolderId == folderId &&
                           fp.UserId == userId &&
                           fp.PermissionType >= permission &&
                           fp.IsActive &&
                           (fp.ExpiresAt == null || fp.ExpiresAt > DateTime.UtcNow),
                     cancellationToken);

        if (hasDirectPermission)
        {
            logger.LogDebug("User {UserId} has direct permission {Permission} on folder {FolderId}", 
                userId, permission, folderId);
            return true;
        }

        // Check inherited permissions from parent folders
        if (folder.ParentFolderId.HasValue)
        {
            var hasInheritedPermission = await HasInheritedFolderPermissionAsync(userId, folder.ParentFolderId.Value, permission, cancellationToken);
            if (hasInheritedPermission)
            {
                logger.LogDebug("User {UserId} has inherited permission {Permission} on folder {FolderId} from parent folder", 
                    userId, permission, folderId);
            }
            return hasInheritedPermission;
        }

        logger.LogDebug("User {UserId} does not have permission {Permission} on folder {FolderId}", 
            userId, permission, folderId);
        return false;
    }

    /// <summary>
    /// Grant permission to a user for a file
    /// </summary>
    public async Task<Guid> GrantFilePermissionAsync(Guid fileId, Guid? userId, Guid? roleId, PermissionType permission, Guid grantedBy, DateTime? expiresAt = null, CancellationToken cancellationToken = default)
    {
        // Enhanced validation
        if (fileId == Guid.Empty)
        {
            logger.LogError("Invalid fileId provided: {FileId}", fileId);
            throw new ArgumentException("File ID cannot be empty", nameof(fileId));
        }

        if (grantedBy == Guid.Empty)
        {
            logger.LogError("Invalid grantedBy provided: {GrantedBy}", grantedBy);
            throw new ArgumentException("Granted by user ID cannot be empty", nameof(grantedBy));
        }

        if (userId == null && roleId == null)
        {
            logger.LogError("Both userId and roleId are null for file permission grant");
            throw new ArgumentException("Either User ID or Role ID must be provided");
        }

        if (expiresAt.HasValue && expiresAt.Value <= DateTime.UtcNow)
        {
            logger.LogError("Invalid expiration date: {ExpiresAt}", expiresAt);
            throw new ArgumentException("Expiration date must be in the future", nameof(expiresAt));
        }

        logger.LogInformation("Granting permission {Permission} to user {UserId}/role {RoleId} on file {FileId} by {GrantedBy}", 
            permission, userId, roleId, fileId, grantedBy);

        // Validate that granter has Admin permission on the file
        if (!await HasPermissionAsync(grantedBy, fileId, PermissionType.Admin, cancellationToken))
        {
            logger.LogWarning("User {GrantedBy} attempted to grant permission without Admin access to file {FileId}", 
                grantedBy, fileId);
            throw new UnauthorizedAccessException("You don't have permission to grant access to this file");
        }

        // Check if permission already exists
        var existingPermission = await context.FilePermissions
            .FirstOrDefaultAsync(fp => fp.FileId == fileId &&
                                      fp.UserId == userId &&
                                      fp.RoleId == roleId &&
                                      fp.PermissionType == permission &&
                                      fp.IsActive,
                               cancellationToken);

        if (existingPermission != null)
        {
            // Update expiration if different - need to recreate since properties are read-only
            if (existingPermission.ExpiresAt != expiresAt)
            {
                logger.LogInformation("Updating existing permission {PermissionId} expiration from {OldExpiration} to {NewExpiration}", 
                    existingPermission.Id, existingPermission.ExpiresAt, expiresAt);

                existingPermission.Deactivate();
                context.FilePermissions.Remove(existingPermission);
                
                var updatedPermission = FilePermission.Create(fileId, userId, roleId, permission, grantedBy, expiresAt);
                context.FilePermissions.Add(updatedPermission);
                await context.SaveChangesAsync(cancellationToken);

                logger.LogInformation("Successfully updated file permission {PermissionId} for user {UserId}/role {RoleId} on file {FileId}", 
                    updatedPermission.Id, userId, roleId, fileId);
                return updatedPermission.Id;
            }

            logger.LogInformation("Permission already exists and is identical: {PermissionId}", existingPermission.Id);
            return existingPermission.Id;
        }

        // Create new permission using factory method
        var filePermission = FilePermission.Create(fileId, userId, roleId, permission, grantedBy, expiresAt);

        context.FilePermissions.Add(filePermission);
        await context.SaveChangesAsync(cancellationToken);

        logger.LogInformation("Successfully granted file permission {PermissionId} to user {UserId}/role {RoleId} on file {FileId}", 
            filePermission.Id, userId, roleId, fileId);
        return filePermission.Id;
    }

    /// <summary>
    /// Grant permission to a user for a folder
    /// </summary>
    public async Task<Guid> GrantFolderPermissionAsync(Guid folderId, Guid? userId, Guid? roleId, PermissionType permission, Guid grantedBy, bool inheritToChildren = true, DateTime? expiresAt = null, CancellationToken cancellationToken = default)
    {
        // Enhanced validation
        if (folderId == Guid.Empty)
        {
            logger.LogError("Invalid folderId provided: {FolderId}", folderId);
            throw new ArgumentException("Folder ID cannot be empty", nameof(folderId));
        }

        if (grantedBy == Guid.Empty)
        {
            logger.LogError("Invalid grantedBy provided: {GrantedBy}", grantedBy);
            throw new ArgumentException("Granted by user ID cannot be empty", nameof(grantedBy));
        }

        if (userId == null && roleId == null)
        {
            logger.LogError("Both userId and roleId are null for folder permission grant");
            throw new ArgumentException("Either User ID or Role ID must be provided");
        }

        if (expiresAt.HasValue && expiresAt.Value <= DateTime.UtcNow)
        {
            logger.LogError("Invalid expiration date: {ExpiresAt}", expiresAt);
            throw new ArgumentException("Expiration date must be in the future", nameof(expiresAt));
        }

        logger.LogInformation("Granting folder permission {Permission} to user {UserId}/role {RoleId} on folder {FolderId} by {GrantedBy}, inherit={InheritToChildren}", 
            permission, userId, roleId, folderId, grantedBy, inheritToChildren);

        // Validate that granter has Admin permission on the folder
        if (!await HasFolderPermissionAsync(grantedBy, folderId, PermissionType.Admin, cancellationToken))
        {
            logger.LogWarning("User {GrantedBy} attempted to grant permission without Admin access to folder {FolderId}", 
                grantedBy, folderId);
            throw new UnauthorizedAccessException("You don't have permission to grant access to this folder");
        }

        // Check if permission already exists
        var existingPermission = await context.FolderPermissions
            .FirstOrDefaultAsync(fp => fp.FolderId == folderId &&
                                      fp.UserId == userId &&
                                      fp.RoleId == roleId &&
                                      fp.PermissionType == permission &&
                                      fp.IsActive,
                               cancellationToken);

        if (existingPermission != null)
        {
            // Update settings if different - need to recreate since most properties are read-only
            if (existingPermission.ExpiresAt != expiresAt || existingPermission.InheritToChildren != inheritToChildren)
            {
                logger.LogInformation("Updating existing folder permission {PermissionId} expiration from {OldExpiration} to {NewExpiration}, inherit from {OldInherit} to {NewInherit}", 
                    existingPermission.Id, existingPermission.ExpiresAt, expiresAt, existingPermission.InheritToChildren, inheritToChildren);

                existingPermission.Deactivate();
                context.FolderPermissions.Remove(existingPermission);
                
                var updatedPermission = FolderPermission.Create(folderId, userId, roleId, permission, grantedBy, expiresAt, inheritToChildren);
                context.FolderPermissions.Add(updatedPermission);
                await context.SaveChangesAsync(cancellationToken);

                logger.LogInformation("Successfully updated folder permission {PermissionId} for user {UserId}/role {RoleId} on folder {FolderId}", 
                    updatedPermission.Id, userId, roleId, folderId);
                return updatedPermission.Id;
            }

            logger.LogInformation("Folder permission already exists and is identical: {PermissionId}", existingPermission.Id);
            return existingPermission.Id;
        }

        // Create new permission using factory method
        var folderPermission = FolderPermission.Create(folderId, userId, roleId, permission, grantedBy, expiresAt, inheritToChildren);

        context.FolderPermissions.Add(folderPermission);
        await context.SaveChangesAsync(cancellationToken);

        logger.LogInformation("Successfully granted folder permission {PermissionId} to user {UserId}/role {RoleId} on folder {FolderId}", 
            folderPermission.Id, userId, roleId, folderId);
        return folderPermission.Id;
    }

    /// <summary>
    /// Revoke a specific permission
    /// </summary>
    public async Task<bool> RevokePermissionAsync(Guid permissionId, Guid revokedBy, CancellationToken cancellationToken = default)
    {
        if (permissionId == Guid.Empty)
        {
            logger.LogError("Invalid permissionId provided: {PermissionId}", permissionId);
            throw new ArgumentException("Permission ID cannot be empty", nameof(permissionId));
        }

        if (revokedBy == Guid.Empty)
        {
            logger.LogError("Invalid revokedBy provided: {RevokedBy}", revokedBy);
            throw new ArgumentException("Revoked by user ID cannot be empty", nameof(revokedBy));
        }

        logger.LogInformation("Attempting to revoke permission {PermissionId} by user {RevokedBy}", 
            permissionId, revokedBy);

        // Try to find as file permission first
        var filePermission = await context.FilePermissions
            .FirstOrDefaultAsync(fp => fp.Id == permissionId, cancellationToken);

        if (filePermission != null)
        {
            // Check if revoker has Admin permission on the file
            if (!await HasPermissionAsync(revokedBy, filePermission.FileId, PermissionType.Admin, cancellationToken))
            {
                logger.LogWarning("User {RevokedBy} attempted to revoke permission without Admin access to file {FileId}", 
                    revokedBy, filePermission.FileId);
                throw new UnauthorizedAccessException("You don't have permission to revoke access to this file");
            }

            filePermission.Deactivate();
            await context.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully revoked file permission {PermissionId} by user {RevokedBy}", 
                permissionId, revokedBy);
            return true;
        }

        // Try to find as folder permission
        var folderPermission = await context.FolderPermissions
            .FirstOrDefaultAsync(fp => fp.Id == permissionId, cancellationToken);

        if (folderPermission != null)
        {
            // Check if revoker has Admin permission on the folder
            if (!await HasFolderPermissionAsync(revokedBy, folderPermission.FolderId, PermissionType.Admin, cancellationToken))
            {
                logger.LogWarning("User {RevokedBy} attempted to revoke permission without Admin access to folder {FolderId}", 
                    revokedBy, folderPermission.FolderId);
                throw new UnauthorizedAccessException("You don't have permission to revoke access to this folder");
            }

            folderPermission.Deactivate();
            await context.SaveChangesAsync(cancellationToken);

            logger.LogInformation("Successfully revoked folder permission {PermissionId} by user {RevokedBy}", 
                permissionId, revokedBy);
            return true;
        }

        logger.LogWarning("Permission not found: {PermissionId}", permissionId);
        return false;
    }

    /// <summary>
    /// Get all permissions for a file
    /// </summary>
    public async Task<List<FilePermission>> GetFilePermissionsAsync(Guid fileId, CancellationToken cancellationToken = default)
    {
        if (fileId == Guid.Empty)
        {
            logger.LogWarning("Invalid fileId provided: {FileId}", fileId);
            return new List<FilePermission>();
        }

        logger.LogDebug("Getting permissions for file {FileId}", fileId);

        var permissions = await context.FilePermissions
            .AsNoTracking()
            .Where(fp => fp.FileId == fileId && fp.IsActive)
            .OrderBy(fp => fp.GrantedAt)
            .ToListAsync(cancellationToken);

        logger.LogDebug("Found {Count} permissions for file {FileId}", permissions.Count, fileId);
        return permissions;
    }

    /// <summary>
    /// Get all permissions for a folder
    /// </summary>
    public async Task<List<FolderPermission>> GetFolderPermissionsAsync(Guid folderId, CancellationToken cancellationToken = default)
    {
        if (folderId == Guid.Empty)
        {
            logger.LogWarning("Invalid folderId provided: {FolderId}", folderId);
            return new List<FolderPermission>();
        }

        logger.LogDebug("Getting permissions for folder {FolderId}", folderId);

        var permissions = await context.FolderPermissions
            .AsNoTracking()
            .Where(fp => fp.FolderId == folderId && fp.IsActive)
            .OrderBy(fp => fp.GrantedAt)
            .ToListAsync(cancellationToken);

        logger.LogDebug("Found {Count} permissions for folder {FolderId}", permissions.Count, folderId);
        return permissions;
    }

    /// <summary>
    /// Get effective permissions for a user on a file (including inherited permissions)
    /// </summary>
    public async Task<List<PermissionType>> GetUserFilePermissionsAsync(Guid userId, Guid fileId, CancellationToken cancellationToken = default)
    {
        if (userId == Guid.Empty || fileId == Guid.Empty)
        {
            logger.LogWarning("Invalid parameters: userId={UserId}, fileId={FileId}", userId, fileId);
            return new List<PermissionType>();
        }

        logger.LogDebug("Getting effective permissions for user {UserId} on file {FileId}", userId, fileId);

        var permissions = new HashSet<PermissionType>();

        // Check if user is the owner
        var file = await context.Files
            .AsNoTracking()
            .FirstOrDefaultAsync(f => f.Id == fileId, cancellationToken);

        if (file != null && file.OwnerId == userId)
        {
            permissions.UnionWith(Enum.GetValues<PermissionType>());
            logger.LogDebug("User {UserId} is owner of file {FileId}, has all permissions", userId, fileId);
            return permissions.ToList();
        }

        // Get direct file permissions
        var directPermissions = await context.FilePermissions
            .AsNoTracking()
            .Where(fp => fp.FileId == fileId &&
                        fp.UserId == userId &&
                        fp.IsActive &&
                        (fp.ExpiresAt == null || fp.ExpiresAt > DateTime.UtcNow))
            .Select(fp => fp.PermissionType)
            .ToListAsync(cancellationToken);

        permissions.UnionWith(directPermissions);

        // Get inherited permissions from folders
        if (file?.ParentFolderId.HasValue == true)
        {
            var inheritedPermissions = await GetInheritedFolderPermissionsAsync(userId, file.ParentFolderId.Value, cancellationToken);
            permissions.UnionWith(inheritedPermissions);
        }

        logger.LogDebug("User {UserId} has {Count} effective permissions on file {FileId}: {Permissions}", 
            userId, permissions.Count, fileId, string.Join(", ", permissions));
        return permissions.ToList();
    }

    /// <summary>
    /// Get effective permissions for a user on a folder (including inherited permissions)
    /// </summary>
    public async Task<List<PermissionType>> GetUserFolderPermissionsAsync(Guid userId, Guid folderId, CancellationToken cancellationToken = default)
    {
        if (userId == Guid.Empty || folderId == Guid.Empty)
        {
            logger.LogWarning("Invalid parameters: userId={UserId}, folderId={FolderId}", userId, folderId);
            return new List<PermissionType>();
        }

        logger.LogDebug("Getting effective permissions for user {UserId} on folder {FolderId}", userId, folderId);

        var permissions = new HashSet<PermissionType>();

        // Check if user is the owner
        var folder = await context.Folders
            .AsNoTracking()
            .FirstOrDefaultAsync(f => f.Id == folderId, cancellationToken);

        if (folder != null && folder.OwnerId == userId)
        {
            permissions.UnionWith(Enum.GetValues<PermissionType>());
            logger.LogDebug("User {UserId} is owner of folder {FolderId}, has all permissions", userId, folderId);
            return permissions.ToList();
        }

        // Get direct folder permissions
        var directPermissions = await context.FolderPermissions
            .AsNoTracking()
            .Where(fp => fp.FolderId == folderId &&
                        fp.UserId == userId &&
                        fp.IsActive &&
                        (fp.ExpiresAt == null || fp.ExpiresAt > DateTime.UtcNow))
            .Select(fp => fp.PermissionType)
            .ToListAsync(cancellationToken);

        permissions.UnionWith(directPermissions);

        // Get inherited permissions from parent folders
        if (folder?.ParentFolderId.HasValue == true)
        {
            var inheritedPermissions = await GetInheritedFolderPermissionsAsync(userId, folder.ParentFolderId.Value, cancellationToken);
            permissions.UnionWith(inheritedPermissions);
        }

        logger.LogDebug("User {UserId} has {Count} effective permissions on folder {FolderId}: {Permissions}", 
            userId, permissions.Count, folderId, string.Join(", ", permissions));
        return permissions.ToList();
    }

    /// <summary>
    /// Bulk grant permissions to multiple users/roles on a file
    /// </summary>
    public async Task<List<Guid>> GrantFilePermissionsBulkAsync(Guid fileId, List<(Guid? UserId, Guid? RoleId, PermissionType Permission)> permissions, Guid grantedBy, DateTime? expiresAt = null, CancellationToken cancellationToken = default)
    {
        if (fileId == Guid.Empty)
        {
            throw new ArgumentException("File ID cannot be empty", nameof(fileId));
        }

        if (permissions == null || !permissions.Any())
        {
            throw new ArgumentException("Permissions list cannot be empty", nameof(permissions));
        }

        logger.LogInformation("Bulk granting {Count} permissions on file {FileId} by user {GrantedBy}", 
            permissions.Count, fileId, grantedBy);

        var permissionIds = new List<Guid>();

        foreach (var (userId, roleId, permission) in permissions)
        {
            try
            {
                var permissionId = await GrantFilePermissionAsync(fileId, userId, roleId, permission, grantedBy, expiresAt, cancellationToken);
                permissionIds.Add(permissionId);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to grant permission {Permission} to user {UserId}/role {RoleId} on file {FileId}", 
                    permission, userId, roleId, fileId);
                // Continue with other permissions
            }
        }

        logger.LogInformation("Successfully granted {SuccessCount}/{TotalCount} permissions on file {FileId}", 
            permissionIds.Count, permissions.Count, fileId);
        return permissionIds;
    }

    /// <summary>
    /// Check if a user has any of the specified permissions on a file
    /// </summary>
    public async Task<bool> HasAnyPermissionAsync(Guid userId, Guid fileId, IEnumerable<PermissionType> permissions, CancellationToken cancellationToken = default)
    {
        if (permissions == null || !permissions.Any())
        {
            return false;
        }

        foreach (var permission in permissions)
        {
            if (await HasPermissionAsync(userId, fileId, permission, cancellationToken))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// Clean up expired permissions
    /// </summary>
    public async Task<int> CleanupExpiredPermissionsAsync(CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting cleanup of expired permissions");

        var expiredFilePermissions = await context.FilePermissions
            .Where(fp => fp.IsActive && fp.ExpiresAt.HasValue && fp.ExpiresAt < DateTime.UtcNow)
            .ToListAsync(cancellationToken);

        var expiredFolderPermissions = await context.FolderPermissions
            .Where(fp => fp.IsActive && fp.ExpiresAt.HasValue && fp.ExpiresAt < DateTime.UtcNow)
            .ToListAsync(cancellationToken);

        var totalExpired = expiredFilePermissions.Count + expiredFolderPermissions.Count;

        if (totalExpired > 0)
        {
            foreach (var permission in expiredFilePermissions)
            {
                permission.Deactivate();
            }

            foreach (var permission in expiredFolderPermissions)
            {
                permission.Deactivate();
            }

            await context.SaveChangesAsync(cancellationToken);
            logger.LogInformation("Cleaned up {Count} expired permissions ({FileCount} file, {FolderCount} folder)", 
                totalExpired, expiredFilePermissions.Count, expiredFolderPermissions.Count);
        }
        else
        {
            logger.LogDebug("No expired permissions found during cleanup");
        }

        return totalExpired;
    }

    /// <summary>
    /// Helper method to check inherited folder permissions
    /// </summary>
    private async Task<bool> HasInheritedFolderPermissionAsync(Guid userId, Guid folderId, PermissionType permission, CancellationToken cancellationToken)
    {
        // Check permissions on this folder
        var hasPermission = await context.FolderPermissions
            .AsNoTracking()
            .AnyAsync(fp => fp.FolderId == folderId &&
                           fp.UserId == userId &&
                           fp.PermissionType >= permission &&
                           fp.IsActive &&
                           fp.InheritToChildren &&
                           (fp.ExpiresAt == null || fp.ExpiresAt > DateTime.UtcNow),
                     cancellationToken);

        if (hasPermission)
            return true;

        // Check parent folder recursively
        var folder = await context.Folders
            .AsNoTracking()
            .FirstOrDefaultAsync(f => f.Id == folderId, cancellationToken);

        if (folder?.ParentFolderId.HasValue == true)
        {
            return await HasInheritedFolderPermissionAsync(userId, folder.ParentFolderId.Value, permission, cancellationToken);
        }

        return false;
    }

    /// <summary>
    /// Helper method to get inherited folder permissions
    /// </summary>
    private async Task<List<PermissionType>> GetInheritedFolderPermissionsAsync(Guid userId, Guid folderId, CancellationToken cancellationToken)
    {
        var permissions = new HashSet<PermissionType>();

        // Get permissions on this folder
        var folderPermissions = await context.FolderPermissions
            .AsNoTracking()
            .Where(fp => fp.FolderId == folderId &&
                        fp.UserId == userId &&
                        fp.IsActive &&
                        fp.InheritToChildren &&
                        (fp.ExpiresAt == null || fp.ExpiresAt > DateTime.UtcNow))
            .Select(fp => fp.PermissionType)
            .ToListAsync(cancellationToken);

        permissions.UnionWith(folderPermissions);

        // Check parent folder recursively
        var folder = await context.Folders
            .AsNoTracking()
            .FirstOrDefaultAsync(f => f.Id == folderId, cancellationToken);

        if (folder?.ParentFolderId.HasValue == true)
        {
            var parentPermissions = await GetInheritedFolderPermissionsAsync(userId, folder.ParentFolderId.Value, cancellationToken);
            permissions.UnionWith(parentPermissions);
        }

        return permissions.ToList();
    }
} 