using Google.Apis.Auth.OAuth2;
using Google.Apis.Drive.v3;
using Google.Apis.Drive.v3.Data;
using Google.Apis.Services;
using Google.Apis.Upload;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Extensions;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Infrastructure.Configurations;

namespace VeasyFileManager.Infrastructure.Services;

/// <summary>
/// Service for Google Drive API integration
/// </summary>
public class GoogleDriveService : IGoogleDriveService, IDisposable
{
    private readonly GoogleDriveOptions _options;
    private readonly ILogger<GoogleDriveService> _logger;
    private readonly DriveService _driveService;
    private bool _disposed;

    public GoogleDriveService(IOptions<GoogleDriveOptions> options, ILogger<GoogleDriveService> logger)
    {
        _options = options.Value;
        _logger = logger;

        try
        {
            // Initialize Google Drive service
            _driveService = InitializeDriveService();
            _logger.LogInformation("Google Drive service initialized successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize Google Drive service");
            throw;
        }
    }

    /// <summary>
    /// Upload file to Google Drive
    /// </summary>
    public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string? parentFolderId = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Uploading file {FileName} to Google Drive, parent folder: {ParentFolderId}", fileName, parentFolderId);

        try
        {
            var fileMetadata = new Google.Apis.Drive.v3.Data.File
            {
                Name = fileName,
                Parents = !string.IsNullOrEmpty(parentFolderId) ? new List<string> { parentFolderId } : null
            };

            FilesResource.CreateMediaUpload request;
            
            // Reset stream position
            if (fileStream.CanSeek)
                fileStream.Position = 0;

            request = _driveService.Files.Create(fileMetadata, fileStream, GetMimeType(fileName));
            request.Fields = "id,name,size,mimeType,createdTime,modifiedTime";

            var uploadProgress = await request.UploadAsync(cancellationToken);

            if (uploadProgress.Status == UploadStatus.Failed)
            {
                _logger.LogError("Failed to upload file {FileName} to Google Drive: {Error}", fileName, uploadProgress.Exception?.Message);
                throw new InvalidOperationException($"Failed to upload file: {uploadProgress.Exception?.Message}");
            }

            var uploadedFile = request.ResponseBody;
            if (uploadedFile?.Id == null)
            {
                throw new InvalidOperationException("Upload succeeded but no file ID was returned");
            }

            _logger.LogFileOperation("GoogleDrive upload completed", Guid.Empty, Guid.Empty, fileName, 
                $"Google Drive ID: {uploadedFile.Id}, Size: {uploadedFile.Size}");

            return uploadedFile.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file {FileName} to Google Drive", fileName);
            throw;
        }
    }

    /// <summary>
    /// Download file from Google Drive
    /// </summary>
    public async Task<Stream> DownloadFileAsync(string fileId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Downloading file {FileId} from Google Drive", fileId);

        try
        {
            var request = _driveService.Files.Get(fileId);
            var stream = new MemoryStream();

            await request.DownloadAsync(stream, cancellationToken);
            stream.Position = 0;

            _logger.LogFileOperation("GoogleDrive download completed", Guid.Empty, Guid.Empty, fileId);
            return stream;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading file {FileId} from Google Drive", fileId);
            throw;
        }
    }

    /// <summary>
    /// Delete file from Google Drive
    /// </summary>
    public async Task DeleteFileAsync(string fileId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Deleting file {FileId} from Google Drive", fileId);

        try
        {
            await _driveService.Files.Delete(fileId).ExecuteAsync(cancellationToken);
            _logger.LogFileOperation("GoogleDrive delete completed", Guid.Empty, Guid.Empty, fileId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file {FileId} from Google Drive", fileId);
            throw;
        }
    }

    /// <summary>
    /// Get file metadata from Google Drive
    /// </summary>
    public async Task<GoogleDriveFileDto> GetFileAsync(string fileId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting file metadata {FileId} from Google Drive", fileId);

        try
        {
            var request = _driveService.Files.Get(fileId);
            request.Fields = "id,name,size,mimeType,createdTime,modifiedTime,parents,md5Checksum";

            var file = await request.ExecuteAsync(cancellationToken);

            return new GoogleDriveFileDto
            {
                Id = file.Id,
                Name = file.Name,
                Size = file.Size,
                MimeType = file.MimeType,
                CreatedTime = file.CreatedTime ?? DateTime.UtcNow,
                ModifiedTime = file.ModifiedTime ?? DateTime.UtcNow,
                Parents = file.Parents?.ToList() ?? new List<string>(),
                Md5Checksum = file.Md5Checksum
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting file {FileId} from Google Drive", fileId);
            throw;
        }
    }

    /// <summary>
    /// List files in Google Drive
    /// </summary>
    public async Task<List<GoogleDriveFileDto>> ListFilesAsync(string? parentFolderId = null, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Listing files from Google Drive, parent folder: {ParentFolderId}", parentFolderId);

        try
        {
            var request = _driveService.Files.List();
            request.Fields = "files(id,name,size,mimeType,createdTime,modifiedTime,parents,md5Checksum)";
            request.PageSize = 1000;

            if (!string.IsNullOrEmpty(parentFolderId))
            {
                request.Q = $"'{parentFolderId}' in parents and trashed=false";
            }
            else
            {
                request.Q = "trashed=false";
            }

            var response = await request.ExecuteAsync(cancellationToken);
            
            return response.Files?.Select(file => new GoogleDriveFileDto
            {
                Id = file.Id,
                Name = file.Name,
                Size = file.Size,
                MimeType = file.MimeType,
                CreatedTime = file.CreatedTime ?? DateTime.UtcNow,
                ModifiedTime = file.ModifiedTime ?? DateTime.UtcNow,
                Parents = file.Parents?.ToList() ?? new List<string>(),
                Md5Checksum = file.Md5Checksum
            }).ToList() ?? new List<GoogleDriveFileDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing files from Google Drive");
            throw;
        }
    }

    /// <summary>
    /// Move file to different folder in Google Drive
    /// </summary>
    public async Task<string> MoveFileAsync(string fileId, string newParentId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Moving file {FileId} to new parent {NewParentId} in Google Drive", fileId, newParentId);

        try
        {
            // First get the current file to find existing parents
            var getRequest = _driveService.Files.Get(fileId);
            getRequest.Fields = "parents";
            var file = await getRequest.ExecuteAsync(cancellationToken);

            // Move the file by updating its parents
            var updateRequest = _driveService.Files.Update(new Google.Apis.Drive.v3.Data.File(), fileId);
            updateRequest.AddParents = newParentId;
            updateRequest.RemoveParents = string.Join(",", file.Parents ?? new List<string>());
            updateRequest.Fields = "id,parents";

            var updatedFile = await updateRequest.ExecuteAsync(cancellationToken);

            _logger.LogFileOperation("GoogleDrive move completed", Guid.Empty, Guid.Empty, fileId, 
                $"New parent: {newParentId}");

            return updatedFile.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error moving file {FileId} in Google Drive", fileId);
            throw;
        }
    }

    /// <summary>
    /// Create folder in Google Drive
    /// </summary>
    public async Task<GoogleDriveFileDto> CreateFolderAsync(string name, string? parentId = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating folder {FolderName} in Google Drive, parent: {ParentId}", name, parentId);

        try
        {
            var folderMetadata = new Google.Apis.Drive.v3.Data.File
            {
                Name = name,
                MimeType = "application/vnd.google-apps.folder",
                Parents = !string.IsNullOrEmpty(parentId) ? new List<string> { parentId } : null
            };

            var request = _driveService.Files.Create(folderMetadata);
            request.Fields = "id,name,mimeType,createdTime,modifiedTime,parents";

            var folder = await request.ExecuteAsync(cancellationToken);

            _logger.LogInformation("Created folder {FolderName} with ID {FolderId} in Google Drive", name, folder.Id);

            return new GoogleDriveFileDto
            {
                Id = folder.Id,
                Name = folder.Name,
                MimeType = folder.MimeType,
                CreatedTime = folder.CreatedTime ?? DateTime.UtcNow,
                ModifiedTime = folder.ModifiedTime ?? DateTime.UtcNow,
                Parents = folder.Parents?.ToList() ?? new List<string>(),
                IsFolder = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating folder {FolderName} in Google Drive", name);
            throw;
        }
    }

    /// <summary>
    /// Sync user files with Google Drive
    /// </summary>
    public async Task SyncUserFilesAsync(Guid userId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting sync for user {UserId} with Google Drive", userId);

        try
        {
            // This is a placeholder for full sync logic
            // In a complete implementation, this would:
            // 1. Get all user files from local database
            // 2. Get all files from Google Drive
            // 3. Compare and identify differences
            // 4. Sync changes bidirectionally
            // 5. Update sync status records

            _logger.LogSyncOperation("GoogleDrive", Guid.Empty, "sync_started", $"User: {userId}");

            // For now, just log that sync was requested
            await Task.Delay(100, cancellationToken); // Simulate work

            _logger.LogSyncOperation("GoogleDrive", Guid.Empty, "sync_completed", $"User: {userId}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during sync for user {UserId}", userId);
            _logger.LogSyncOperation("GoogleDrive", Guid.Empty, "sync_failed", $"User: {userId}, Error: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Initialize Google Drive service with authentication
    /// </summary>
    private DriveService InitializeDriveService()
    {
        GoogleCredential credential;

        if (!string.IsNullOrEmpty(_options.ServiceAccountKeyPath) && System.IO.File.Exists(_options.ServiceAccountKeyPath))
        {
            // Use service account authentication
            _logger.LogDebug("Initializing Google Drive service with service account");
            
            using var stream = new FileStream(_options.ServiceAccountKeyPath, FileMode.Open, FileAccess.Read);
            credential = GoogleCredential.FromStream(stream)
                .CreateScoped(DriveService.Scope.Drive);
        }
        else if (!string.IsNullOrEmpty(_options.ClientId) && !string.IsNullOrEmpty(_options.ClientSecret))
        {
            // Use OAuth2 authentication - Note: This requires user consent flow
            // For server-to-server scenarios, service account is recommended
            _logger.LogDebug("Initializing Google Drive service with OAuth2");
            
            throw new NotImplementedException("OAuth2 user consent flow is not implemented. Please use service account authentication instead.");
        }
        else
        {
            throw new InvalidOperationException("Google Drive authentication not configured. Please provide either service account key path or OAuth2 credentials.");
        }

        return new DriveService(new BaseClientService.Initializer
        {
            HttpClientInitializer = credential,
            ApplicationName = "VeasyFileManager",
        });
    }

    /// <summary>
    /// Get MIME type based on file extension
    /// </summary>
    private static string GetMimeType(string fileName)
    {
        var extension = Path.GetExtension(fileName).ToLowerInvariant();
        
        return extension switch
        {
            ".txt" => "text/plain",
            ".pdf" => "application/pdf",
            ".doc" => "application/msword",
            ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".xls" => "application/vnd.ms-excel",
            ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".gif" => "image/gif",
            ".zip" => "application/zip",
            ".rar" => "application/x-rar-compressed",
            ".mp4" => "video/mp4",
            ".mp3" => "audio/mpeg",
            _ => "application/octet-stream"
        };
    }

    /// <summary>
    /// Dispose resources
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _driveService?.Dispose();
            _disposed = true;
        }
    }
} 