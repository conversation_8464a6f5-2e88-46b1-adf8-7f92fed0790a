using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Cryptography;
using System.Text;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Infrastructure.Configurations;
using ApplicationByteRange = VeasyFileManager.Application.Interfaces.ByteRange;

namespace VeasyFileManager.Infrastructure.Services;

/// <summary>
/// Cloudflare R2 storage service implementation using S3-compatible API
/// </summary>
public sealed class CloudflareR2StorageService : IFileStorageService, IDisposable
{
    private readonly AmazonS3Client _s3Client;
    private readonly CloudflareR2Options _options;
    private readonly ILogger<CloudflareR2StorageService> _logger;
    private bool _disposed = false;

    public CloudflareR2StorageService(
        IOptions<CloudflareR2Options> options,
        ILogger<CloudflareR2StorageService> logger)
    {
        _options = options.Value ?? throw new ArgumentNullException(nameof(options));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        if (!_options.IsValid())
        {
            throw new InvalidOperationException("Cloudflare R2 configuration is invalid. Please check your settings.");
        }

        var config = new AmazonS3Config
        {
            ServiceURL = _options.Endpoint,
            ForcePathStyle = true,
            SignatureVersion = "4",
        };

        _s3Client = new AmazonS3Client(_options.AccessKeyId, _options.SecretAccessKey, config);
        _logger.LogInformation("CloudflareR2StorageService initialized for bucket: {BucketName}", _options.BucketName);
    }

    public async Task<string> UploadFileAsync(Stream fileStream, string fileName, string contentType, CancellationToken cancellationToken = default)
    {
        try
        {
            if (fileStream == null)
                throw new ArgumentNullException(nameof(fileStream));

            if (string.IsNullOrEmpty(fileName))
                throw new ArgumentException("File name cannot be null or empty", nameof(fileName));

            // Generate unique file path
            var filePath = GenerateFilePath(fileName);

            // Check file size
            if (fileStream.Length > _options.MaxFileSize)
            {
                throw new InvalidOperationException($"File size {fileStream.Length} exceeds maximum allowed size {_options.MaxFileSize}");
            }

            // Use multipart upload for large files to avoid streaming signature issues
            if (fileStream.Length >= _options.MultipartThreshold)
            {
                return await UploadLargeFileAsync(fileStream, filePath, fileName, contentType, cancellationToken);
            }

            // For smaller files, buffer the stream to avoid streaming signature issues
            using var memoryStream = new MemoryStream();
            await fileStream.CopyToAsync(memoryStream, cancellationToken);
            memoryStream.Position = 0;

            var request = new PutObjectRequest
            {
                BucketName = _options.BucketName,
                Key = filePath,
                InputStream = memoryStream,
                ContentType = contentType,
                ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                CannedACL = S3CannedACL.Private,
                DisablePayloadSigning = true
            };

            // Add metadata - encode filename as Base64 to handle Unicode characters
            var encodedFileName = Convert.ToBase64String(Encoding.UTF8.GetBytes(fileName));
            request.Metadata.Add("original-filename-base64", encodedFileName);
            request.Metadata.Add("upload-timestamp", DateTime.UtcNow.ToString("O"));

            var response = await _s3Client.PutObjectAsync(request, cancellationToken);

            _logger.LogInformation("File uploaded successfully: {FilePath}, ETag: {ETag}", filePath, response.ETag);
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error uploading file {FileName} to R2 storage", fileName);
            throw;
        }
    }

    public async Task<Stream> DownloadFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            var request = new GetObjectRequest
            {
                BucketName = _options.BucketName,
                Key = filePath
            };

            var response = await _s3Client.GetObjectAsync(request, cancellationToken);

            _logger.LogInformation("File downloaded successfully: {FilePath}", filePath);
            return response.ResponseStream;
        }
        catch (AmazonS3Exception ex) when (ex.ErrorCode == "NoSuchKey")
        {
            _logger.LogWarning("File not found: {FilePath}", filePath);
            throw new FileNotFoundException($"File not found: {filePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading file {FilePath} from R2 storage", filePath);
            throw;
        }
    }

    /// <summary>
    /// DELETE FILE METHOD - DISABLED FOR SAFETY
    /// This method is intentionally disabled to prevent any file deletion on R2 storage.
    /// Files on R2 should never be deleted to ensure data preservation.
    /// The method is kept here for reference but will never execute.
    /// </summary>
    /*
    public async Task DeleteFileAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            var request = new DeleteObjectRequest
            {
                BucketName = _options.BucketName,
                Key = filePath
            };

            await _s3Client.DeleteObjectAsync(request, cancellationToken);

            _logger.LogInformation("File deleted successfully: {FilePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting file {FilePath} from R2 storage", filePath);
            throw;
        }
    }
    */

    public async Task<string> CopyFileAsync(string sourceFilePath, string destinationFilePath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(sourceFilePath))
                throw new ArgumentException("Source file path cannot be null or empty", nameof(sourceFilePath));

            if (string.IsNullOrEmpty(destinationFilePath))
                throw new ArgumentException("Destination file path cannot be null or empty", nameof(destinationFilePath));

            var request = new CopyObjectRequest
            {
                SourceBucket = _options.BucketName,
                SourceKey = sourceFilePath,
                DestinationBucket = _options.BucketName,
                DestinationKey = destinationFilePath,
                ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                CannedACL = S3CannedACL.Private
            };

            var response = await _s3Client.CopyObjectAsync(request, cancellationToken);

            _logger.LogInformation("File copied successfully from {SourcePath} to {DestinationPath}", sourceFilePath, destinationFilePath);
            return destinationFilePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error copying file from {SourcePath} to {DestinationPath}", sourceFilePath, destinationFilePath);
            throw;
        }
    }

    public async Task<bool> FileExistsAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            var request = new GetObjectMetadataRequest
            {
                BucketName = _options.BucketName,
                Key = filePath
            };

            await _s3Client.GetObjectMetadataAsync(request, cancellationToken);
            return true;
        }
        catch (AmazonS3Exception ex) when (ex.ErrorCode == "NoSuchKey" || ex.ErrorCode == "NotFound")
        {
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if file exists: {FilePath}", filePath);
            throw;
        }
    }

    public async Task<string> GeneratePresignedUrlAsync(string filePath, TimeSpan expiration, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            var request = new GetPreSignedUrlRequest
            {
                BucketName = _options.BucketName,
                Key = filePath,
                Verb = HttpVerb.GET,
                Expires = DateTime.UtcNow.Add(expiration)
            };

            var presignedUrl = await _s3Client.GetPreSignedURLAsync(request);

            _logger.LogInformation("Presigned URL generated for {FilePath}, expires at {ExpiryTime}", filePath, request.Expires);
            return presignedUrl;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating presigned URL for file {FilePath}", filePath);
            throw;
        }
    }

    public async Task<StorageFileMetadata> GetFileMetadataAsync(string filePath, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            var request = new GetObjectMetadataRequest
            {
                BucketName = _options.BucketName,
                Key = filePath
            };

            var response = await _s3Client.GetObjectMetadataAsync(request, cancellationToken);

            // Extract original filename from metadata if available
            var originalFileName = string.Empty;
            if (response.Metadata.Keys.Contains("original-filename-base64"))
            {
                originalFileName = DecodeFileName(response.Metadata["original-filename-base64"]);
            }

            // Extract upload timestamp from metadata if available
            DateTime? uploadTimestamp = null;
            if (response.Metadata.Keys.Contains("upload-timestamp"))
            {
                if (DateTime.TryParse(response.Metadata["upload-timestamp"], out var parsedTimestamp))
                {
                    uploadTimestamp = parsedTimestamp;
                }
            }

            return new StorageFileMetadata
            {
                FilePath = filePath,
                FileSize = response.ContentLength,
                ContentType = response.Headers.ContentType,
                LastModified = response.LastModified,
                ETag = response.ETag?.Trim('"') ?? string.Empty,
                OriginalFileName = originalFileName,
                UploadTimestamp = uploadTimestamp
            };
        }
        catch (AmazonS3Exception ex) when (ex.ErrorCode == "NoSuchKey")
        {
            throw new FileNotFoundException($"File not found: {filePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting metadata for file {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Upload large files using multipart upload to avoid streaming signature issues
    /// </summary>
    private async Task<string> UploadLargeFileAsync(Stream fileStream, string filePath, string fileName, string contentType, CancellationToken cancellationToken)
    {
        var partSize = 5 * 1024 * 1024; // 5MB parts (minimum for S3/R2)
        var uploadId = string.Empty;

        try
        {
            // Initialize multipart upload
            var initRequest = new InitiateMultipartUploadRequest
            {
                BucketName = _options.BucketName,
                Key = filePath,
                ContentType = contentType,
                ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256,
                CannedACL = S3CannedACL.Private
            };

            // Add metadata - encode filename as Base64 to handle Unicode characters
            var encodedFileName = Convert.ToBase64String(Encoding.UTF8.GetBytes(fileName));
            initRequest.Metadata.Add("original-filename-base64", encodedFileName);
            initRequest.Metadata.Add("upload-timestamp", DateTime.UtcNow.ToString("O"));

            var initResponse = await _s3Client.InitiateMultipartUploadAsync(initRequest, cancellationToken);
            uploadId = initResponse.UploadId;

            _logger.LogInformation("Initiated multipart upload for {FilePath}, UploadId: {UploadId}", filePath, uploadId);

            var uploadedParts = new List<PartETag>();
            var partNumber = 1;
            var totalBytes = fileStream.Length;
            var uploadedBytes = 0L;

            // Upload parts
            while (uploadedBytes < totalBytes)
            {
                var remainingBytes = totalBytes - uploadedBytes;
                var currentPartSize = (int)Math.Min(partSize, remainingBytes);

                var buffer = new byte[currentPartSize];
                var bytesRead = await fileStream.ReadAsync(buffer, 0, currentPartSize, cancellationToken);

                if (bytesRead == 0)
                    break;

                using var partStream = new MemoryStream(buffer, 0, bytesRead);

                var uploadPartRequest = new UploadPartRequest
                {
                    BucketName = _options.BucketName,
                    Key = filePath,
                    UploadId = uploadId,
                    PartNumber = partNumber,
                    InputStream = partStream,
                    PartSize = bytesRead
                };

                var uploadPartResponse = await _s3Client.UploadPartAsync(uploadPartRequest, cancellationToken);
                uploadedParts.Add(new PartETag(partNumber, uploadPartResponse.ETag));

                uploadedBytes += bytesRead;
                partNumber++;

                _logger.LogDebug("Uploaded part {PartNumber} of {FilePath}, Progress: {Progress}%",
                    partNumber - 1, filePath, (uploadedBytes * 100) / totalBytes);
            }

            // Complete multipart upload
            var completeRequest = new CompleteMultipartUploadRequest
            {
                BucketName = _options.BucketName,
                Key = filePath,
                UploadId = uploadId,
                PartETags = uploadedParts
            };

            var completeResponse = await _s3Client.CompleteMultipartUploadAsync(completeRequest, cancellationToken);

            _logger.LogInformation("Multipart upload completed successfully: {FilePath}, ETag: {ETag}", filePath, completeResponse.ETag);
            return filePath;
        }
        catch (Exception ex)
        {
            // Abort multipart upload on failure
            if (!string.IsNullOrEmpty(uploadId))
            {
                try
                {
                    var abortRequest = new AbortMultipartUploadRequest
                    {
                        BucketName = _options.BucketName,
                        Key = filePath,
                        UploadId = uploadId
                    };

                    await _s3Client.AbortMultipartUploadAsync(abortRequest, cancellationToken);
                    _logger.LogInformation("Aborted multipart upload: {FilePath}, UploadId: {UploadId}", filePath, uploadId);
                }
                catch (Exception abortEx)
                {
                    _logger.LogWarning(abortEx, "Failed to abort multipart upload: {FilePath}, UploadId: {UploadId}", filePath, uploadId);
                }
            }

            _logger.LogError(ex, "Error during multipart upload of {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Generate a unique file path in the storage
    /// </summary>
    private string GenerateFilePath(string fileName)
    {
        var fileExtension = Path.GetExtension(fileName);
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

        // Sanitize filename
        var sanitizedFileName = SanitizeFileName(fileNameWithoutExtension);

        // Generate unique identifier
        var uniqueId = Guid.NewGuid().ToString("N");
        var timestamp = DateTime.UtcNow.ToString("yyyy/MM/dd");

        return $"{_options.BasePath}/{timestamp}/{uniqueId}_{sanitizedFileName}{fileExtension}";
    }

    /// <summary>
    /// Sanitize filename to remove invalid characters and handle Unicode properly
    /// </summary>
    private static string SanitizeFileName(string fileName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        var sanitized = new StringBuilder();

        foreach (var c in fileName)
        {
            if (!invalidChars.Contains(c))
            {
                // Handle spaces and special Unicode characters
                if (c == ' ')
                {
                    sanitized.Append('_');
                }
                else if (char.IsControl(c) || c > 127) // Non-ASCII characters
                {
                    // Convert Unicode to safe representation
                    sanitized.Append($"_{(int)c:X4}_");
                }
                else
                {
                    sanitized.Append(c);
                }
            }
        }

        var result = sanitized.ToString();

        // Ensure filename is not empty and not too long
        if (string.IsNullOrEmpty(result))
        {
            result = "file";
        }

        // Truncate if too long (keeping some buffer for GUID and timestamp)
        if (result.Length > 100)
        {
            result = result.Substring(0, 100);
        }

        return result;
    }

    /// <summary>
    /// Download a partial range of a file from storage
    /// </summary>
    public async Task<PartialFileStream> DownloadFileRangeAsync(string filePath, long rangeStart, long? rangeEnd = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            // First get file metadata to validate range
            var metadata = await GetFileMetadataAsync(filePath, cancellationToken);
            var actualRangeEnd = rangeEnd ?? metadata.FileSize - 1;

            // Validate range
            if (rangeStart < 0 || rangeStart >= metadata.FileSize || actualRangeEnd >= metadata.FileSize || rangeStart > actualRangeEnd)
            {
                throw new ArgumentOutOfRangeException($"Invalid range: {rangeStart}-{actualRangeEnd} for file size {metadata.FileSize}");
            }

            var request = new GetObjectRequest
            {
                BucketName = _options.BucketName,
                Key = filePath,
                ByteRange = new Amazon.S3.Model.ByteRange(rangeStart, actualRangeEnd)
            };

            var response = await _s3Client.GetObjectAsync(request, cancellationToken);

            _logger.LogInformation("File range downloaded successfully: {FilePath}, Range: {Start}-{End}",
                filePath, rangeStart, actualRangeEnd);

            return new PartialFileStream
            {
                Stream = response.ResponseStream,
                RangeStart = rangeStart,
                RangeEnd = actualRangeEnd,
                ContentLength = actualRangeEnd - rangeStart + 1,
                TotalFileSize = metadata.FileSize,
                ContentType = metadata.ContentType,
                ETag = metadata.ETag
            };
        }
        catch (AmazonS3Exception ex) when (ex.ErrorCode == "NoSuchKey")
        {
            _logger.LogWarning("File not found: {FilePath}", filePath);
            throw new FileNotFoundException($"File not found: {filePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading file range {FilePath} ({Start}-{End})", filePath, rangeStart, rangeEnd);
            throw;
        }
    }

    /// <summary>
    /// Get file content for streaming with optional range support
    /// </summary>
    public async Task<FileStreamResponse> GetFileStreamAsync(string filePath, ApplicationByteRange? range = null, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrEmpty(filePath))
                throw new ArgumentException("File path cannot be null or empty", nameof(filePath));

            // Get file metadata first
            var metadata = await GetFileMetadataAsync(filePath, cancellationToken);

            if (range != null)
            {
                // Validate and adjust range
                if (!range.IsValid(metadata.FileSize))
                {
                    throw new ArgumentOutOfRangeException(nameof(range),
                        $"Invalid range: {range.Start}-{range.End} for file size {metadata.FileSize}");
                }

                var actualEnd = range.GetActualEnd(metadata.FileSize);
                var request = new GetObjectRequest
                {
                    BucketName = _options.BucketName,
                    Key = filePath,
                    ByteRange = new Amazon.S3.Model.ByteRange(range.Start, actualEnd)
                };

                var response = await _s3Client.GetObjectAsync(request, cancellationToken);

                _logger.LogInformation("File range streamed: {FilePath}, Range: {Start}-{End}",
                    filePath, range.Start, actualEnd);

                return new FileStreamResponse
                {
                    Stream = response.ResponseStream,
                    Metadata = metadata,
                    IsPartialContent = true,
                    Range = range,
                    ContentLength = range.GetContentLength(metadata.FileSize)
                };
            }
            else
            {
                // Get full file
                var request = new GetObjectRequest
                {
                    BucketName = _options.BucketName,
                    Key = filePath
                };

                var response = await _s3Client.GetObjectAsync(request, cancellationToken);

                _logger.LogInformation("Full file streamed: {FilePath}", filePath);

                return new FileStreamResponse
                {
                    Stream = response.ResponseStream,
                    Metadata = metadata,
                    IsPartialContent = false,
                    Range = null,
                    ContentLength = metadata.FileSize
                };
            }
        }
        catch (AmazonS3Exception ex) when (ex.ErrorCode == "NoSuchKey")
        {
            _logger.LogWarning("File not found for streaming: {FilePath}", filePath);
            throw new FileNotFoundException($"File not found: {filePath}", filePath);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error streaming file {FilePath}", filePath);
            throw;
        }
    }

    /// <summary>
    /// Decode Base64-encoded filename back to original Unicode string
    /// </summary>
    public static string DecodeFileName(string base64FileName)
    {
        try
        {
            var bytes = Convert.FromBase64String(base64FileName);
            return Encoding.UTF8.GetString(bytes);
        }
        catch
        {
            // Fallback to original string if decode fails
            return base64FileName;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    private void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _s3Client?.Dispose();
            _disposed = true;
        }
    }
}
