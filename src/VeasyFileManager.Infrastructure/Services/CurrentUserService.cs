using System.Security.Claims;
using Microsoft.AspNetCore.Http;
using VeasyFileManager.Application.Interfaces;

namespace VeasyFileManager.Infrastructure.Services;

/// <summary>
/// Service for getting current user information from the HTTP context
/// </summary>
public class CurrentUserService(IHttpContextAccessor httpContextAccessor) : ICurrentUserService
{
    /// <summary>
    /// Get the current user's ID from the JWT token
    /// </summary>
    public Guid? UserId
    {
        get
        {
            var userIdClaim = httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value
                           ?? httpContextAccessor.HttpContext?.User?.FindFirst("sub")?.Value
                           ?? httpContextAccessor.HttpContext?.User?.FindFirst("user_id")?.Value;

            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }
    }

    /// <summary>
    /// Get the current user's email from the JWT token
    /// </summary>
    public string? Email
    {
        get
        {
            return httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Email)?.Value
                ?? httpContextAccessor.HttpContext?.User?.FindFirst("email")?.Value;
        }
    }

    /// <summary>
    /// Get the current user's name from the JWT token
    /// </summary>
    public string? Name
    {
        get
        {
            return httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Name)?.Value
                ?? httpContextAccessor.HttpContext?.User?.FindFirst("name")?.Value
                ?? httpContextAccessor.HttpContext?.User?.FindFirst("preferred_username")?.Value;
        }
    }

    /// <summary>
    /// Check if the current user is authenticated
    /// </summary>
    public bool IsAuthenticated => httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;

    /// <summary>
    /// Get the current user's IP address
    /// </summary>
    public string? IpAddress
    {
        get
        {
            var context = httpContextAccessor.HttpContext;
            if (context == null) return null;

            // Check for forwarded header first (for load balancers/proxies)
            var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',').FirstOrDefault()?.Trim();
            }

            // Check for real IP header
            var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            // Fall back to connection remote IP
            return context.Connection.RemoteIpAddress?.ToString();
        }
    }

    /// <summary>
    /// Get the current request's correlation ID
    /// </summary>
    public string? CorrelationId
    {
        get
        {
            return httpContextAccessor.HttpContext?.Items["CorrelationId"]?.ToString()
                ?? httpContextAccessor.HttpContext?.Request.Headers["X-Correlation-ID"].FirstOrDefault();
        }
    }

    /// <summary>
    /// Get all user claims
    /// </summary>
    public IEnumerable<KeyValuePair<string, string>> Claims
    {
        get
        {
            var user = httpContextAccessor.HttpContext?.User;
            if (user?.Claims == null)
                return Enumerable.Empty<KeyValuePair<string, string>>();

            return user.Claims.Select(c => new KeyValuePair<string, string>(c.Type, c.Value));
        }
    }

    /// <summary>
    /// Get a specific claim value
    /// </summary>
    public string? GetClaim(string claimType)
    {
        return httpContextAccessor.HttpContext?.User?.FindFirst(claimType)?.Value;
    }
} 