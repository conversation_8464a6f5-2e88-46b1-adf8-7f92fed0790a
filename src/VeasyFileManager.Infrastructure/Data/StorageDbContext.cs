using Microsoft.EntityFrameworkCore;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Infrastructure.Data;

/// <summary>
/// Database context for the file management storage schema
/// </summary>
public class StorageDbContext(DbContextOptions<StorageDbContext> options)
    : DbContext(options)
{
    public DbSet<Domain.Entities.File> Files { get; set; } = null!;
    public DbSet<Folder> Folders { get; set; } = null!;
    public DbSet<FilePermission> FilePermissions { get; set; } = null!;
    public DbSet<FolderPermission> FolderPermissions { get; set; } = null!;
    public DbSet<Domain.Entities.FileShare> FileShares { get; set; } = null!;
    public DbSet<FolderShare> FolderShares { get; set; } = null!;
    public DbSet<SyncStatus> SyncStatuses { get; set; } = null!;
    public DbSet<DeletedItem> DeletedItems { get; set; } = null!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply entity configurations
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(StorageDbContext).Assembly);

        // Configure soft delete global query filter
        modelBuilder.Entity<Domain.Entities.File>()
            .HasQueryFilter(f => !f.IsDeleted);

        modelBuilder.Entity<Folder>()
            .HasQueryFilter(f => !f.IsDeleted);

        // Configure query filter for SyncStatus to match File's filter
        modelBuilder.Entity<SyncStatus>()
            .HasQueryFilter(ss => !ss.File.IsDeleted);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update timestamps before saving
        var entries = ChangeTracker.Entries()
            .Where(e => e.Entity is Domain.Entities.File || e.Entity is Folder)
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.Entity is Domain.Entities.File file)
            {
                if (entry.State == EntityState.Added)
                {
                    // CreatedAt is set in the entity constructor
                }
                if (entry.State == EntityState.Modified)
                {
                    // UpdatedAt is managed in the entity methods
                }
            }
            else if (entry.Entity is Folder folder)
            {
                if (entry.State == EntityState.Added)
                {
                    // CreatedAt is set in the entity constructor
                }
                if (entry.State == EntityState.Modified)
                {
                    // UpdatedAt is managed in the entity methods
                }
            }
        }

        return await base.SaveChangesAsync(cancellationToken);
    }
}
