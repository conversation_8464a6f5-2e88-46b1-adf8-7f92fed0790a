using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Infrastructure.Data.Configurations;

public class FileShareConfiguration : IEntityTypeConfiguration<Domain.Entities.FileShare>
{
    public void Configure(EntityTypeBuilder<Domain.Entities.FileShare> builder)
    {
        builder.ToTable("FileShares");

        builder.HasKey(fs => fs.Id);

        builder.Property(fs => fs.Id)
            .ValueGeneratedOnAdd();

        builder.Property(fs => fs.FileId)
            .IsRequired();

        builder.Property(fs => fs.ShareToken)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(fs => fs.SharedBy)
            .IsRequired();

        builder.Property(fs => fs.ShareType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(fs => fs.PasswordHash)
            .HasMaxLength(255);

        builder.Property(fs => fs.DownloadCount)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(fs => fs.CreatedAt)
            .IsRequired();

        builder.Property(fs => fs.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(fs => fs.ShareToken)
            .IsUnique()
            .HasDatabaseName("idx_file_shares_token");

        builder.HasIndex(fs => fs.FileId)
            .HasDatabaseName("idx_file_shares_file_id");

        builder.HasIndex(fs => fs.SharedBy)
            .HasDatabaseName("idx_file_shares_shared_by");

        builder.HasIndex(fs => fs.ExpiresAt)
            .HasDatabaseName("idx_file_shares_expires_at");

        builder.HasIndex(fs => fs.IsActive)
            .HasDatabaseName("idx_file_shares_is_active");

        builder.HasIndex(fs => fs.CreatedAt)
            .HasDatabaseName("idx_file_shares_created_at");
    }
} 