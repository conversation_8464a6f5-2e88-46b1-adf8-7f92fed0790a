using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Infrastructure.Data.Configurations;

public class SyncStatusConfiguration : IEntityTypeConfiguration<SyncStatus>
{
    public void Configure(EntityTypeBuilder<SyncStatus> builder)
    {
        builder.ToTable("SyncStatus");

        builder.HasKey(ss => ss.Id);

        builder.Property(ss => ss.Id)
            .ValueGeneratedOnAdd();

        builder.Property(ss => ss.FileId)
            .IsRequired();

        builder.Property(ss => ss.Provider)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(ss => ss.ExternalId)
            .HasMaxLength(255);

        builder.Property(ss => ss.Status)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20)
            .HasDefaultValue(Domain.Enums.SyncStatus.Pending);

        builder.Property(ss => ss.ErrorMessage)
            .HasMaxLength(1000);

        builder.Property(ss => ss.RetryCount)
            .IsRequired()
            .HasDefaultValue(0);

        // Relationships
        builder.HasOne(ss => ss.File)
            .WithMany()
            .HasForeignKey(ss => ss.FileId)
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(ss => new { ss.FileId, ss.Provider })
            .HasDatabaseName("idx_sync_status_file_provider");

        builder.HasIndex(ss => ss.Status)
            .HasDatabaseName("idx_sync_status_status");

        builder.HasIndex(ss => ss.LastSyncAt)
            .HasDatabaseName("idx_sync_status_last_sync");

        builder.HasIndex(ss => ss.RetryCount)
            .HasDatabaseName("idx_sync_status_retry_count");

        builder.HasIndex(ss => ss.ExternalId)
            .HasDatabaseName("idx_sync_status_external_id");
    }
} 