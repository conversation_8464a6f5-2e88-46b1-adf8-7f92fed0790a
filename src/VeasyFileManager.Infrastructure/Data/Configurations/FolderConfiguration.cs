using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Infrastructure.Data.Configurations;

public class FolderConfiguration : IEntityTypeConfiguration<Folder>
{
    public void Configure(EntityTypeBuilder<Folder> builder)
    {
        builder.ToTable("Folders");

        builder.HasKey(f => f.Id);

        builder.Property(f => f.Id)
            .ValueGeneratedOnAdd();

        builder.Property(f => f.Name)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(f => f.OwnerId)
            .IsRequired();

        builder.Property(f => f.Path)
            .IsRequired()
            .HasMaxLength(2000);

        builder.Property(f => f.Level)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(f => f.CreatedAt)
            .IsRequired();

        builder.Property(f => f.UpdatedAt)
            .IsRequired();

        builder.Property(f => f.DeletedAt);

        builder.Property(f => f.IsDeleted)
            .IsRequired()
            .HasDefaultValue(false);

        // Self-referencing relationship for folder hierarchy
        builder.HasOne(f => f.ParentFolder)
            .WithMany(f => f.SubFolders)
            .HasForeignKey(f => f.ParentFolderId)
            .OnDelete(DeleteBehavior.Restrict); // Prevent cascade delete to avoid cycles

        // Files relationship
        builder.HasMany(f => f.Files)
            .WithOne()
            .HasForeignKey(file => file.ParentFolderId)
            .OnDelete(DeleteBehavior.SetNull);

        // Indexes
        builder.HasIndex(f => f.OwnerId)
            .HasDatabaseName("idx_folders_owner_id");

        builder.HasIndex(f => f.ParentFolderId)
            .HasDatabaseName("idx_folders_parent");

        builder.HasIndex(f => f.Path)
            .HasDatabaseName("idx_folders_path");

        builder.HasIndex(f => new { f.ParentFolderId, f.Name })
            .HasDatabaseName("idx_folders_parent_name")
            .IsUnique();

        builder.HasIndex(f => f.Level)
            .HasDatabaseName("idx_folders_level");

        builder.HasIndex(f => f.CreatedAt)
            .HasDatabaseName("idx_folders_created_at");

        builder.HasIndex(f => f.IsDeleted)
            .HasDatabaseName("idx_folders_is_deleted");

        // Ignore domain events collection for EF Core
        builder.Ignore(f => f.DomainEvents);
    }
} 