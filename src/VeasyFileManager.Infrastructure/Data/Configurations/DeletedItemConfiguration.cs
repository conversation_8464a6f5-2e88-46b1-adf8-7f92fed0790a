using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Infrastructure.Data.Configurations;

/// <summary>
/// EF Core configuration for DeletedItem entity
/// </summary>
public class DeletedItemConfiguration : IEntityTypeConfiguration<DeletedItem>
{
    public void Configure(EntityTypeBuilder<DeletedItem> builder)
    {
        builder.ToTable("DeletedItems", "storage");

        builder.HasKey(d => d.Id);

        builder.Property(d => d.Id)
            .IsRequired();

        builder.Property(d => d.OriginalId)
            .IsRequired();

        builder.Property(d => d.ItemType)
            .HasConversion<int>()
            .IsRequired();

        builder.Property(d => d.OriginalName)
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(d => d.OriginalPath)
            .HasMaxLength(2048)
            .IsRequired();

        builder.Property(d => d.ParentFolderId)
            .IsRequired(false);

        builder.Property(d => d.DeletedBy)
            .IsRequired();

        builder.Property(d => d.DeletedAt)
            .IsRequired();

        builder.Property(d => d.RestoredAt)
            .IsRequired(false);

        builder.Property(d => d.RestoredBy)
            .IsRequired(false);

        builder.Property(d => d.OriginalSize)
            .IsRequired(false);

        builder.Property(d => d.OriginalContentType)
            .HasMaxLength(255)
            .IsRequired(false);

        builder.Property(d => d.Metadata)
            .HasColumnType("jsonb")
            .IsRequired(false);

        // Computed properties are ignored
        builder.Ignore(d => d.CanRestore);
        builder.Ignore(d => d.ShouldPermanentlyDelete);

        // Indexes
        builder.HasIndex(d => d.OriginalId)
            .HasDatabaseName("IX_DeletedItems_OriginalId");

        builder.HasIndex(d => d.ItemType)
            .HasDatabaseName("IX_DeletedItems_ItemType");

        builder.HasIndex(d => d.DeletedBy)
            .HasDatabaseName("IX_DeletedItems_DeletedBy");

        builder.HasIndex(d => d.DeletedAt)
            .HasDatabaseName("IX_DeletedItems_DeletedAt");

        builder.HasIndex(d => new { d.DeletedAt, d.RestoredAt })
            .HasDatabaseName("IX_DeletedItems_DeletedAt_RestoredAt");

        builder.HasIndex(d => new { d.ItemType, d.DeletedBy, d.DeletedAt })
            .HasDatabaseName("IX_DeletedItems_ItemType_DeletedBy_DeletedAt");
    }
}
