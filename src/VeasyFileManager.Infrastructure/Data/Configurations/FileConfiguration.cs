using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VeasyFileManager.Domain.Entities;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Infrastructure.Data.Configurations;

public class FileConfiguration : IEntityTypeConfiguration<Domain.Entities.File>
{
    public void Configure(EntityTypeBuilder<Domain.Entities.File> builder)
    {
        builder.ToTable("Files");

        builder.HasKey(f => f.Id);

        builder.Property(f => f.Id)
            .ValueGeneratedOnAdd();

        builder.Property(f => f.Name)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(f => f.DisplayName)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(f => f.FilePath)
            .HasMaxLength(1000);

        builder.Property(f => f.FileSize)
            .IsRequired();

        builder.Property(f => f.MimeType)
            .HasMaxLength(100);

        builder.Property(f => f.HashMd5)
            .HasMaxLength(32);

        builder.Property(f => f.HashSha256)
            .HasMaxLength(64);

        builder.Property(f => f.StorageProvider)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(50);

        builder.Property(f => f.ExternalId)
            .HasMaxLength(255);

        builder.Property(f => f.OwnerId)
            .IsRequired();

        builder.Property(f => f.CreatedAt)
            .IsRequired();

        builder.Property(f => f.UpdatedAt)
            .IsRequired();

        builder.Property(f => f.DeletedAt);

        builder.Property(f => f.IsDeleted)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(f => f.Version)
            .IsRequired()
            .HasDefaultValue(1);

        // Parent folder relationship - already configured in FolderConfiguration

        // Indexes for performance
        builder.HasIndex(f => f.OwnerId)
            .HasDatabaseName("idx_files_owner_id");

        builder.HasIndex(f => f.ParentFolderId)
            .HasDatabaseName("idx_files_parent_folder");

        builder.HasIndex(f => f.HashMd5)
            .HasDatabaseName("idx_files_hash_md5");

        builder.HasIndex(f => f.HashSha256)
            .HasDatabaseName("idx_files_hash_sha256");

        builder.HasIndex(f => f.StorageProvider)
            .HasDatabaseName("idx_files_storage_provider");

        builder.HasIndex(f => f.ExternalId)
            .HasDatabaseName("idx_files_external_id");

        builder.HasIndex(f => f.CreatedAt)
            .HasDatabaseName("idx_files_created_at");

        builder.HasIndex(f => f.IsDeleted)
            .HasDatabaseName("idx_files_is_deleted");

        builder.HasIndex(f => new { f.Name, f.ParentFolderId })
            .HasDatabaseName("idx_files_name_parent");
    }
} 