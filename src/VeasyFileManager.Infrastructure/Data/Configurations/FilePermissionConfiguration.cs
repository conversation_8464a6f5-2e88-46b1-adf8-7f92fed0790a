using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Infrastructure.Data.Configurations;

public class FilePermissionConfiguration : IEntityTypeConfiguration<FilePermission>
{
    public void Configure(EntityTypeBuilder<FilePermission> builder)
    {
        builder.ToTable("FilePermissions");

        builder.HasKey(fp => fp.Id);

        builder.Property(fp => fp.Id)
            .ValueGeneratedOnAdd();

        builder.Property(fp => fp.FileId)
            .IsRequired();

        builder.Property(fp => fp.PermissionType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(fp => fp.GrantedBy)
            .IsRequired();

        builder.Property(fp => fp.GrantedAt)
            .IsRequired();

        builder.Property(fp => fp.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(fp => new { fp.FileId, fp.UserId })
            .HasDatabaseName("idx_file_permissions_file_user");

        builder.HasIndex(fp => new { fp.FileId, fp.RoleId })
            .HasDatabaseName("idx_file_permissions_file_role");

        builder.HasIndex(fp => fp.GrantedBy)
            .HasDatabaseName("idx_file_permissions_granted_by");

        builder.HasIndex(fp => fp.ExpiresAt)
            .HasDatabaseName("idx_file_permissions_expires_at");

        builder.HasIndex(fp => fp.IsActive)
            .HasDatabaseName("idx_file_permissions_is_active");
    }
} 