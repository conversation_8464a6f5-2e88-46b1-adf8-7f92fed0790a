using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Infrastructure.Data.Configurations;

public class FolderShareConfiguration : IEntityTypeConfiguration<FolderShare>
{
    public void Configure(EntityTypeBuilder<FolderShare> builder)
    {
        builder.ToTable("FolderShares", "storage");

        builder.HasKey(fs => fs.Id);

        builder.Property(fs => fs.Id)
            .ValueGeneratedOnAdd();

        builder.Property(fs => fs.FolderId)
            .IsRequired();

        builder.Property(fs => fs.ShareToken)
            .IsRequired()
            .HasMaxLength(64);

        builder.Property(fs => fs.SharedBy)
            .IsRequired();

        builder.Property(fs => fs.ShareType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(fs => fs.PasswordHash)
            .HasMaxLength(256);

        builder.Property(fs => fs.ExpiresAt);

        builder.Property(fs => fs.MaxDownloads);

        builder.Property(fs => fs.DownloadCount)
            .IsRequired()
            .HasDefaultValue(0);

        builder.Property(fs => fs.CreatedAt)
            .IsRequired();

        builder.Property(fs => fs.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(fs => fs.IncludeSubfolders)
            .IsRequired()
            .HasDefaultValue(true);

        // Indexes
        builder.HasIndex(fs => fs.ShareToken)
            .IsUnique()
            .HasDatabaseName("idx_folder_shares_token");

        builder.HasIndex(fs => fs.FolderId)
            .HasDatabaseName("idx_folder_shares_folder");

        builder.HasIndex(fs => fs.SharedBy)
            .HasDatabaseName("idx_folder_shares_shared_by");

        builder.HasIndex(fs => fs.ExpiresAt)
            .HasDatabaseName("idx_folder_shares_expires_at");

        builder.HasIndex(fs => fs.IsActive)
            .HasDatabaseName("idx_folder_shares_is_active");

        // Relationships
        builder.HasOne(fs => fs.Folder)
            .WithMany()
            .HasForeignKey(fs => fs.FolderId)
            .OnDelete(DeleteBehavior.Cascade);
    }
} 