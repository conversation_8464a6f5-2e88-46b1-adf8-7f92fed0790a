using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using VeasyFileManager.Domain.Entities;

namespace VeasyFileManager.Infrastructure.Data.Configurations;

public class FolderPermissionConfiguration : IEntityTypeConfiguration<FolderPermission>
{
    public void Configure(EntityTypeBuilder<FolderPermission> builder)
    {
        builder.ToTable("FolderPermissions");

        builder.<PERSON><PERSON>ey(fp => fp.Id);

        builder.Property(fp => fp.Id)
            .ValueGeneratedOnAdd();

        builder.Property(fp => fp.FolderId)
            .IsRequired();

        builder.Property(fp => fp.PermissionType)
            .IsRequired()
            .HasConversion<string>()
            .HasMaxLength(20);

        builder.Property(fp => fp.GrantedBy)
            .IsRequired();

        builder.Property(fp => fp.GrantedAt)
            .IsRequired();

        builder.Property(fp => fp.IsActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(fp => fp.InheritToChildren)
            .IsRequired()
            .HasDefaultValue(true);

        // No explicit relationship configuration needed as FolderId is a simple foreign key

        // Indexes for performance
        builder.HasIndex(fp => new { fp.FolderId, fp.UserId })
            .HasDatabaseName("idx_folder_permissions_folder_user");

        builder.HasIndex(fp => new { fp.FolderId, fp.RoleId })
            .HasDatabaseName("idx_folder_permissions_folder_role");

        builder.HasIndex(fp => fp.GrantedBy)
            .HasDatabaseName("idx_folder_permissions_granted_by");

        builder.HasIndex(fp => fp.ExpiresAt)
            .HasDatabaseName("idx_folder_permissions_expires_at");

        builder.HasIndex(fp => fp.IsActive)
            .HasDatabaseName("idx_folder_permissions_is_active");

        builder.HasIndex(fp => fp.InheritToChildren)
            .HasDatabaseName("idx_folder_permissions_inherit");
    }
} 