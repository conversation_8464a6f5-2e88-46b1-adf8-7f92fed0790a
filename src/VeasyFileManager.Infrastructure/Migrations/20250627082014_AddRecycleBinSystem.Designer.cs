// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using VeasyFileManager.Infrastructure.Data;

#nullable disable

namespace VeasyFileManager.Infrastructure.Migrations
{
    [DbContext(typeof(StorageDbContext))]
    [Migration("20250627082014_AddRecycleBinSystem")]
    partial class AddRecycleBinSystem
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.DeletedItem", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("DeletedBy")
                        .HasColumnType("uuid");

                    b.Property<int>("ItemType")
                        .HasColumnType("integer");

                    b.Property<string>("Metadata")
                        .HasColumnType("jsonb");

                    b.Property<string>("OriginalContentType")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("OriginalId")
                        .HasColumnType("uuid");

                    b.Property<string>("OriginalName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("OriginalPath")
                        .IsRequired()
                        .HasMaxLength(2048)
                        .HasColumnType("character varying(2048)");

                    b.Property<long?>("OriginalSize")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("ParentFolderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("RestoredAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("RestoredBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DeletedAt")
                        .HasDatabaseName("IX_DeletedItems_DeletedAt");

                    b.HasIndex("DeletedBy")
                        .HasDatabaseName("IX_DeletedItems_DeletedBy");

                    b.HasIndex("ItemType")
                        .HasDatabaseName("IX_DeletedItems_ItemType");

                    b.HasIndex("OriginalId")
                        .HasDatabaseName("IX_DeletedItems_OriginalId");

                    b.HasIndex("DeletedAt", "RestoredAt")
                        .HasDatabaseName("IX_DeletedItems_DeletedAt_RestoredAt");

                    b.HasIndex("ItemType", "DeletedBy", "DeletedAt")
                        .HasDatabaseName("IX_DeletedItems_ItemType_DeletedBy_DeletedAt");

                    b.ToTable("DeletedItems", "storage");
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.File", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ExternalId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FilePath")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<string>("HashMd5")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<string>("HashSha256")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<string>("MimeType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ParentFolderId")
                        .HasColumnType("uuid");

                    b.Property<string>("StorageProvider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Version")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(1);

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("idx_files_created_at");

                    b.HasIndex("ExternalId")
                        .HasDatabaseName("idx_files_external_id");

                    b.HasIndex("HashMd5")
                        .HasDatabaseName("idx_files_hash_md5");

                    b.HasIndex("HashSha256")
                        .HasDatabaseName("idx_files_hash_sha256");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("idx_files_is_deleted");

                    b.HasIndex("OwnerId")
                        .HasDatabaseName("idx_files_owner_id");

                    b.HasIndex("ParentFolderId")
                        .HasDatabaseName("idx_files_parent_folder");

                    b.HasIndex("StorageProvider")
                        .HasDatabaseName("idx_files_storage_provider");

                    b.HasIndex("Name", "ParentFolderId")
                        .HasDatabaseName("idx_files_name_parent");

                    b.ToTable("Files", (string)null);
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.FilePermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FileId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("GrantedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("GrantedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("PermissionType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("idx_file_permissions_expires_at");

                    b.HasIndex("GrantedBy")
                        .HasDatabaseName("idx_file_permissions_granted_by");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("idx_file_permissions_is_active");

                    b.HasIndex("FileId", "RoleId")
                        .HasDatabaseName("idx_file_permissions_file_role");

                    b.HasIndex("FileId", "UserId")
                        .HasDatabaseName("idx_file_permissions_file_user");

                    b.ToTable("FilePermissions", (string)null);
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.FileShare", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DownloadCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FileId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<int?>("MaxDownloads")
                        .HasColumnType("integer");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ShareToken")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ShareType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid>("SharedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("idx_file_shares_created_at");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("idx_file_shares_expires_at");

                    b.HasIndex("FileId")
                        .HasDatabaseName("idx_file_shares_file_id");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("idx_file_shares_is_active");

                    b.HasIndex("ShareToken")
                        .IsUnique()
                        .HasDatabaseName("idx_file_shares_token");

                    b.HasIndex("SharedBy")
                        .HasDatabaseName("idx_file_shares_shared_by");

                    b.ToTable("FileShares", (string)null);
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.Folder", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(false);

                    b.Property<int>("Level")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ParentFolderId")
                        .HasColumnType("uuid");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("idx_folders_created_at");

                    b.HasIndex("IsDeleted")
                        .HasDatabaseName("idx_folders_is_deleted");

                    b.HasIndex("Level")
                        .HasDatabaseName("idx_folders_level");

                    b.HasIndex("OwnerId")
                        .HasDatabaseName("idx_folders_owner_id");

                    b.HasIndex("ParentFolderId")
                        .HasDatabaseName("idx_folders_parent");

                    b.HasIndex("Path")
                        .HasDatabaseName("idx_folders_path");

                    b.HasIndex("ParentFolderId", "Name")
                        .IsUnique()
                        .HasDatabaseName("idx_folders_parent_name");

                    b.ToTable("Folders", (string)null);
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.FolderPermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FolderId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("GrantedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("GrantedBy")
                        .HasColumnType("uuid");

                    b.Property<bool>("InheritToChildren")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<string>("PermissionType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("idx_folder_permissions_expires_at");

                    b.HasIndex("GrantedBy")
                        .HasDatabaseName("idx_folder_permissions_granted_by");

                    b.HasIndex("InheritToChildren")
                        .HasDatabaseName("idx_folder_permissions_inherit");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("idx_folder_permissions_is_active");

                    b.HasIndex("FolderId", "RoleId")
                        .HasDatabaseName("idx_folder_permissions_folder_role");

                    b.HasIndex("FolderId", "UserId")
                        .HasDatabaseName("idx_folder_permissions_folder_user");

                    b.ToTable("FolderPermissions", (string)null);
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.FolderShare", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("DownloadCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<DateTime?>("ExpiresAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("FolderId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("FolderId1")
                        .HasColumnType("uuid");

                    b.Property<bool>("IncludeSubfolders")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<bool>("IsActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("boolean")
                        .HasDefaultValue(true);

                    b.Property<int?>("MaxDownloads")
                        .HasColumnType("integer");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("ShareToken")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("character varying(64)");

                    b.Property<string>("ShareType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<Guid>("SharedBy")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ExpiresAt")
                        .HasDatabaseName("idx_folder_shares_expires_at");

                    b.HasIndex("FolderId")
                        .HasDatabaseName("idx_folder_shares_folder");

                    b.HasIndex("FolderId1");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("idx_folder_shares_is_active");

                    b.HasIndex("ShareToken")
                        .IsUnique()
                        .HasDatabaseName("idx_folder_shares_token");

                    b.HasIndex("SharedBy")
                        .HasDatabaseName("idx_folder_shares_shared_by");

                    b.ToTable("FolderShares", "storage");
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.SyncStatus", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ExternalId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("FileId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastSyncAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Provider")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("RetryCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)")
                        .HasDefaultValue("Pending");

                    b.HasKey("Id");

                    b.HasIndex("ExternalId")
                        .HasDatabaseName("idx_sync_status_external_id");

                    b.HasIndex("LastSyncAt")
                        .HasDatabaseName("idx_sync_status_last_sync");

                    b.HasIndex("RetryCount")
                        .HasDatabaseName("idx_sync_status_retry_count");

                    b.HasIndex("Status")
                        .HasDatabaseName("idx_sync_status_status");

                    b.HasIndex("FileId", "Provider")
                        .HasDatabaseName("idx_sync_status_file_provider");

                    b.ToTable("SyncStatus", (string)null);
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.File", b =>
                {
                    b.HasOne("VeasyFileManager.Domain.Entities.Folder", null)
                        .WithMany("Files")
                        .HasForeignKey("ParentFolderId")
                        .OnDelete(DeleteBehavior.SetNull);
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.FilePermission", b =>
                {
                    b.HasOne("VeasyFileManager.Domain.Entities.File", null)
                        .WithMany("Permissions")
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.FileShare", b =>
                {
                    b.HasOne("VeasyFileManager.Domain.Entities.File", null)
                        .WithMany("Shares")
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.Folder", b =>
                {
                    b.HasOne("VeasyFileManager.Domain.Entities.Folder", "ParentFolder")
                        .WithMany("SubFolders")
                        .HasForeignKey("ParentFolderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("ParentFolder");
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.FolderPermission", b =>
                {
                    b.HasOne("VeasyFileManager.Domain.Entities.Folder", null)
                        .WithMany("Permissions")
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.FolderShare", b =>
                {
                    b.HasOne("VeasyFileManager.Domain.Entities.Folder", "Folder")
                        .WithMany()
                        .HasForeignKey("FolderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("VeasyFileManager.Domain.Entities.Folder", null)
                        .WithMany("Shares")
                        .HasForeignKey("FolderId1");

                    b.Navigation("Folder");
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.SyncStatus", b =>
                {
                    b.HasOne("VeasyFileManager.Domain.Entities.File", "File")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("File");
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.File", b =>
                {
                    b.Navigation("Permissions");

                    b.Navigation("Shares");
                });

            modelBuilder.Entity("VeasyFileManager.Domain.Entities.Folder", b =>
                {
                    b.Navigation("Files");

                    b.Navigation("Permissions");

                    b.Navigation("Shares");

                    b.Navigation("SubFolders");
                });
#pragma warning restore 612, 618
        }
    }
}
