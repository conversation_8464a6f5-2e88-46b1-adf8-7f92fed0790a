using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace VeasyFileManager.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AddDeletedItemsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "storage");

            migrationBuilder.CreateTable(
                name: "DeletedItems",
                schema: "storage",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    OriginalId = table.Column<Guid>(type: "uuid", nullable: false),
                    ItemType = table.Column<int>(type: "integer", nullable: false),
                    OriginalName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    OriginalPath = table.Column<string>(type: "character varying(2048)", maxLength: 2048, nullable: false),
                    ParentFolderId = table.Column<Guid>(type: "uuid", nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RestoredAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RestoredBy = table.Column<Guid>(type: "uuid", nullable: true),
                    OriginalSize = table.Column<long>(type: "bigint", nullable: true),
                    OriginalContentType = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Metadata = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DeletedItems", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "FolderShares",
                schema: "storage",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FolderId = table.Column<Guid>(type: "uuid", nullable: false),
                    ShareToken = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    SharedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    ShareType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    PasswordHash = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    MaxDownloads = table.Column<int>(type: "integer", nullable: true),
                    DownloadCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    IncludeSubfolders = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    FolderId1 = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FolderShares", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FolderShares_Folders_FolderId",
                        column: x => x.FolderId,
                        principalTable: "Folders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FolderShares_Folders_FolderId1",
                        column: x => x.FolderId1,
                        principalTable: "Folders",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_DeletedItems_DeletedAt",
                schema: "storage",
                table: "DeletedItems",
                column: "DeletedAt");

            migrationBuilder.CreateIndex(
                name: "IX_DeletedItems_DeletedAt_RestoredAt",
                schema: "storage",
                table: "DeletedItems",
                columns: new[] { "DeletedAt", "RestoredAt" });

            migrationBuilder.CreateIndex(
                name: "IX_DeletedItems_DeletedBy",
                schema: "storage",
                table: "DeletedItems",
                column: "DeletedBy");

            migrationBuilder.CreateIndex(
                name: "IX_DeletedItems_ItemType",
                schema: "storage",
                table: "DeletedItems",
                column: "ItemType");

            migrationBuilder.CreateIndex(
                name: "IX_DeletedItems_ItemType_DeletedBy_DeletedAt",
                schema: "storage",
                table: "DeletedItems",
                columns: new[] { "ItemType", "DeletedBy", "DeletedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_DeletedItems_OriginalId",
                schema: "storage",
                table: "DeletedItems",
                column: "OriginalId");

            migrationBuilder.CreateIndex(
                name: "idx_folder_shares_expires_at",
                schema: "storage",
                table: "FolderShares",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "idx_folder_shares_folder",
                schema: "storage",
                table: "FolderShares",
                column: "FolderId");

            migrationBuilder.CreateIndex(
                name: "idx_folder_shares_is_active",
                schema: "storage",
                table: "FolderShares",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "idx_folder_shares_shared_by",
                schema: "storage",
                table: "FolderShares",
                column: "SharedBy");

            migrationBuilder.CreateIndex(
                name: "idx_folder_shares_token",
                schema: "storage",
                table: "FolderShares",
                column: "ShareToken",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_FolderShares_FolderId1",
                schema: "storage",
                table: "FolderShares",
                column: "FolderId1");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DeletedItems",
                schema: "storage");

            migrationBuilder.DropTable(
                name: "FolderShares",
                schema: "storage");
        }
    }
}
