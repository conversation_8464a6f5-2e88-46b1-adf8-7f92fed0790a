using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace VeasyFileManager.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Folders",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ParentFolderId = table.Column<Guid>(type: "uuid", nullable: true),
                    OwnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    Path = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: false),
                    Level = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Folders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Folders_Folders_ParentFolderId",
                        column: x => x.ParentFolderId,
                        principalTable: "Folders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Files",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    DisplayName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    FilePath = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    FileSize = table.Column<long>(type: "bigint", nullable: false),
                    MimeType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    HashMd5 = table.Column<string>(type: "character varying(32)", maxLength: 32, nullable: false),
                    HashSha256 = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    StorageProvider = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ExternalId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ParentFolderId = table.Column<Guid>(type: "uuid", nullable: true),
                    OwnerId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false, defaultValue: false),
                    Version = table.Column<int>(type: "integer", nullable: false, defaultValue: 1)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Files", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Files_Folders_ParentFolderId",
                        column: x => x.ParentFolderId,
                        principalTable: "Folders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "FolderPermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FolderId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: true),
                    PermissionType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    GrantedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    GrantedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true),
                    InheritToChildren = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FolderPermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FolderPermissions_Folders_FolderId",
                        column: x => x.FolderId,
                        principalTable: "Folders",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FilePermissions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FileId = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: true),
                    PermissionType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    GrantedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    GrantedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FilePermissions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FilePermissions_Files_FileId",
                        column: x => x.FileId,
                        principalTable: "Files",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FileShares",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FileId = table.Column<Guid>(type: "uuid", nullable: false),
                    ShareToken = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    SharedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    ShareType = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    PasswordHash = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ExpiresAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    MaxDownloads = table.Column<int>(type: "integer", nullable: true),
                    DownloadCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FileShares", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FileShares_Files_FileId",
                        column: x => x.FileId,
                        principalTable: "Files",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SyncStatus",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FileId = table.Column<Guid>(type: "uuid", nullable: false),
                    Provider = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ExternalId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    LastSyncAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false, defaultValue: "Pending"),
                    ErrorMessage = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RetryCount = table.Column<int>(type: "integer", nullable: false, defaultValue: 0)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncStatus", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SyncStatus_Files_FileId",
                        column: x => x.FileId,
                        principalTable: "Files",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "idx_file_permissions_expires_at",
                table: "FilePermissions",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "idx_file_permissions_file_role",
                table: "FilePermissions",
                columns: new[] { "FileId", "RoleId" });

            migrationBuilder.CreateIndex(
                name: "idx_file_permissions_file_user",
                table: "FilePermissions",
                columns: new[] { "FileId", "UserId" });

            migrationBuilder.CreateIndex(
                name: "idx_file_permissions_granted_by",
                table: "FilePermissions",
                column: "GrantedBy");

            migrationBuilder.CreateIndex(
                name: "idx_file_permissions_is_active",
                table: "FilePermissions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "idx_files_created_at",
                table: "Files",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "idx_files_external_id",
                table: "Files",
                column: "ExternalId");

            migrationBuilder.CreateIndex(
                name: "idx_files_hash_md5",
                table: "Files",
                column: "HashMd5");

            migrationBuilder.CreateIndex(
                name: "idx_files_hash_sha256",
                table: "Files",
                column: "HashSha256");

            migrationBuilder.CreateIndex(
                name: "idx_files_is_deleted",
                table: "Files",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "idx_files_name_parent",
                table: "Files",
                columns: new[] { "Name", "ParentFolderId" });

            migrationBuilder.CreateIndex(
                name: "idx_files_owner_id",
                table: "Files",
                column: "OwnerId");

            migrationBuilder.CreateIndex(
                name: "idx_files_parent_folder",
                table: "Files",
                column: "ParentFolderId");

            migrationBuilder.CreateIndex(
                name: "idx_files_storage_provider",
                table: "Files",
                column: "StorageProvider");

            migrationBuilder.CreateIndex(
                name: "idx_file_shares_created_at",
                table: "FileShares",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "idx_file_shares_expires_at",
                table: "FileShares",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "idx_file_shares_file_id",
                table: "FileShares",
                column: "FileId");

            migrationBuilder.CreateIndex(
                name: "idx_file_shares_is_active",
                table: "FileShares",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "idx_file_shares_shared_by",
                table: "FileShares",
                column: "SharedBy");

            migrationBuilder.CreateIndex(
                name: "idx_file_shares_token",
                table: "FileShares",
                column: "ShareToken",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_folder_permissions_expires_at",
                table: "FolderPermissions",
                column: "ExpiresAt");

            migrationBuilder.CreateIndex(
                name: "idx_folder_permissions_folder_role",
                table: "FolderPermissions",
                columns: new[] { "FolderId", "RoleId" });

            migrationBuilder.CreateIndex(
                name: "idx_folder_permissions_folder_user",
                table: "FolderPermissions",
                columns: new[] { "FolderId", "UserId" });

            migrationBuilder.CreateIndex(
                name: "idx_folder_permissions_granted_by",
                table: "FolderPermissions",
                column: "GrantedBy");

            migrationBuilder.CreateIndex(
                name: "idx_folder_permissions_inherit",
                table: "FolderPermissions",
                column: "InheritToChildren");

            migrationBuilder.CreateIndex(
                name: "idx_folder_permissions_is_active",
                table: "FolderPermissions",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "idx_folders_created_at",
                table: "Folders",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "idx_folders_is_deleted",
                table: "Folders",
                column: "IsDeleted");

            migrationBuilder.CreateIndex(
                name: "idx_folders_level",
                table: "Folders",
                column: "Level");

            migrationBuilder.CreateIndex(
                name: "idx_folders_owner_id",
                table: "Folders",
                column: "OwnerId");

            migrationBuilder.CreateIndex(
                name: "idx_folders_parent",
                table: "Folders",
                column: "ParentFolderId");

            migrationBuilder.CreateIndex(
                name: "idx_folders_parent_name",
                table: "Folders",
                columns: new[] { "ParentFolderId", "Name" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "idx_folders_path",
                table: "Folders",
                column: "Path");

            migrationBuilder.CreateIndex(
                name: "idx_sync_status_external_id",
                table: "SyncStatus",
                column: "ExternalId");

            migrationBuilder.CreateIndex(
                name: "idx_sync_status_file_provider",
                table: "SyncStatus",
                columns: new[] { "FileId", "Provider" });

            migrationBuilder.CreateIndex(
                name: "idx_sync_status_last_sync",
                table: "SyncStatus",
                column: "LastSyncAt");

            migrationBuilder.CreateIndex(
                name: "idx_sync_status_retry_count",
                table: "SyncStatus",
                column: "RetryCount");

            migrationBuilder.CreateIndex(
                name: "idx_sync_status_status",
                table: "SyncStatus",
                column: "Status");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FilePermissions");

            migrationBuilder.DropTable(
                name: "FileShares");

            migrationBuilder.DropTable(
                name: "FolderPermissions");

            migrationBuilder.DropTable(
                name: "SyncStatus");

            migrationBuilder.DropTable(
                name: "Files");

            migrationBuilder.DropTable(
                name: "Folders");
        }
    }
}
