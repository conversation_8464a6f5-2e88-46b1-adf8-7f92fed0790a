{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Elasticsearch": {"Username": "", "Password": "", "IndexFormat": "veasy-filemanager-{0:yyyy.MM.dd}"}, "CloudflareR2": {"AccessKeyId": "4c0bef52101fc79e7e78af2629d83672", "SecretAccessKey": "****************************************************************", "BucketName": "default", "Endpoint": "https://c9eea23a10d0ea2551ec0f664c7811ed.r2.cloudflarestorage.com", "Region": "auto", "BasePath": "files", "MaxFileSize": *********, "MultipartThreshold": 10485760, "DefaultPresignedUrlExpiry": "01:00:00"}, "ConnectionStrings": {"StorageConnection": "Host=*************;Port=4523;Database=Storage;Username=veasypostgresadmin;Password=***********************;Timeout=30;"}, "JwtOptions": {"Authority": "https://sso.veasy.vn", "Audience": "file_service", "Issuer": "https://sso.veasy.vn", "RequireHttpsMetadata": true, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ClockSkew": "00:05:00"}, "GoogleDrive": {"ClientId": "************-206u4vgif1bejb2n9hpdcf32n80qen56.apps.googleusercontent.com", "ClientSecret": "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", "RedirectUri": "https://localhost:7040/auth/google/callback", "ServiceAccountKeyPath": "veasy-462014-0f8be66a4a9b.json", "ApplicationName": "veasy-file-manager-service", "RequestsPerSecond": 10, "MaxRetryAttempts": 3, "Scopes": ["https://www.googleapis.com/auth/drive", "https://www.googleapis.com/auth/drive.file"]}, "FileUpload": {"MaxFileSize": *********, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".docx", ".xlsx", ".txt"], "TempPath": "/tmp/uploads"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {CorrelationId} {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/veasy-filemanager-.log", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {CorrelationId} {UserId} {RequestId} {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithProcessId", "WithThreadId"], "Properties": {"Application": "VeasyFileManager", "Environment": "Development", "Version": "1.0.0"}}, "RateLimit": {"Enabled": false, "GlobalRpm": 10000, "PerUserRpm": 1000, "UploadRpm": 100, "DownloadRpm": 500, "ShareRpm": 50, "WindowMinutes": 1, "WhitelistedIps": ["127.0.0.1", "::1", "localhost"]}, "AllowedHosts": "*"}