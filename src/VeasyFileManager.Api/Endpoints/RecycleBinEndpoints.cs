using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.OpenApi.Models;
using System.Net;
using VeasyFileManager.Application.Commands.RecycleBin;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Application.Queries.RecycleBin;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Api.Endpoints;

/// <summary>
/// Recycle bin (thùng rác) endpoints for managing deleted items
/// </summary>
public static class RecycleBinEndpoints
{
    /// <summary>
    /// Map recycle bin endpoints to the application
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapRecycleBinEndpoints(this WebApplication app)
    {
        var recycleBinGroup = app.MapGroup("/api/v1/recycle-bin")
            .WithTags("Recycle Bin")
            .WithOpenApi()
            .RequireAuthorization();

        // List deleted items
        recycleBinGroup.MapGet("/", GetDeletedItems)
            .WithName("GetDeletedItems")
            .WithSummary("Lấy danh sách thùng rác")
            .WithDescription("Lấy danh sách phân trang các file/folder đã xóa có thể khôi phục trong 30 ngày")
            .WithOpenApi(operation => new(operation)
            {
                Summary = "Danh sách thùng rác",
                Description = "Lấy danh sách phân trang các file và folder đã xóa có thể khôi phục trong vòng 30 ngày",
                Parameters = new List<OpenApiParameter>
                {
                    new() { Name = "page", In = ParameterLocation.Query, Description = "Số trang (bắt đầu từ 1)", Schema = new OpenApiSchema { Type = "integer", Default = new Microsoft.OpenApi.Any.OpenApiInteger(1) } },
                    new() { Name = "pageSize", In = ParameterLocation.Query, Description = "Số item mỗi trang", Schema = new OpenApiSchema { Type = "integer", Default = new Microsoft.OpenApi.Any.OpenApiInteger(20) } },
                    new() { Name = "itemType", In = ParameterLocation.Query, Description = "Lọc theo loại (Folder=1, File=2)", Schema = new OpenApiSchema { Type = "integer" } },
                    new() { Name = "searchTerm", In = ParameterLocation.Query, Description = "Tìm kiếm theo tên hoặc đường dẫn", Schema = new OpenApiSchema { Type = "string" } },
                    new() { Name = "uploaderEmail", In = ParameterLocation.Query, Description = "Lọc theo email người tạo", Schema = new OpenApiSchema { Type = "string" } },
                    new() { Name = "deletedAfter", In = ParameterLocation.Query, Description = "Lọc từ ngày xóa (YYYY-MM-DD)", Schema = new OpenApiSchema { Type = "string", Format = "date" } },
                    new() { Name = "deletedBefore", In = ParameterLocation.Query, Description = "Lọc đến ngày xóa (YYYY-MM-DD)", Schema = new OpenApiSchema { Type = "string", Format = "date" } },
                    new() { Name = "onlyRestorable", In = ParameterLocation.Query, Description = "Chỉ hiển thị item có thể khôi phục", Schema = new OpenApiSchema { Type = "boolean", Default = new Microsoft.OpenApi.Any.OpenApiBoolean(true) } }
                }
            })
            .Produces<object>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized);

        // Restore item
        recycleBinGroup.MapPost("/{deletedItemId:guid}/restore", RestoreItem)
            .WithName("RestoreItem")
            .WithSummary("Khôi phục item đã xóa")
            .WithDescription("Khôi phục file hoặc folder từ thùng rác")
            .WithOpenApi(operation => new(operation)
            {
                Summary = "Khôi phục item đã xóa",
                Description = "Khôi phục file hoặc folder từ thùng rác. Item phải được xóa trong vòng 30 ngày.",
                RequestBody = new OpenApiRequestBody
                {
                    Description = "Tùy chọn khôi phục",
                    Required = false,
                    Content = new Dictionary<string, OpenApiMediaType>
                    {
                        ["application/json"] = new()
                        {
                            Schema = new OpenApiSchema
                            {
                                Type = "object",
                                Properties = new Dictionary<string, OpenApiSchema>
                                {
                                    ["newParentFolderId"] = new() { Type = "string", Format = "uuid", Description = "ID folder đích (nếu khôi phục về vị trí khác)" }
                                },
                                Required = new HashSet<string>()
                            }
                        }
                    }
                }
            })
            .Produces<object>(StatusCodes.Status200OK)
            .ProducesValidationProblem()
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Permanent delete item
        recycleBinGroup.MapDelete("/{deletedItemId:guid}/permanent", PermanentDeleteItem)
            .WithName("PermanentDeleteItem")
            .WithSummary("Xóa vĩnh viễn item")
            .WithDescription("Xóa vĩnh viễn file hoặc folder khỏi thùng rác và database")
            .WithOpenApi(operation => new(operation)
            {
                Summary = "Xóa vĩnh viễn item",
                Description = "Xóa vĩnh viễn file hoặc folder khỏi thùng rác và database. Thao tác này không thể hoàn tác. File trên R2 storage sẽ được bảo toàn."
            })
            .Produces<object>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Get recycle bin statistics
        recycleBinGroup.MapGet("/statistics", GetRecycleBinStatistics)
            .WithName("GetRecycleBinStatistics")
            .WithSummary("Get recycle bin statistics")
            .WithDescription("Get statistics about deleted items including counts and sizes")
            .WithOpenApi()
            .Produces<object>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized);

        // Empty recycle bin (permanently delete all expired items)
        recycleBinGroup.MapDelete("/empty", EmptyRecycleBin)
            .WithName("EmptyRecycleBin")
            .WithSummary("Empty recycle bin")
            .WithDescription("Permanently delete all expired items (older than 30 days) from the recycle bin")
            .WithOpenApi()
            .Produces<object>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized);

        return app;
    }

    /// <summary>
    /// Get deleted items
    /// </summary>
    private static async Task<IResult> GetDeletedItems(
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] DeletedItemType? itemType = null,
        [FromQuery] string? searchTerm = null,
        [FromQuery] string? uploaderEmail = null,
        [FromQuery] DateTime? deletedAfter = null,
        [FromQuery] DateTime? deletedBefore = null,
        [FromQuery] bool onlyRestorable = true)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var query = new GetDeletedItemsQuery
            {
                UserId = userId.Value,
                Page = page,
                PageSize = pageSize,
                ItemType = itemType,
                SearchTerm = searchTerm,
                UploaderEmail = uploaderEmail,
                DeletedAfter = deletedAfter,
                DeletedBefore = deletedBefore,
                OnlyRestorable = onlyRestorable
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting deleted items",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Restore item
    /// </summary>
    private static async Task<IResult> RestoreItem(
        Guid deletedItemId,
        [FromBody] RestoreItemRequest? request,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var command = new RestoreItemCommand
            {
                DeletedItemId = deletedItemId,
                UserId = userId.Value,
                NewParentFolderId = request?.NewParentFolderId
            };

            var result = await mediator.Send(command, cancellationToken);

            if (result.Success)
            {
                return Results.Ok(result);
            }

            return result.ErrorCode switch
            {
                "NOT_FOUND" => Results.NotFound(new { error = result.Message }),
                "FORBIDDEN" => Results.Forbid(),
                "CANNOT_RESTORE" or "RESTORE_FAILED" => Results.BadRequest(new { error = result.Message }),
                _ => Results.Problem(result.Message)
            };
        }
        catch (Exception ex)
        {
            return Results.Problem($"An error occurred while restoring the item: {ex.Message}");
        }
    }

    /// <summary>
    /// Permanently delete item
    /// </summary>
    private static async Task<IResult> PermanentDeleteItem(
        Guid deletedItemId,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var command = new PermanentDeleteItemCommand
            {
                DeletedItemId = deletedItemId,
                UserId = userId.Value
            };

            var result = await mediator.Send(command, cancellationToken);

            if (result.Success)
            {
                return Results.Ok(result);
            }

            return result.ErrorCode switch
            {
                "NOT_FOUND" => Results.NotFound(new { error = result.Message }),
                "FORBIDDEN" => Results.Forbid(),
                "DELETE_FAILED" => Results.BadRequest(new { error = result.Message }),
                _ => Results.Problem(result.Message)
            };
        }
        catch (Exception ex)
        {
            return Results.Problem($"An error occurred while permanently deleting the item: {ex.Message}");
        }
    }

    /// <summary>
    /// Get recycle bin statistics
    /// </summary>
    private static async Task<IResult> GetRecycleBinStatistics(
        [FromServices] IDeletedItemRepository deletedItemRepository,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var statistics = await deletedItemRepository.GetRestorationStatisticsAsync(userId.Value, cancellationToken);
            return Results.Ok(statistics);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting recycle bin statistics",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Empty recycle bin (delete expired items)
    /// </summary>
    private static async Task<IResult> EmptyRecycleBin(
        [FromServices] IDeletedItemRepository deletedItemRepository,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var deletedCount = await deletedItemRepository.DeleteExpiredItemsAsync(cancellationToken);

            return Results.Ok(new
            {
                success = true,
                message = $"Successfully deleted {deletedCount} expired items from recycle bin",
                deletedCount
            });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while emptying recycle bin",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }
}

/// <summary>
/// Request model for restoring items
/// </summary>
public record RestoreItemRequest(
    Guid? NewParentFolderId);
