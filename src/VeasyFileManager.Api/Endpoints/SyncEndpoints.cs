using MediatR;
using Microsoft.AspNetCore.Mvc;
using VeasyFileManager.Application.Commands.GoogleDrive;
using VeasyFileManager.Application.Commands.Sync;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Application.Queries.GoogleDrive;
using VeasyFileManager.Application.Queries.Sync;
using VeasyFileManager.Api.Extensions;

namespace VeasyFileManager.Api.Endpoints;

/// <summary>
/// Sync and Google Drive integration endpoints
/// </summary>
public static class SyncEndpoints
{
    /// <summary>
    /// Map sync endpoints to the application
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapSyncEndpoints(this WebApplication app)
    {
        var syncGroup = app.MapGroup("/api/v1/sync")
            .WithTags("Sync")
            .RequireAuthorization();

        // Trigger Google Drive sync
        syncGroup.MapPost("/google-drive", TriggerGoogleDriveSync)
            .WithName("TriggerGoogleDriveSync")
            .WithSummary("Trigger Google Drive synchronization")
            .WithDescription("Manually trigger synchronization with Google Drive")
            .Produces<object>(StatusCodes.Status202Accepted)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status500InternalServerError);

        // Get sync status
        syncGroup.MapGet("/status", GetSyncStatus)
            .WithName("GetSyncStatus")
            .WithSummary("Get synchronization status")
            .WithDescription("Get current synchronization status for user files")
            .Produces<object>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized);

        var googleDriveGroup = app.MapGroup("/api/v1/google-drive")
            .WithTags("Google Drive")
            .RequireAuthorization();

        // Get files from Google Drive
        googleDriveGroup.MapGet("/get-files", GetGoogleDriveFiles)
            .WithName("GetGoogleDriveFiles")
            .WithSummary("Get files from Google Drive")
            .WithDescription("List files from user's Google Drive")
            .Produces<object>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status500InternalServerError);

        // Move file between service and Google Drive
        googleDriveGroup.MapPost("/move", MoveFileToGoogleDrive)
            .WithName("MoveFileToGoogleDrive")
            .WithSummary("Move file to/from Google Drive")
            .WithDescription("Move a file between the service and Google Drive")
            .Produces(StatusCodes.Status204NoContent)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status404NotFound)
            .Produces(StatusCodes.Status500InternalServerError);

        return app;
    }

    /// <summary>
    /// Trigger Google Drive synchronization
    /// </summary>
    private static async Task<IResult> TriggerGoogleDriveSync(
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        [FromBody] TriggerSyncRequest? request,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new TriggerGoogleDriveSyncCommand
            {
                UserId = userId,
                FileId = request?.FileId,
                ForceSync = request?.ForceSync ?? false
            };

            var result = await mediator.Send(command, cancellationToken);
            return Results.Accepted("/api/v1/sync/status", result);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while triggering Google Drive sync",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get synchronization status
    /// </summary>
    private static async Task<IResult> GetSyncStatus(
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] Guid? fileId = null,
        [FromQuery] string? provider = null)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var query = new GetSyncStatusQuery
            {
                UserId = userId,
                FileId = fileId,
                Provider = provider
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting sync status",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get files from Google Drive
    /// </summary>
    private static async Task<IResult> GetGoogleDriveFiles(
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] string? parentFolderId = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var query = new GetGoogleDriveFilesQuery
            {
                UserId = userId,
                ParentFolderId = parentFolderId,
                Page = page,
                PageSize = pageSize
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting Google Drive files",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Move file to/from Google Drive
    /// </summary>
    private static async Task<IResult> MoveFileToGoogleDrive(
        [FromBody] MoveToGoogleDriveRequest request,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new MoveFileToGoogleDriveCommand
            {
                UserId = userId,
                FileId = request.FileId,
                Direction = request.Direction,
                TargetFolderId = request.TargetFolderId
            };

            await mediator.Send(command, cancellationToken);
            return Results.NoContent();
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (FileNotFoundException)
        {
            return Results.NotFound();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while moving file to Google Drive",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }
}

/// <summary>
/// Request model for triggering sync
/// </summary>
public record TriggerSyncRequest(
    Guid? FileId,
    bool ForceSync = false);

/// <summary>
/// Request model for moving files to Google Drive
/// </summary>
public record MoveToGoogleDriveRequest(
    Guid FileId,
    string Direction, // "to-gdrive" or "from-gdrive"
    string? TargetFolderId); 