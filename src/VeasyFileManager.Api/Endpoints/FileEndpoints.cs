using MediatR;
using Microsoft.AspNetCore.Mvc;
using FluentValidation;
using VeasyFileManager.Application.Commands.Files;
using VeasyFileManager.Application.Commands;
using VeasyFileManager.Application.Queries.Files;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Extensions;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Api.Extensions;
using VeasyFileManager.Domain.Enums;
using VeasyFileManager.Application.Helpers;

namespace VeasyFileManager.Api.Endpoints;

/// <summary>
/// File management endpoints
/// </summary>
public static class FileEndpoints
{
    /// <summary>
    /// Map file endpoints
    /// </summary>
    /// <param name="app">Web application</param>
    public static void MapFileEndpoints(this WebApplication app)
    {
        var fileGroup = app.MapGroup("/api/v1/files")
            .WithTags("Files")
            .RequireAuthorization();

        // Upload single file
        fileGroup.MapPost("/upload", UploadSingleFileAsync)
            .WithName("UploadSingleFile")
            .WithSummary("Upload a single file")
            .WithDescription("Upload a single file with metadata and optional folder placement")
            .DisableAntiforgery()
            .Accepts<UploadSingleFileRequest>("multipart/form-data")
            .Produces<FileDto>(StatusCodes.Status201Created)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status413PayloadTooLarge);

        // Upload multiple files
        fileGroup.MapPost("/upload/multiple", UploadMultipleFilesAsync)
            .WithName("UploadMultipleFiles")
            .WithSummary("Upload multiple files")
            .WithDescription("Upload multiple files in a single request")
            .DisableAntiforgery()
            .Accepts<UploadMultipleFilesRequest>("multipart/form-data")
            .Produces<BatchUploadResult>(StatusCodes.Status201Created)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized);

        // Chunked upload initialization
        fileGroup.MapPost("/upload/chunked/init", InitializeChunkedUploadAsync)
            .WithName("InitializeChunkedUpload")
            .WithSummary("Initialize chunked upload")
            .WithDescription("Initialize a chunked upload session for large files")
            .Produces<ChunkedUploadSession>(StatusCodes.Status201Created)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest);

        // Upload chunk
        fileGroup.MapPost("/upload/chunked/{sessionId}/chunk", UploadChunkAsync)
            .WithName("UploadChunk")
            .WithSummary("Upload file chunk")
            .WithDescription("Upload a chunk for an existing chunked upload session")
            .DisableAntiforgery()
            .Produces<ChunkUploadResult>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status404NotFound);

        // Complete chunked upload
        fileGroup.MapPost("/upload/chunked/{sessionId}/complete", CompleteChunkedUploadAsync)
            .WithName("CompleteChunkedUpload")
            .WithSummary("Complete chunked upload")
            .WithDescription("Complete a chunked upload session and create the final file")
            .Produces<FileDto>(StatusCodes.Status201Created)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status404NotFound);

        // Replace the old upload endpoint
        fileGroup.MapPost("/", UploadFileAsync)
            .WithName("UploadFile")
            .WithSummary("Upload a new file (legacy)")
            .WithDescription("Upload a file to the system with optional folder organization - legacy endpoint")
            .DisableAntiforgery()
            .Accepts<IFormFile>("multipart/form-data")
            .Produces<FileDto>(StatusCodes.Status201Created)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status413PayloadTooLarge);

        // Download file
        fileGroup.MapGet("/{id:guid}/download", DownloadFileAsync)
            .WithName("DownloadFile")
            .WithSummary("Download a file")
            .WithDescription("Download a file by ID, returns file stream or presigned URL")
            .Produces<FileResult>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status404NotFound);

        // Stream file for viewing (with range support for large files)
        fileGroup.MapGet("/{id:guid}/stream", StreamFileAsync)
            .WithName("StreamFile")
            .WithSummary("Stream a file for viewing")
            .WithDescription("Stream a file for frontend viewing with range request support for large files, images, videos, and documents")
            .Produces<FileResult>(StatusCodes.Status200OK)
            .Produces<FileResult>(StatusCodes.Status206PartialContent)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status404NotFound)
            .Produces(StatusCodes.Status416RangeNotSatisfiable);

        // Get file details
        fileGroup.MapGet("/{id:guid}", GetFileDetailsAsync)
            .WithName("GetFileDetails")
            .WithSummary("Get file details")
            .WithDescription("Get detailed information about a file")
            .Produces<FileDto>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status404NotFound);

        // Delete file
        fileGroup.MapDelete("/{id:guid}", DeleteFileAsync)
            .WithName("DeleteFile")
            .WithSummary("Delete a file")
            .WithDescription("Delete a file (soft delete by default)")
            .Produces(StatusCodes.Status204NoContent)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status404NotFound);

        // Update file metadata
        fileGroup.MapPut("/{id:guid}", UpdateFileAsync)
            .WithName("UpdateFile")
            .WithSummary("Update file metadata")
            .WithDescription("Update file name, description and other metadata")
            .Produces<FileDto>(StatusCodes.Status200OK)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status404NotFound);

        // Get user files
        fileGroup.MapGet("/", GetUserFilesAsync)
            .WithName("GetUserFiles")
            .WithSummary("Get user files")
            .WithDescription("Get paginated list of user files with filtering options")
            .Produces<PagedResult<FileDto>>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized);

        // Grant file permission
        fileGroup.MapPost("/{id:guid}/permissions", GrantFilePermissionAsync)
            .WithName("GrantFilePermission")
            .WithSummary("Grant permission to a user for a file")
            .WithDescription("Grant specific permission to a user or role for a file")
            .Produces<Guid>(StatusCodes.Status201Created)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden);

        // Get file permissions
        fileGroup.MapGet("/{id:guid}/permissions", GetFilePermissionsAsync)
            .WithName("GetFilePermissions")
            .WithSummary("Get all permissions for a file")
            .WithDescription("Get list of all permissions granted for a specific file")
            .Produces<List<FilePermissionDto>>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Revoke permission
        fileGroup.MapDelete("/permissions/{permissionId:guid}", RevokePermissionAsync)
            .WithName("RevokePermission")
            .WithSummary("Revoke a permission")
            .WithDescription("Revoke a specific permission by its ID")
            .Produces(StatusCodes.Status204NoContent)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Copy file
        fileGroup.MapPost("/{id:guid}/copy", CopyFileAsync)
            .WithName("CopyFile")
            .WithSummary("Copy a file")
            .WithDescription("Create a copy of a file in the same or different folder")
            .Produces<FileDto>(StatusCodes.Status201Created)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Move file
        fileGroup.MapPost("/{id:guid}/move", MoveFileAsync)
            .WithName("MoveFile")
            .WithSummary("Move a file")
            .WithDescription("Move a file to a different folder")
            .Produces(StatusCodes.Status204NoContent)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Create file share
        fileGroup.MapPost("/{id:guid}/share", CreateFileShareAsync)
            .WithName("CreateFileShare")
            .WithSummary("Create a share link for a file")
            .WithDescription("Create a public, password-protected, or user-specific share link")
            .Produces<FileShareDto>(StatusCodes.Status201Created)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Get file shares
        fileGroup.MapGet("/{id:guid}/shares", GetFileSharesAsync)
            .WithName("GetFileShares")
            .WithSummary("Get all shares for a file")
            .WithDescription("Get list of all share links created for a specific file")
            .Produces<List<FileShareDto>>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Share endpoints (outside of file group for anonymous access)
        var shareGroup = app.MapGroup("/api/v1/shares")
            .WithTags("Shares");

        // Access shared file (anonymous)
        shareGroup.MapGet("/{token}", AccessSharedFileAsync)
            .WithName("AccessSharedFile")
            .WithSummary("Access a shared file")
            .WithDescription("Access a file via share token (anonymous access allowed)")
            .AllowAnonymous()
            .Produces<FileDto>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status404NotFound);

        // Disable share link
        shareGroup.MapDelete("/{token}", DisableShareAsync)
            .WithName("DisableShare")
            .WithSummary("Disable a share link")
            .WithDescription("Disable/deactivate a share link")
            .RequireAuthorization()
            .Produces(StatusCodes.Status204NoContent)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);
    }

    private static async Task<IResult> UploadFileAsync(
        HttpRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            if (!request.HasFormContentType)
                return Results.BadRequest("Request must be multipart/form-data");

            var form = await request.ReadFormAsync(cancellationToken);
            var file = form.Files.FirstOrDefault();

            if (file == null || file.Length == 0)
                return Results.BadRequest("No file provided");

            var command = new UploadFileCommand
            {
                File = file,
                UserId = userId.Value,
                ParentFolderId = form.TryGetValue("parentFolderId", out var parentFolderIdStr) &&
                                Guid.TryParse(parentFolderIdStr, out var parentFolderId) ? parentFolderId : null,
                DisplayName = form.TryGetValue("displayName", out var displayName) ? displayName.ToString() : null,
                Description = form.TryGetValue("description", out var description) ? description.ToString() : null,
                SyncToGoogleDrive = form.TryGetValue("syncToGoogleDrive", out var syncStr) &&
                                   bool.TryParse(syncStr, out var sync) ? sync : true,
                Tags = form.TryGetValue("tags", out var tagsStr) ?
                       tagsStr.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries).ToList() :
                       new List<string>()
            };

            var result = await mediator.Send(command, cancellationToken);
            return Results.Created($"/api/v1/files/{result.Data.Id}", result.Data);
        }
        catch (ValidationException ex)
        {
            return Results.ValidationProblem(ex.Errors.ToDictionary(
                e => e.PropertyName,
                e => new[] { e.ErrorMessage }));
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Unauthorized();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while uploading the file",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> DownloadFileAsync(
        Guid id,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] bool presigned = false,
        [FromQuery] int expiration = 3600)
    {
        try
        {
            // Clean authentication check using extension method
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var query = new DownloadFileQuery
            {
                FileId = id,
                UserId = userId,
                GeneratePresignedUrl = presigned,
                PresignedUrlExpiration = TimeSpan.FromSeconds(expiration)
            };

            var result = await mediator.Send(query, cancellationToken);

            if (result.Data.IsPresignedUrl)
            {
                return Results.Ok(new { url = result.Data.PresignedUrl, expires = result.Data.UrlExpiration });
            }

            return Results.File(result.Data.ContentStream!, result.Data.ContentType, result.Data.FileName);
        }
        catch (FileNotFoundException)
        {
            return Results.NotFound();
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Unauthorized();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while downloading the file",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> StreamFileAsync(
        Guid id,
        HttpRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            // Clean authentication check
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            // First get basic file info to determine size for range validation
            var fileDetailsQuery = new GetFileDetailsQuery
            {
                FileId = id,
                UserId = userId
            };

            var fileDetailsResult = await mediator.Send(fileDetailsQuery, cancellationToken);
            if (fileDetailsResult?.Data == null)
            {
                return Results.NotFound("File not found");
            }

            var fileSize = fileDetailsResult.Data.FileSize;

            // Parse range header if present
            var range = RangeHelper.ParseRangeFromHeaders(request.Headers, fileSize);

            // Create stream query
            var streamQuery = new StreamFileQuery
            {
                FileId = id,
                UserId = userId,
                Range = range,
                EnableCaching = true,
                CacheDuration = TimeSpan.FromHours(1)
            };

            var result = await mediator.Send(streamQuery, cancellationToken);

            if (!result.Success || result.Data == null)
            {
                return result.StatusCode switch
                {
                    404 => Results.NotFound(result.Message),
                    403 => Results.Forbid(),
                    416 => Results.StatusCode(416), // Range Not Satisfiable
                    _ => Results.Problem(title: "Error streaming file", detail: result.Message, statusCode: result.StatusCode)
                };
            }

            var streamResponse = result.Data;
            var metadata = streamResponse.Metadata;

            // Determine if content is streamable for browser viewing
            var isStreamable = RangeHelper.IsStreamableContentType(metadata.ContentType);
            var contentDisposition = isStreamable ? "inline" : "attachment";

            // Set appropriate headers
            var response = request.HttpContext.Response;
            response.Headers.Accept = "bytes";
            response.Headers.ContentType = metadata.ContentType;
            response.Headers.ContentDisposition = $"{contentDisposition}; filename=\"{metadata.OriginalFileName}\"";

            // Add cache headers
            var cacheHeaders = RangeHelper.GetCacheHeaders(metadata.ContentType, streamQuery.CacheDuration);
            foreach (var header in cacheHeaders)
            {
                response.Headers[header.Key] = header.Value;
            }

            // Set ETag for caching
            if (!string.IsNullOrEmpty(metadata.ETag))
            {
                response.Headers.ETag = $"\"{metadata.ETag}\"";
            }

            // Handle range requests
            if (streamResponse.IsPartialContent && streamResponse.Range != null)
            {
                response.StatusCode = 206; // Partial Content
                response.Headers.ContentRange = RangeHelper.GenerateContentRangeHeader(streamResponse.Range, metadata.FileSize);
                response.Headers.ContentLength = streamResponse.ContentLength;
            }
            else
            {
                response.StatusCode = 200;
                response.Headers.ContentLength = metadata.FileSize;
            }

            // Stream the file content
            await streamResponse.Stream.CopyToAsync(response.Body, cancellationToken);

            return Results.Empty;
        }
        catch (ArgumentOutOfRangeException)
        {
            return Results.StatusCode(416); // Range Not Satisfiable
        }
        catch (FileNotFoundException)
        {
            return Results.NotFound("File not found in storage");
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while streaming the file",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> GetFileDetailsAsync(
        Guid id,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var query = new GetFileDetailsQuery
            {
                FileId = id,
                UserId = userId
            };

            var result = await mediator.Send(query, cancellationToken);

            if (result == null)
            {
                return Results.NotFound(new { error = "File not found" });
            }

            return Results.Ok(result);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Unauthorized();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting file details",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> DeleteFileAsync(
        Guid id,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] bool permanent = false)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new DeleteFileCommand
            {
                FileId = id,
                UserId = userId,
                PermanentDelete = permanent
            };

            var result = await mediator.Send(command, cancellationToken);

            if (!result.Data)
            {
                return Results.NotFound(new { error = "File not found" });
            }

            return Results.NoContent();
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Unauthorized();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while deleting the file",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> UpdateFileAsync(
        Guid id,
        UpdateFileRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new UpdateFileCommand
            {
                FileId = id,
                UserId = userId,
                DisplayName = request.DisplayName,
                Description = request.Description,
                ParentFolderId = request.ParentFolderId,
                Tags = request.Tags
            };

            var result = await mediator.Send(command, cancellationToken);
            return Results.Ok(result.Data);
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Unauthorized();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while updating the file",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> GetUserFilesAsync(
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] Guid? parentFolderId = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? search = null,
        [FromQuery] string sortBy = "CreatedAt",
        [FromQuery] string sortDirection = "DESC")
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var query = new GetUserFilesQuery
            {
                UserId = userId,
                ParentFolderId = parentFolderId,
                Page = page,
                PageSize = pageSize,
                SearchTerm = search,
                SortBy = sortBy,
                SortDirection = sortDirection
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting user files",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> GrantFilePermissionAsync(
        Guid id,
        GrantFilePermissionRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new GrantFilePermissionCommand
            {
                FileId = id,
                UserId = request.UserId,
                RoleId = request.RoleId,
                Permission = request.Permission,
                ExpiresAt = request.ExpiresAt,
                GrantedBy = userId
            };

            var permissionId = await mediator.Send(command, cancellationToken);
            return Results.Created($"/api/v1/files/{id}/permissions/{permissionId}", new { permissionId });
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while granting permission",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> GetFilePermissionsAsync(
        Guid id,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var query = new GetFilePermissionsQuery
            {
                FileId = id,
                UserId = userId
            };

            var permissions = await mediator.Send(query, cancellationToken);
            return Results.Ok(permissions);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting file permissions",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> RevokePermissionAsync(
        Guid permissionId,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new RevokePermissionCommand
            {
                PermissionId = permissionId,
                RevokedBy = userId
            };

            var result = await mediator.Send(command, cancellationToken);

            if (!result.Data)
            {
                return Results.NotFound(new { error = "Permission not found" });
            }

            return Results.NoContent();
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while revoking permission",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> CopyFileAsync(
        Guid id,
        CopyFileRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new CopyFileCommand
            {
                FileId = id,
                TargetFolderId = request.TargetFolderId,
                NewName = request.NewName,
                UserId = userId,
                SyncToGoogleDrive = request.SyncToGoogleDrive
            };

            var result = await mediator.Send(command, cancellationToken);
            return Results.Created($"/api/v1/files/{result.Data.Id}", result.Data);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (FileNotFoundException)
        {
            return Results.NotFound();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while copying the file",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> MoveFileAsync(
        Guid id,
        MoveFileRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new MoveFileCommand
            {
                FileId = id,
                TargetFolderId = request.TargetFolderId,
                UserId = userId
            };

            await mediator.Send(command, cancellationToken);
            return Results.NoContent();
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (FileNotFoundException)
        {
            return Results.NotFound();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while moving the file",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> CreateFileShareAsync(
        Guid id,
        CreateFileShareRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new CreateFileShareCommand
            {
                FileId = id,
                ShareType = request.ShareType,
                Password = request.Password,
                ExpiresAt = request.ExpiresAt,
                MaxDownloads = request.MaxDownloads,
                UserId = userId
            };

            var result = await mediator.Send(command, cancellationToken);
            return Results.Created($"/api/v1/shares/{result.Data.ShareToken}", result.Data);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (FileNotFoundException)
        {
            return Results.NotFound();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while creating file share",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> GetFileSharesAsync(
        Guid id,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var query = new GetFileSharesQuery
            {
                FileId = id,
                UserId = userId
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting file shares",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> AccessSharedFileAsync(
        string token,
        IMediator mediator,
        CancellationToken cancellationToken,
        [FromQuery] string? password = null)
    {
        try
        {
            var query = new GetSharedFileQuery
            {
                ShareToken = token,
                Password = password
            };

            var result = await mediator.Send(query, cancellationToken);
            if (result == null)
            {
                return Results.NotFound("Shared file not found or access denied");
            }

            return Results.Ok(result);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Unauthorized();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while accessing shared file",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    private static async Task<IResult> DisableShareAsync(
        string token,
        IMediator mediator,
        ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            var command = new DisableShareCommand
            {
                ShareToken = token,
                DisabledBy = userId
            };

            await mediator.Send(command, cancellationToken);
            return Results.NoContent();
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while disabling share",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    #region Enhanced Upload Methods

    /// <summary>
    /// Upload a single file with enhanced features
    /// </summary>
    private static async Task<IResult> UploadSingleFileAsync(
        HttpRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        ILogger<Program> logger,
        CancellationToken cancellationToken)
    {
        var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
        if (authResult != null) return authResult;

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            if (!request.HasFormContentType)
                return Results.BadRequest("Request must be multipart/form-data");

            var form = await request.ReadFormAsync(cancellationToken);
            var file = form.Files.FirstOrDefault();

            if (file == null || file.Length == 0)
                return Results.BadRequest("No file provided or file is empty");

            // Validate file
            var validationResult = ValidateFile(file);
            if (!validationResult.IsValid)
            {
                return Results.BadRequest(new { error = validationResult.ErrorMessage });
            }

            // Extract metadata from form
            var metadata = ExtractUploadMetadata(form);

            var command = new UploadFileCommand
            {
                File = file,
                UserId = userId,
                ParentFolderId = metadata.ParentFolderId,
                DisplayName = metadata.DisplayName ?? file.FileName,
                Description = metadata.Description,
                SyncToGoogleDrive = metadata.SyncToGoogleDrive,
                Tags = metadata.Tags
            };

            var result = await mediator.Send(command, cancellationToken);

            stopwatch.Stop();
            logger.LogFileUpload(result.Data.Id, userId, result.Data.Name, result.Data.FileSize,
                result.Data.MimeType, Domain.Enums.StorageProvider.R2);
            logger.LogPerformanceMetric("SingleFileUpload", stopwatch.Elapsed,
                $"Size: {file.Length} bytes");

            return Results.Created($"/api/v1/files/{result.Data.Id}", result.Data);
        }
        catch (ValidationException ex)
        {
            return Results.ValidationProblem(ex.Errors.ToDictionary(
                e => e.PropertyName, e => new[] { e.ErrorMessage }));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error uploading single file for user {UserId}", userId);
            return Results.Problem("An error occurred while uploading the file");
        }
    }

    /// <summary>
    /// Upload multiple files
    /// </summary>
    private static async Task<IResult> UploadMultipleFilesAsync(
        HttpRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        ILogger<Program> logger,
        CancellationToken cancellationToken)
    {
        var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
        if (authResult != null) return authResult;

        var overallStopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            if (!request.HasFormContentType)
                return Results.BadRequest("Request must be multipart/form-data");

            var form = await request.ReadFormAsync(cancellationToken);
            var files = form.Files;

            if (files.Count == 0)
                return Results.BadRequest("No files provided");

            // Parse batch settings
            var batchSettings = ExtractBatchUploadSettings(form);

            var result = new BatchUploadResult
            {
                TotalFiles = files.Count
            };

            // Process each file
            foreach (var file in files)
            {
                try
                {
                    var fileStopwatch = System.Diagnostics.Stopwatch.StartNew();

                    // Validate file
                    var validationResult = ValidateFile(file);
                    if (!validationResult.IsValid)
                    {
                        result.Errors.Add(new UploadError
                        {
                            FileName = file.FileName,
                            ErrorMessage = validationResult.ErrorMessage,
                            ErrorCode = "VALIDATION_FAILED"
                        });
                        result.FailedUploads++;

                        if (batchSettings.FailOnError)
                            break;
                        continue;
                    }

                    var command = new UploadFileCommand
                    {
                        File = file,
                        UserId = userId,
                        ParentFolderId = batchSettings.ParentFolderId,
                        DisplayName = file.FileName,
                        SyncToGoogleDrive = batchSettings.SyncToGoogleDrive,
                        Tags = batchSettings.Tags
                    };

                    var uploadResult = await mediator.Send(command, cancellationToken);
                    result.UploadedFiles.Add(uploadResult.Data);
                    result.SuccessfulUploads++;
                    result.TotalSizeUploaded += file.Length;

                    fileStopwatch.Stop();
                    logger.LogFileUpload(uploadResult.Data.Id, userId, uploadResult.Data.Name,
                        uploadResult.Data.FileSize, uploadResult.Data.MimeType, Domain.Enums.StorageProvider.R2);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Error uploading file {FileName} for user {UserId}",
                        file.FileName, userId);

                    result.Errors.Add(new UploadError
                    {
                        FileName = file.FileName,
                        ErrorMessage = ex.Message,
                        ErrorCode = "UPLOAD_FAILED"
                    });
                    result.FailedUploads++;

                    if (batchSettings.FailOnError)
                        break;
                }
            }

            overallStopwatch.Stop();
            result.TotalProcessingTime = overallStopwatch.Elapsed;

            logger.LogPerformanceMetric("BatchFileUpload", overallStopwatch.Elapsed,
                $"Files: {result.TotalFiles}, Success: {result.SuccessfulUploads}, Failed: {result.FailedUploads}");

            return Results.Created("/api/v1/files/upload/multiple", result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in batch upload for user {UserId}", userId);
            return Results.Problem("An error occurred during batch upload");
        }
    }

    /// <summary>
    /// Initialize chunked upload session
    /// </summary>
    private static async Task<IResult> InitializeChunkedUploadAsync(
        InitializeChunkedUploadRequest request,
        ICurrentUserService currentUserService,
        ILogger<Program> logger)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            // Validate request
            if (string.IsNullOrWhiteSpace(request.FileName))
                return Results.BadRequest("FileName is required");

            if (request.TotalFileSize <= 0)
                return Results.BadRequest("TotalFileSize must be greater than 0");

            // Calculate chunk parameters
            const int defaultChunkSize = 5 * 1024 * 1024; // 5MB chunks
            var totalChunks = (int)Math.Ceiling((double)request.TotalFileSize / defaultChunkSize);

            var session = new ChunkedUploadSession
            {
                SessionId = Guid.NewGuid(),
                FileName = request.FileName,
                TotalFileSize = request.TotalFileSize,
                ChunkSize = defaultChunkSize,
                TotalChunks = totalChunks,
                ExpiresAt = DateTime.UtcNow.AddHours(24), // 24 hour expiration
                UploadToken = GenerateUploadToken()
            };

            // TODO: Store session in cache/database

            logger.LogInformation("Chunked upload session initialized: {SessionId} for file {FileName} ({FileSize} bytes) by user {UserId}",
                session.SessionId, request.FileName, request.TotalFileSize, userId);

            return Results.Created($"/api/v1/files/upload/chunked/{session.SessionId}", session);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error initializing chunked upload");
            return Results.Problem("An error occurred while initializing chunked upload");
        }
    }

    /// <summary>
    /// Upload a chunk
    /// </summary>
    private static async Task<IResult> UploadChunkAsync(
        Guid sessionId,
        HttpRequest request,
        ILogger<Program> logger,
        CancellationToken cancellationToken)
    {
        try
        {
            // TODO: Retrieve session from cache/database
            // For now, return a mock response

            if (!request.HasFormContentType)
                return Results.BadRequest("Request must be multipart/form-data");

            var form = await request.ReadFormAsync(cancellationToken);
            var chunkFile = form.Files.FirstOrDefault();

            if (chunkFile == null)
                return Results.BadRequest("No chunk data provided");

            // Extract chunk metadata
            var chunkNumber = form.TryGetValue("chunkNumber", out var chunkNumStr) &&
                             int.TryParse(chunkNumStr, out var chunkNum) ? chunkNum : 0;

            // Calculate chunk hash for integrity
            using var stream = chunkFile.OpenReadStream();
            var chunkHash = await CalculateHashAsync(stream);

            // TODO: Store chunk and update session progress

            var result = new ChunkUploadResult
            {
                ChunkNumber = chunkNumber,
                ChunkSize = chunkFile.Length,
                ChunkHash = chunkHash,
                IsLastChunk = false, // TODO: Calculate based on session
                RemainingChunks = 0, // TODO: Calculate from session
                ProgressPercentage = 0.0 // TODO: Calculate from session
            };

            logger.LogInformation("Chunk {ChunkNumber} uploaded for session {SessionId} ({ChunkSize} bytes)",
                chunkNumber, sessionId, chunkFile.Length);

            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error uploading chunk for session {SessionId}", sessionId);
            return Results.Problem("An error occurred while uploading chunk");
        }
    }

    /// <summary>
    /// Complete chunked upload
    /// </summary>
    private static async Task<IResult> CompleteChunkedUploadAsync(
        Guid sessionId,
        CompleteChunkedUploadRequest request,
        IMediator mediator,
        ICurrentUserService currentUserService,
        ILogger<Program> logger)
    {
        try
        {
            var authResult = HttpContextExtensions.ValidateAuthentication(currentUserService, out var userId);
            if (authResult != null) return authResult;

            // TODO: Retrieve session and validate all chunks are uploaded
            // TODO: Combine chunks into final file
            // TODO: Create file record

            // Mock implementation for now
            var mockFile = new FileDto
            {
                Id = Guid.NewGuid(),
                Name = "chunked-upload-completed.bin",
                DisplayName = "Chunked Upload Completed",
                FileSize = 1024 * 1024, // 1MB mock size
                MimeType = "application/octet-stream",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            logger.LogInformation("Chunked upload completed for session {SessionId} by user {UserId}",
                sessionId, userId);

            return Results.Created($"/api/v1/files/{mockFile.Id}", mockFile);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error completing chunked upload for session {SessionId}", sessionId);
            return Results.Problem("An error occurred while completing chunked upload");
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// Validate uploaded file
    /// </summary>
    private static (bool IsValid, string ErrorMessage) ValidateFile(IFormFile file)
    {
        // File size validation (100MB max)
        const long maxFileSize = 100 * 1024 * 1024;
        if (file.Length > maxFileSize)
            return (false, $"File size exceeds maximum allowed size of {maxFileSize / (1024 * 1024)}MB");

        // File extension validation
        var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".docx", ".xlsx", ".txt", ".zip", ".rar" };
        var fileExtension = Path.GetExtension(file.FileName).ToLowerInvariant();

        if (!allowedExtensions.Contains(fileExtension))
            return (false, $"File type '{fileExtension}' is not allowed");

        // MIME type validation
        var allowedMimeTypes = new[]
        {
            "image/jpeg", "image/png", "image/gif",
            "application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "text/plain", "application/zip", "application/x-rar-compressed"
        };

        if (!allowedMimeTypes.Contains(file.ContentType))
            return (false, $"MIME type '{file.ContentType}' is not allowed");

        return (true, string.Empty);
    }

    /// <summary>
    /// Extract upload metadata from form
    /// </summary>
    private static (Guid? ParentFolderId, string? DisplayName, string? Description, bool SyncToGoogleDrive, List<string> Tags)
        ExtractUploadMetadata(IFormCollection form)
    {
        var parentFolderId = form.TryGetValue("parentFolderId", out var parentStr) &&
                            Guid.TryParse(parentStr, out var parentId) ? parentId : (Guid?)null;

        var displayName = form.TryGetValue("displayName", out var displayStr) ? displayStr.ToString() : null;
        var description = form.TryGetValue("description", out var descStr) ? descStr.ToString() : null;

        var syncToGoogleDrive = form.TryGetValue("syncToGoogleDrive", out var syncStr) &&
                               bool.TryParse(syncStr, out var sync) ? sync : true;

        var tags = form.TryGetValue("tags", out var tagsStr) ?
                   tagsStr.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries).ToList() :
                   new List<string>();

        return (parentFolderId, displayName, description, syncToGoogleDrive, tags);
    }

    /// <summary>
    /// Extract batch upload settings
    /// </summary>
    private static (Guid? ParentFolderId, bool SyncToGoogleDrive, bool FailOnError, List<string> Tags)
        ExtractBatchUploadSettings(IFormCollection form)
    {
        var parentFolderId = form.TryGetValue("parentFolderId", out var parentStr) &&
                            Guid.TryParse(parentStr, out var parentId) ? parentId : (Guid?)null;

        var syncToGoogleDrive = form.TryGetValue("syncToGoogleDrive", out var syncStr) &&
                               bool.TryParse(syncStr, out var sync) ? sync : true;

        var failOnError = form.TryGetValue("failOnError", out var failStr) &&
                         bool.TryParse(failStr, out var fail) ? fail : false;

        var tags = form.TryGetValue("tags", out var tagsStr) ?
                   tagsStr.ToString().Split(',', StringSplitOptions.RemoveEmptyEntries).ToList() :
                   new List<string>();

        return (parentFolderId, syncToGoogleDrive, failOnError, tags);
    }

    /// <summary>
    /// Generate secure upload token
    /// </summary>
    private static string GenerateUploadToken()
    {
        return Convert.ToBase64String(System.Security.Cryptography.RandomNumberGenerator.GetBytes(32));
    }

    /// <summary>
    /// Calculate SHA256 hash of stream
    /// </summary>
    private static async Task<string> CalculateHashAsync(Stream stream)
    {
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashBytes = await sha256.ComputeHashAsync(stream);
        return Convert.ToHexString(hashBytes).ToLowerInvariant();
    }

    #endregion
}

/// <summary>
/// Request model for updating file metadata
/// </summary>
public class UpdateFileRequest
{
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public Guid? ParentFolderId { get; set; }
    public List<string>? Tags { get; set; }
}

/// <summary>
/// Request model for granting file permissions
/// </summary>
public class GrantFilePermissionRequest
{
    public Guid? UserId { get; set; }
    public Guid? RoleId { get; set; }
    public PermissionType Permission { get; set; }
    public DateTime? ExpiresAt { get; set; }
}

/// <summary>
/// Request model for copying files
/// </summary>
public class CopyFileRequest
{
    public Guid? TargetFolderId { get; set; }
    public string? NewName { get; set; }
    public bool SyncToGoogleDrive { get; set; } = true;
}

/// <summary>
/// Request model for moving files
/// </summary>
public class MoveFileRequest
{
    public Guid? TargetFolderId { get; set; }
}

/// <summary>
/// Request model for creating file shares
/// </summary>
public class CreateFileShareRequest
{
    public ShareType ShareType { get; set; }
    public string? Password { get; set; }
    public DateTime? ExpiresAt { get; set; }
    public int? MaxDownloads { get; set; }
}

/// <summary>
/// Enhanced single file upload request
/// </summary>
public class UploadSingleFileRequest
{
    public IFormFile File { get; set; } = null!;
    public Guid? ParentFolderId { get; set; }
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public bool SyncToGoogleDrive { get; set; } = true;
    public List<string> Tags { get; set; } = new();
    public bool OverwriteExisting { get; set; } = false;
    public string? CustomMetadata { get; set; } // JSON string for additional metadata
}

/// <summary>
/// Multiple files upload request
/// </summary>
public class UploadMultipleFilesRequest
{
    public IFormFileCollection Files { get; set; } = null!;
    public Guid? ParentFolderId { get; set; }
    public bool SyncToGoogleDrive { get; set; } = true;
    public bool FailOnError { get; set; } = false; // If true, fail entire batch on any single file error
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Batch upload result
/// </summary>
public class BatchUploadResult
{
    public int TotalFiles { get; set; }
    public int SuccessfulUploads { get; set; }
    public int FailedUploads { get; set; }
    public List<FileDto> UploadedFiles { get; set; } = new();
    public List<UploadError> Errors { get; set; } = new();
    public TimeSpan TotalProcessingTime { get; set; }
    public long TotalSizeUploaded { get; set; }
}

/// <summary>
/// Upload error details
/// </summary>
public class UploadError
{
    public string FileName { get; set; } = null!;
    public string ErrorMessage { get; set; } = null!;
    public string ErrorCode { get; set; } = null!;
}

/// <summary>
/// Chunked upload session
/// </summary>
public class ChunkedUploadSession
{
    public Guid SessionId { get; set; }
    public string FileName { get; set; } = null!;
    public long TotalFileSize { get; set; }
    public int ChunkSize { get; set; }
    public int TotalChunks { get; set; }
    public DateTime ExpiresAt { get; set; }
    public string UploadToken { get; set; } = null!;
}

/// <summary>
/// Chunked upload initialization request
/// </summary>
public class InitializeChunkedUploadRequest
{
    public string FileName { get; set; } = null!;
    public long TotalFileSize { get; set; }
    public string ContentType { get; set; } = null!;
    public string? FileHash { get; set; } // SHA256 hash for integrity check
    public Guid? ParentFolderId { get; set; }
    public string? DisplayName { get; set; }
    public string? Description { get; set; }
    public bool SyncToGoogleDrive { get; set; } = true;
    public List<string> Tags { get; set; } = new();
}

/// <summary>
/// Chunk upload result
/// </summary>
public class ChunkUploadResult
{
    public int ChunkNumber { get; set; }
    public long ChunkSize { get; set; }
    public bool IsLastChunk { get; set; }
    public string ChunkHash { get; set; } = null!;
    public int RemainingChunks { get; set; }
    public double ProgressPercentage { get; set; }
}

/// <summary>
/// Complete chunked upload request
/// </summary>
public class CompleteChunkedUploadRequest
{
    public List<string> ChunkHashes { get; set; } = new(); // Hashes of all chunks for verification
    public string? FinalFileHash { get; set; } // SHA256 hash of complete file
}
