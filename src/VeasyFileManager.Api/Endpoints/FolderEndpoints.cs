using MediatR;
using Microsoft.AspNetCore.Mvc;
using Microsoft.OpenApi.Models;
using System.Net;
using VeasyFileManager.Application.Commands;
using VeasyFileManager.Application.Commands.Folders;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Application.Queries.Folders;
using VeasyFileManager.Domain.Enums;

namespace VeasyFileManager.Api.Endpoints;

/// <summary>
/// Folder management endpoints
/// </summary>
public static class FolderEndpoints
{
    /// <summary>
    /// Map folder endpoints to the application
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapFolderEndpoints(this WebApplication app)
    {
        var folderGroup = app.MapGroup("/api/v1/folders")
            .WithTags("Folders")
            .WithOpenApi()
            .RequireAuthorization();

        // List folders
        folderGroup.MapGet("/", GetUserFolders)
            .WithName("GetUserFolders")
            .WithSummary("List user folders")
            .WithDescription("Get paginated list of user folders with advanced filtering options")
            .WithOpenApi(operation => new(operation)
            {
                Summary = "List user folders with filtering",
                Description = "Get a paginated list of folders with support for search, filtering by uploader, date range, and inclusion of shared folders",
                Parameters = new List<OpenApiParameter>
                {
                    new() { Name = "parentFolderId", In = ParameterLocation.Query, Description = "Parent folder ID (null for root folders)", Schema = new OpenApiSchema { Type = "string", Format = "uuid" } },
                    new() { Name = "page", In = ParameterLocation.Query, Description = "Page number (1-based)", Schema = new OpenApiSchema { Type = "integer", Default = new Microsoft.OpenApi.Any.OpenApiInteger(1) } },
                    new() { Name = "pageSize", In = ParameterLocation.Query, Description = "Number of items per page", Schema = new OpenApiSchema { Type = "integer", Default = new Microsoft.OpenApi.Any.OpenApiInteger(20) } },
                    new() { Name = "search", In = ParameterLocation.Query, Description = "Search term for folder names", Schema = new OpenApiSchema { Type = "string" } },
                    new() { Name = "sortBy", In = ParameterLocation.Query, Description = "Sort field (Name, CreatedAt, UpdatedAt)", Schema = new OpenApiSchema { Type = "string", Default = new Microsoft.OpenApi.Any.OpenApiString("Name") } },
                    new() { Name = "sortDirection", In = ParameterLocation.Query, Description = "Sort direction (ASC/DESC)", Schema = new OpenApiSchema { Type = "string", Default = new Microsoft.OpenApi.Any.OpenApiString("ASC") } },
                    new() { Name = "uploaderEmail", In = ParameterLocation.Query, Description = "Filter by uploader email (future feature)", Schema = new OpenApiSchema { Type = "string" } },
                    new() { Name = "folderType", In = ParameterLocation.Query, Description = "Filter by folder type (future feature)", Schema = new OpenApiSchema { Type = "string" } },
                    new() { Name = "createdAfter", In = ParameterLocation.Query, Description = "Filter folders created after this date", Schema = new OpenApiSchema { Type = "string", Format = "date-time" } },
                    new() { Name = "createdBefore", In = ParameterLocation.Query, Description = "Filter folders created before this date", Schema = new OpenApiSchema { Type = "string", Format = "date-time" } },
                    new() { Name = "includeShared", In = ParameterLocation.Query, Description = "Include shared folders in results", Schema = new OpenApiSchema { Type = "boolean", Default = new Microsoft.OpenApi.Any.OpenApiBoolean(true) } }
                }
            })
            .Produces<object>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized);

        // Get folder statistics
        folderGroup.MapGet("/statistics", GetFolderStatistics)
            .WithName("GetFolderStatistics")
            .WithSummary("Get folder statistics")
            .WithDescription("Get summary statistics about user folders including counts, sizes, and recent activity")
            .WithOpenApi()
            .Produces<object>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized);

        // Create folder
        folderGroup.MapPost("/", CreateFolder)
            .WithName("CreateFolder")
            .WithSummary("Create a new folder")
            .WithDescription("Creates a new folder in the specified parent folder or root")
            .WithOpenApi(operation => new(operation)
            {
                Summary = "Create a new folder",
                Description = "Creates a new folder with the specified name and parent folder",
                RequestBody = new OpenApiRequestBody
                {
                    Description = "Folder creation request",
                    Required = true,
                    Content = new Dictionary<string, OpenApiMediaType>
                    {
                        ["application/json"] = new()
                        {
                            Schema = new OpenApiSchema
                            {
                                Type = "object",
                                Properties = new Dictionary<string, OpenApiSchema>
                                {
                                    ["name"] = new() { Type = "string", Description = "Folder name" },
                                    ["parentFolderId"] = new() { Type = "string", Format = "uuid", Description = "Parent folder ID (optional)" },
                                    ["description"] = new() { Type = "string", Description = "Folder description (optional)" }
                                },
                                Required = new HashSet<string> { "name" }
                            }
                        }
                    }
                },
                Responses = new OpenApiResponses
                {
                    ["201"] = new() { Description = "Folder created successfully" },
                    ["400"] = new() { Description = "Invalid request" },
                    ["401"] = new() { Description = "Unauthorized" },
                    ["403"] = new() { Description = "Forbidden" },
                    ["409"] = new() { Description = "Folder with same name already exists" }
                }
            })
            .Produces<FolderDto>((int)HttpStatusCode.Created)
            .ProducesValidationProblem()
            .Produces((int)HttpStatusCode.Unauthorized)
            .Produces((int)HttpStatusCode.Forbidden)
            .Produces((int)HttpStatusCode.Conflict);

        // Get folder details
        folderGroup.MapGet("/{id:guid}", GetFolderDetails)
            .WithName("GetFolderDetails")
            .WithSummary("Get folder details")
            .WithDescription("Retrieves detailed information about a specific folder")
            .WithOpenApi()
            .Produces<FolderDto>()
            .Produces((int)HttpStatusCode.NotFound)
            .Produces((int)HttpStatusCode.Unauthorized)
            .Produces((int)HttpStatusCode.Forbidden);

        // List folder contents
        folderGroup.MapGet("/{id:guid}/contents", GetFolderContents)
            .WithName("GetFolderContents")
            .WithSummary("Get folder contents")
            .WithDescription("Lists files and subfolders in a specific folder")
            .WithOpenApi()
            .Produces<object>()
            .Produces((int)HttpStatusCode.NotFound)
            .Produces((int)HttpStatusCode.Unauthorized)
            .Produces((int)HttpStatusCode.Forbidden);

        // Update folder
        folderGroup.MapPut("/{id:guid}", UpdateFolder)
            .WithName("UpdateFolder")
            .WithSummary("Update folder")
            .WithDescription("Updates folder name or moves it to a different parent")
            .WithOpenApi()
            .Produces<FolderDto>()
            .ProducesValidationProblem()
            .Produces((int)HttpStatusCode.NotFound)
            .Produces((int)HttpStatusCode.Unauthorized)
            .Produces((int)HttpStatusCode.Forbidden);

        // Delete folder
        folderGroup.MapDelete("/{id:guid}", DeleteFolder)
            .WithName("DeleteFolder")
            .WithSummary("Delete folder")
            .WithDescription("Deletes a folder and all its contents")
            .WithOpenApi()
            .Produces((int)HttpStatusCode.NoContent)
            .Produces((int)HttpStatusCode.NotFound)
            .Produces((int)HttpStatusCode.Unauthorized)
            .Produces((int)HttpStatusCode.Forbidden);

        // Bulk delete folders
        folderGroup.MapDelete("/bulk", BulkDeleteFolders)
            .WithName("BulkDeleteFolders")
            .WithSummary("Bulk delete folders")
            .WithDescription("Delete multiple folders and their contents at once")
            .WithOpenApi(operation => new(operation)
            {
                Summary = "Bulk delete folders",
                Description = "Delete multiple folders and all their contents in a single operation",
                RequestBody = new OpenApiRequestBody
                {
                    Description = "Bulk delete request with folder IDs",
                    Required = true,
                    Content = new Dictionary<string, OpenApiMediaType>
                    {
                        ["application/json"] = new()
                        {
                            Schema = new OpenApiSchema
                            {
                                Type = "object",
                                Properties = new Dictionary<string, OpenApiSchema>
                                {
                                    ["folderIds"] = new()
                                    {
                                        Type = "array",
                                        Items = new OpenApiSchema { Type = "string", Format = "uuid" },
                                        Description = "Array of folder IDs to delete"
                                    },
                                    ["force"] = new() { Type = "boolean", Description = "Force delete even if folders are not empty", Default = new Microsoft.OpenApi.Any.OpenApiBoolean(false) },
                                    ["permanent"] = new() { Type = "boolean", Description = "Permanently delete folders", Default = new Microsoft.OpenApi.Any.OpenApiBoolean(false) }
                                },
                                Required = new HashSet<string> { "folderIds" }
                            }
                        }
                    }
                }
            })
            .Produces<object>(StatusCodes.Status200OK)
            .ProducesValidationProblem()
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden);

        // Move folder
        folderGroup.MapPost("/{id:guid}/move", MoveFolder)
            .WithName("MoveFolder")
            .WithSummary("Move folder")
            .WithDescription("Move a folder to a different parent folder")
            .Produces(StatusCodes.Status204NoContent)
            .Produces(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Download folder as ZIP
        folderGroup.MapGet("/{id:guid}/download", DownloadFolder)
            .WithName("DownloadFolder")
            .WithSummary("Download folder as ZIP")
            .WithDescription("Download a folder and its contents as a ZIP file")
            .Produces<FileResult>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Folder permissions endpoints
        folderGroup.MapGet("/{id:guid}/permissions", GetFolderPermissions)
            .WithName("GetFolderPermissions")
            .WithSummary("Get folder permissions")
            .WithDescription("Get all permissions granted on a folder")
            .Produces<List<FolderPermissionDto>>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        folderGroup.MapPost("/{id:guid}/permissions", GrantFolderPermission)
            .WithName("GrantFolderPermission")
            .WithSummary("Grant folder permission")
            .WithDescription("Grant permission to a user or role on a folder")
            .Produces<Guid>(StatusCodes.Status201Created)
            .ProducesValidationProblem()
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        folderGroup.MapDelete("/{id:guid}/permissions/{permissionId:guid}", RevokeFolderPermission)
            .WithName("RevokeFolderPermission")
            .WithSummary("Revoke folder permission")
            .WithDescription("Revoke a specific permission from a folder")
            .Produces(StatusCodes.Status204NoContent)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        // Folder sharing endpoints
        folderGroup.MapPost("/{id:guid}/share", CreateFolderShare)
            .WithName("CreateFolderShare")
            .WithSummary("Create a share link for a folder")
            .WithDescription("Create a public, password-protected, or user-specific share link for a folder")
            .Produces<FolderShareDto>(StatusCodes.Status201Created)
            .ProducesValidationProblem()
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        folderGroup.MapGet("/{id:guid}/shares", GetFolderShares)
            .WithName("GetFolderShares")
            .WithSummary("Get folder shares")
            .WithDescription("Get all active share links for a folder")
            .Produces<List<FolderShareDto>>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces(StatusCodes.Status404NotFound);

        return app;
    }

    /// <summary>
    /// Create a new folder
    /// </summary>
    private static async Task<IResult> CreateFolder(
        [FromBody] CreateFolderRequest request,
        [FromServices] IMediator mediator,
        HttpContext context,
        CancellationToken cancellationToken)
    {
        try
        {
            var command = new CreateFolderCommand
            {
                Name = request.Name,
                ParentFolderId = request.ParentFolderId,
                Description = request.Description
            };

            var result = await mediator.Send(command, cancellationToken);
            return Results.Created($"/api/folders/{result.Data.Id}", result);
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (UnauthorizedAccessException ex)
        {
            return Results.Forbid();
        }
        catch (InvalidOperationException ex) when (ex.Message.Contains("already exists"))
        {
            return Results.Conflict(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem($"An error occurred while creating the folder: {ex.Message}");
        }
    }

    /// <summary>
    /// Get folder details
    /// </summary>
    private static async Task<IResult> GetFolderDetails(
        Guid id,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var query = new GetFolderDetailsQuery
            {
                FolderId = id,
                UserId = userId.Value
            };

            var result = await mediator.Send(query, cancellationToken);

            if (result == null)
            {
                return Results.NotFound(new { error = "Folder not found" });
            }

            return Results.Ok(result);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (Exception ex)
        {
            return Results.Problem($"An error occurred while retrieving folder details: {ex.Message}");
        }
    }

    /// <summary>
    /// Get folder contents (files and subfolders)
    /// </summary>
    private static async Task<IResult> GetFolderContents(
        Guid id,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? sortBy = "Name",
        [FromQuery] string? sortDirection = "ASC")
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var query = new GetFolderContentsQuery
            {
                FolderId = id,
                UserId = userId.Value,
                Page = page,
                PageSize = pageSize,
                SortBy = sortBy ?? "Name",
                SortDirection = sortDirection ?? "ASC"
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (ArgumentException ex)
        {
            return Results.NotFound(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem($"An error occurred while retrieving folder contents: {ex.Message}");
        }
    }

    /// <summary>
    /// Update folder
    /// </summary>
    private static async Task<IResult> UpdateFolder(
        Guid id,
        [FromBody] UpdateFolderRequest request,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var command = new UpdateFolderCommand
            {
                FolderId = id,
                UserId = userId.Value,
                Name = request.Name,
                ParentFolderId = request.ParentFolderId,
                Description = request.Description
            };

            var result = await mediator.Send(command, cancellationToken);
            return Results.Ok(result);
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (InvalidOperationException ex)
        {
            return Results.Conflict(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem($"An error occurred while updating the folder: {ex.Message}");
        }
    }

    /// <summary>
    /// Delete folder
    /// </summary>
    private static async Task<IResult> DeleteFolder(
        Guid id,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] bool force = false,
        [FromQuery] bool permanent = false)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var command = new DeleteFolderCommand
            {
                FolderId = id,
                UserId = userId.Value,
                Force = force,
                PermanentDelete = permanent
            };

            var result = await mediator.Send(command, cancellationToken);

            if (!result.Data)
            {
                return Results.NotFound(new { error = "Folder not found" });
            }

            return Results.NoContent();
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (InvalidOperationException ex)
        {
            return Results.Conflict(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem($"An error occurred while deleting the folder: {ex.Message}");
        }
    }

    /// <summary>
    /// Bulk delete folders
    /// </summary>
    private static async Task<IResult> BulkDeleteFolders(
        [FromBody] BulkDeleteFoldersRequest request,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            if (request.FolderIds == null || !request.FolderIds.Any())
            {
                return Results.BadRequest(new { error = "No folder IDs provided" });
            }

            var results = new List<object>();
            var successCount = 0;
            var failureCount = 0;

            foreach (var folderId in request.FolderIds)
            {
                try
                {
                    var command = new DeleteFolderCommand
                    {
                        FolderId = folderId,
                        UserId = userId.Value,
                        Force = request.Force,
                        PermanentDelete = request.Permanent
                    };

                    var result = await mediator.Send(command, cancellationToken);

                    if (result.Data)
                    {
                        successCount++;
                        results.Add(new { folderId, success = true });
                    }
                    else
                    {
                        failureCount++;
                        results.Add(new { folderId, success = false, error = "Folder not found" });
                    }
                }
                catch (UnauthorizedAccessException)
                {
                    failureCount++;
                    results.Add(new { folderId, success = false, error = "Unauthorized access" });
                }
                catch (InvalidOperationException ex)
                {
                    failureCount++;
                    results.Add(new { folderId, success = false, error = ex.Message });
                }
                catch (Exception ex)
                {
                    failureCount++;
                    results.Add(new { folderId, success = false, error = ex.Message });
                }
            }

            return Results.Ok(new
            {
                totalRequested = request.FolderIds.Count(),
                successCount,
                failureCount,
                results
            });
        }
        catch (Exception ex)
        {
            return Results.Problem($"An error occurred during bulk delete operation: {ex.Message}");
        }
    }

    /// <summary>
    /// Get user folders
    /// </summary>
    private static async Task<IResult> GetUserFolders(
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] Guid? parentFolderId = null,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? search = null,
        [FromQuery] string sortBy = "Name",
        [FromQuery] string sortDirection = "ASC",
        [FromQuery] string? uploaderEmail = null,
        [FromQuery] string? folderType = null,
        [FromQuery] DateTime? createdAfter = null,
        [FromQuery] DateTime? createdBefore = null,
        [FromQuery] bool includeShared = true)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var query = new GetUserFoldersQuery
            {
                UserId = userId.Value,
                ParentFolderId = parentFolderId,
                Page = page,
                PageSize = pageSize,
                Search = search,
                SortBy = sortBy,
                SortDirection = sortDirection,
                UploaderEmail = uploaderEmail,
                FolderType = folderType,
                CreatedAfter = createdAfter,
                CreatedBefore = createdBefore,
                IncludeShared = includeShared
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting user folders",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get folder statistics
    /// </summary>
    private static async Task<IResult> GetFolderStatistics(
        [FromServices] IFolderRepository folderRepository,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            // Get all user folders
            var allFolders = await folderRepository.GetByOwnerAsync(userId.Value, cancellationToken);
            var foldersList = allFolders.ToList();

            // Calculate statistics
            var statistics = new
            {
                totalFolders = foldersList.Count,
                rootFolders = foldersList.Count(f => f.ParentFolderId == null),
                nestedFolders = foldersList.Count(f => f.ParentFolderId != null),
                foldersCreatedToday = foldersList.Count(f => f.CreatedAt.Date == DateTime.UtcNow.Date),
                foldersCreatedThisWeek = foldersList.Count(f => f.CreatedAt >= DateTime.UtcNow.AddDays(-7)),
                foldersCreatedThisMonth = foldersList.Count(f => f.CreatedAt >= DateTime.UtcNow.AddDays(-30)),
                oldestFolder = foldersList.OrderBy(f => f.CreatedAt).FirstOrDefault()?.CreatedAt,
                newestFolder = foldersList.OrderByDescending(f => f.CreatedAt).FirstOrDefault()?.CreatedAt,
                averageFolderDepth = foldersList.Any() ? foldersList.Average(f => f.Level) : 0,
                maxFolderDepth = foldersList.Any() ? foldersList.Max(f => f.Level) : 0
            };

            return Results.Ok(statistics);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting folder statistics",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Move folder
    /// </summary>
    private static async Task<IResult> MoveFolder(
        Guid id,
        MoveFolderRequest request,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var command = new MoveFolderCommand
            {
                FolderId = id,
                TargetParentFolderId = request.TargetParentFolderId,
                UserId = userId.Value
            };

            await mediator.Send(command, cancellationToken);
            return Results.NoContent();
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while moving the folder",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Download folder as ZIP
    /// </summary>
    private static async Task<IResult> DownloadFolder(
        Guid id,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken,
        [FromQuery] bool includeSubfolders = true,
        [FromQuery] long maxZipSize = 1073741824) // 1GB default
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var query = new DownloadFolderAsZipQuery
            {
                FolderId = id,
                UserId = userId.Value,
                IncludeSubfolders = includeSubfolders,
                MaxZipSize = maxZipSize
            };

            var result = await mediator.Send(query, cancellationToken);

            if (result.Data != null)
                return Results.File(
                    fileStream: result.Data.ZipStream,
                    contentType: result.Data.ContentType,
                    fileDownloadName: result.Data.FileName);

            return Results.NotFound(new { error = "Folder not found" });
        }
        catch (FileNotFoundException)
        {
            return Results.NotFound(new { error = "Folder not found" });
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (InvalidOperationException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while downloading the folder",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get folder permissions
    /// </summary>
    private static async Task<IResult> GetFolderPermissions(
        Guid id,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var query = new GetFolderPermissionsQuery
            {
                FolderId = id,
                RequesterId = userId.Value
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (ArgumentException)
        {
            return Results.NotFound();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting folder permissions",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Grant folder permission
    /// </summary>
    private static async Task<IResult> GrantFolderPermission(
        Guid id,
        GrantFolderPermissionRequest request,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var command = new GrantFolderPermissionCommand
            {
                FolderId = id,
                UserId = request.UserId,
                RoleId = request.RoleId,
                Permission = request.Permission,
                ExpiresAt = request.ExpiresAt,
                InheritToChildren = request.InheritToChildren,
                GrantedBy = userId.Value
            };

            var result = await mediator.Send(command, cancellationToken);
            return Results.Created($"/api/v1/folders/{id}/permissions/{result}", result);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while granting folder permission",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Revoke folder permission
    /// </summary>
    private static async Task<IResult> RevokeFolderPermission(
        Guid id,
        Guid permissionId,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var command = new RevokePermissionCommand
            {
                PermissionId = permissionId,
                RevokedBy = userId.Value
            };

            await mediator.Send(command, cancellationToken);
            return Results.NoContent();
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (ArgumentException)
        {
            return Results.NotFound();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while revoking folder permission",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Create folder share
    /// </summary>
    private static async Task<IResult> CreateFolderShare(
        Guid id,
        CreateFolderShareRequest request,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var command = new CreateFolderShareCommand
            {
                FolderId = id,
                ShareType = request.ShareType,
                Password = request.Password,
                ExpiresAt = request.ExpiresAt,
                MaxDownloads = request.MaxDownloads,
                IncludeSubfolders = request.IncludeSubfolders,
                UserId = userId.Value
            };

            var result = await mediator.Send(command, cancellationToken);
            return Results.Created($"/api/v1/shares/{result.Data.ShareToken}", result.Data);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (ArgumentException ex)
        {
            return Results.BadRequest(new { error = ex.Message });
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while creating folder share",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }

    /// <summary>
    /// Get folder shares
    /// </summary>
    private static async Task<IResult> GetFolderShares(
        Guid id,
        [FromServices] IMediator mediator,
        [FromServices] ICurrentUserService currentUserService,
        CancellationToken cancellationToken)
    {
        try
        {
            var userId = currentUserService.UserId;
            if (!userId.HasValue)
            {
                return Results.Unauthorized();
            }

            var query = new GetFolderSharesQuery
            {
                FolderId = id,
                UserId = userId.Value
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (UnauthorizedAccessException)
        {
            return Results.Forbid();
        }
        catch (ArgumentException)
        {
            return Results.NotFound();
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting folder shares",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }
}

/// <summary>
/// Request model for creating a folder
/// </summary>
public record CreateFolderRequest(
    string Name,
    Guid? ParentFolderId,
    string? Description);

/// <summary>
/// Request model for updating a folder
/// </summary>
public record UpdateFolderRequest(
    string? Name,
    Guid? ParentFolderId,
    string? Description);

/// <summary>
/// Request model for moving a folder
/// </summary>
public record MoveFolderRequest(
    Guid? TargetParentFolderId);

/// <summary>
/// Request model for granting folder permissions
/// </summary>
public record GrantFolderPermissionRequest(
    Guid? UserId,
    Guid? RoleId,
    PermissionType Permission,
    DateTime? ExpiresAt,
    bool InheritToChildren = true);

/// <summary>
/// Request model for creating folder shares
/// </summary>
public record CreateFolderShareRequest(
    ShareType ShareType,
    string? Password,
    DateTime? ExpiresAt,
    int? MaxDownloads,
    bool IncludeSubfolders = true);

public record BulkDeleteFoldersRequest(
    IEnumerable<Guid> FolderIds,
    bool Force = false,
    bool Permanent = false);
