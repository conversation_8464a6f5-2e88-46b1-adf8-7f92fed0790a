using MediatR;
using Microsoft.AspNetCore.Mvc;
using VeasyFileManager.Application.DTOs;
using VeasyFileManager.Application.Queries;

namespace VeasyFileManager.Api.Endpoints;

/// <summary>
/// Permission-related endpoints
/// </summary>
public static class PermissionEndpoints
{
    /// <summary>
    /// Map permission endpoints to the application
    /// </summary>
    /// <param name="app">Web application</param>
    /// <returns>Web application</returns>
    public static WebApplication MapPermissionEndpoints(this WebApplication app)
    {
        var permissionGroup = app.MapGroup("/api/v1/permissions")
            .WithTags("Permissions")
            .WithOpenApi()
            .RequireAuthorization();

        // Get available permission types
        permissionGroup.MapGet("/types", GetAvailablePermissionTypes)
            .WithName("GetAvailablePermissionTypes")
            .WithSummary("Get available permission types")
            .WithDescription("Get all available permission types that can be assigned to files and folders")
            .Produces<List<PermissionTypeDto>>(StatusCodes.Status200OK)
            .Produces(StatusCodes.Status500InternalServerError);

        return app;
    }

    /// <summary>
    /// Get available permission types
    /// </summary>
    private static async Task<IResult> GetAvailablePermissionTypes(
        [FromServices] IMediator mediator,
        [FromQuery] string? resourceType = null,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetAvailablePermissionsQuery
            {
                ResourceType = resourceType
            };

            var result = await mediator.Send(query, cancellationToken);
            return Results.Ok(result);
        }
        catch (Exception ex)
        {
            return Results.Problem(
                title: "An error occurred while getting available permission types",
                detail: ex.Message,
                statusCode: StatusCodes.Status500InternalServerError);
        }
    }
} 