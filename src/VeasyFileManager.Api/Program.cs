using Microsoft.EntityFrameworkCore;
using Npgsql;
using VeasyFileManager.Infrastructure.Data;
using VeasyFileManager.Infrastructure.Repositories;
using VeasyFileManager.Application.Behaviors;
using VeasyFileManager.Application.Mappings;
using VeasyFileManager.Application.Interfaces;
using VeasyFileManager.Api.Endpoints;
using FluentValidation;
using FluentValidation.Results;
using System.Reflection;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using VeasyFileManager.Infrastructure.Configurations;
using VeasyFileManager.Infrastructure.Services;
using VeasyFileManager.Api.Middleware;
using VeasyFileManager.Application.Extensions;
using Serilog;
using Serilog.Events;
using Microsoft.OpenApi.Models;

// Configure Serilog before building the host
var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .Enrich.FromLogContext()
    // .Enrich.WithThreadId()
    // .Enrich.WithProperty("Application", "VeasyFileManager")
    // .Enrich.WithProperty("Version", "1.0.0")
    // .Enrich.WithProperty("Environment", builder.Environment.EnvironmentName)
    .WriteTo.Console()
    .WriteTo.File(
        path: "logs/log-.txt",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30,
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {CorrelationId} {Message:lj} {Properties:j}{NewLine}{Exception}")
    .CreateBootstrapLogger();

// Configure the web host to use Serilog
builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new() { Title = "VeasyFileManager API", Version = "v1" });

    // Add JWT Bearer Authentication
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme. Enter 'Bearer' [space] and then your token in the text input below.\r\n\r\nExample: \"Bearer 12345abcdef\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.Http,
        Scheme = "bearer",
        BearerFormat = "JWT"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });

    // Include XML comments if available
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Configure Entity Framework
builder.Services.AddDbContext<StorageDbContext>(options =>
    options.UseNpgsql(builder.Configuration.GetConnectionString("StorageConnection")));

// Configure MediatR
builder.Services.AddMediatR(cfg =>
{
    cfg.RegisterServicesFromAssembly(typeof(VeasyFileManager.Application.DTOs.FileDto).Assembly);

    // Add pipeline behaviors in order of execution
    cfg.AddOpenBehavior(typeof(RequestResponseLoggingBehavior<,>));
    cfg.AddOpenBehavior(typeof(PerformanceLoggingBehavior<,>));
    cfg.AddOpenBehavior(typeof(ExceptionHandlingBehavior<,>));
    cfg.AddOpenBehavior(typeof(ValidationBehavior<,>));
});

// Configure AutoMapper
builder.Services.AddAutoMapper(typeof(VeasyFileManager.Application.Mappings.FileProfile).Assembly);

// Configure FluentValidation
builder.Services.AddValidatorsFromAssembly(typeof(VeasyFileManager.Application.DTOs.FileDto).Assembly);

// Register Repositories
builder.Services.AddScoped<IFileRepository, FileRepository>();
builder.Services.AddScoped<IFolderRepository, FolderRepository>();
builder.Services.AddScoped<ISyncStatusRepository, SyncStatusRepository>();
builder.Services.AddScoped<IDeletedItemRepository, DeletedItemRepository>();

// Configure Cloudflare R2 Options
builder.Services.Configure<CloudflareR2Options>(builder.Configuration.GetSection(CloudflareR2Options.SectionName));

// Register Storage Service
var r2Options = builder.Configuration.GetSection(CloudflareR2Options.SectionName).Get<CloudflareR2Options>();
if (r2Options?.IsValid() == true)
{
    builder.Services.AddScoped<IFileStorageService, VeasyFileManager.Infrastructure.Services.CloudflareR2StorageService>();
    Log.Information("CloudflareR2StorageService registered successfully");
}
else
{
    // For development/testing environments without proper R2 configuration
    Log.Warning("Cloudflare R2 configuration is invalid or missing. Using null storage service.");
}

// Register User Service
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();

// Register Permission Service
builder.Services.AddScoped<IFilePermissionService, VeasyFileManager.Infrastructure.Services.FilePermissionService>();

// Register File Sharing Service
builder.Services.AddScoped<IFileSharingService, VeasyFileManager.Infrastructure.Services.FileSharingService>();

// Register Google Drive Service
builder.Services.Configure<GoogleDriveOptions>(builder.Configuration.GetSection("GoogleDrive"));
builder.Services.AddScoped<IGoogleDriveService, VeasyFileManager.Infrastructure.Services.GoogleDriveService>();

// Register Background Services
// Configure Authentication and Authorization
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme);
builder.Services.AddAuthorization();

// Configure JWT Authentication
var jwtOptions = builder.Configuration.GetSection(JwtOptions.SectionName).Get<JwtOptions>();
if (jwtOptions != null && jwtOptions.IsValid())
{
    builder.Services.Configure<JwtOptions>(builder.Configuration.GetSection(JwtOptions.SectionName));

    builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
        .AddJwtBearer(options =>
        {
            options.Authority = jwtOptions.Authority;
            options.Audience = jwtOptions.Audience;
            options.RequireHttpsMetadata = jwtOptions.RequireHttpsMetadata;

            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = jwtOptions.ValidateIssuer,
                ValidateAudience = jwtOptions.ValidateAudience,
                ValidateLifetime = jwtOptions.ValidateLifetime,
                ValidateIssuerSigningKey = true,
                ValidIssuer = jwtOptions.Issuer,
                ValidAudience = jwtOptions.Audience,
                ClockSkew = jwtOptions.ClockSkew
            };
        });

    Log.Information("JWT Authentication configured with Authority: {Authority}", jwtOptions.Authority);
}
else
{
    Log.Warning("JWT Authentication configuration not found or invalid");
}

// Configure Rate Limiting
builder.Services.Configure<RateLimitOptions>(
    builder.Configuration.GetSection(RateLimitOptions.SectionName));

// Configure CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

var app = builder.Build();

// Configure middleware pipeline
app.UseCorrelationId();
app.UseGlobalErrorHandling();
app.UseMiddleware<VeasyFileManager.Api.Middleware.RateLimitingMiddleware>();


app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "VeasyFileManager API v1");
});

app.UseHttpsRedirection();
app.UseCors();

// Authentication & Authorization
app.UseAuthentication();
app.UseAuthorization();
app.MapFileEndpoints();
app.MapFolderEndpoints();
app.MapSyncEndpoints();
app.MapPermissionEndpoints();
app.MapRecycleBinEndpoints();

try
{
    Log.Information("Starting VeasyFileManager API v{Version} in {Environment} environment", "1.0.0", builder.Environment.EnvironmentName);
    Log.Information("Serilog configured with Console, File and Elasticsearch sinks");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
