using System.Collections.Concurrent;
using Microsoft.Extensions.Options;
using VeasyFileManager.Application.Interfaces;

namespace VeasyFileManager.Api.Middleware;

/// <summary>
/// Configuration options for rate limiting
/// </summary>
public class RateLimitOptions
{
    public const string SectionName = "RateLimit";

    /// <summary>
    /// Global requests per minute limit
    /// </summary>
    public int GlobalRpm { get; set; } = 1000;

    /// <summary>
    /// Per-user requests per minute limit
    /// </summary>
    public int PerUserRpm { get; set; } = 100;

    /// <summary>
    /// Upload requests per user per minute
    /// </summary>
    public int UploadRpm { get; set; } = 10;

    /// <summary>
    /// Download requests per user per minute
    /// </summary>
    public int DownloadRpm { get; set; } = 50;

    /// <summary>
    /// Share creation requests per user per minute
    /// </summary>
    public int ShareRpm { get; set; } = 5;

    /// <summary>
    /// Time window in minutes
    /// </summary>
    public int WindowMinutes { get; set; } = 1;

    /// <summary>
    /// Enable rate limiting
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// IP addresses to whitelist (bypass rate limiting)
    /// </summary>
    public List<string> WhitelistedIps { get; set; } = new();
}

/// <summary>
/// Request tracking information
/// </summary>
public class RequestCounter
{
    public int Count { get; set; }
    public DateTime WindowStart { get; set; }
    public DateTime LastRequest { get; set; }
}

/// <summary>
/// Rate limiting middleware
/// </summary>
public class RateLimitingMiddleware(
    RequestDelegate next,
    IOptions<RateLimitOptions> options,
    ILogger<RateLimitingMiddleware> logger)
{
    private readonly RequestDelegate _next = next;
    private readonly RateLimitOptions _options = options.Value;
    private readonly ILogger<RateLimitingMiddleware> _logger = logger;

    // In-memory storage for request counts
    // Note: In production, consider using Redis for distributed scenarios
    private static readonly ConcurrentDictionary<string, RequestCounter> _requestCounts = new();
    private static readonly Lock _cleanupLock = new();
    private static DateTime _lastCleanup = DateTime.UtcNow;

    /// <summary>
    /// Process HTTP request with rate limiting
    /// </summary>
    public async Task InvokeAsync(HttpContext context)
    {
        // Skip rate limiting if disabled
        if (!_options.Enabled)
        {
            await _next(context);
            return;
        }

        // Skip rate limiting for whitelisted IPs
        var clientIp = GetClientIpAddress(context);
        if (_options.WhitelistedIps.Contains(clientIp))
        {
            await _next(context);
            return;
        }

        // Skip rate limiting for health checks
        if (context.Request.Path.StartsWithSegments("/health"))
        {
            await _next(context);
            return;
        }

        // Periodic cleanup of old entries
        PerformCleanupIfNeeded();

        // Get rate limit based on endpoint and user
        var rateLimit = GetRateLimit(context);
        
        // Resolve ICurrentUserService from request scope
        var currentUserService = context.RequestServices.GetService<ICurrentUserService>();
        var userId = currentUserService?.UserId?.ToString() ?? "anonymous";
        var endpoint = GetEndpointIdentifier(context);

        // Create composite key for tracking
        var compositeKey = $"{userId}:{endpoint}";
        var globalKey = "global";

        // Check global rate limit
        if (!CheckRateLimit(globalKey, _options.GlobalRpm))
        {
            await HandleRateLimitExceeded(context, "Global rate limit exceeded");
            return;
        }

        // Check per-user rate limit
        if (!CheckRateLimit(compositeKey, rateLimit))
        {
            await HandleRateLimitExceeded(context, "User rate limit exceeded");
            return;
        }

        // Increment counters
        IncrementCounter(globalKey);
        IncrementCounter(compositeKey);

        // Add rate limit headers
        AddRateLimitHeaders(context, compositeKey, rateLimit);

        // Log rate limiting if approaching limits
        LogIfApproachingLimits(compositeKey, rateLimit);

        await _next(context);
    }

    /// <summary>
    /// Get rate limit for specific endpoint and user
    /// </summary>
    private int GetRateLimit(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant() ?? "";
        var method = context.Request.Method.ToUpperInvariant();

        // Specific endpoint limits
        return path switch
        {
            var p when p.Contains("/files") && method == "POST" => _options.UploadRpm,
            var p when p.Contains("/download") => _options.DownloadRpm,
            var p when p.Contains("/share") && method == "POST" => _options.ShareRpm,
            var p when p.Contains("/folders") && method == "GET" && p.Contains("/download") => _options.DownloadRpm,
            _ => _options.PerUserRpm
        };
    }

    /// <summary>
    /// Get endpoint identifier for rate limiting
    /// </summary>
    private static string GetEndpointIdentifier(HttpContext context)
    {
        var path = context.Request.Path.Value?.ToLowerInvariant() ?? "";
        var method = context.Request.Method.ToUpperInvariant();

        // Group similar endpoints
        if (path.Contains("/files"))
        {
            if (method == "POST") return "file_upload";
            if (path.Contains("/download")) return "file_download";
            if (path.Contains("/share")) return "file_share";
            return "file_operations";
        }

        if (path.Contains("/folders"))
        {
            if (path.Contains("/download")) return "folder_download";
            return "folder_operations";
        }

        if (path.Contains("/shares"))
        {
            return "share_access";
        }

        return "general";
    }

    /// <summary>
    /// Check if request is within rate limit
    /// </summary>
    private bool CheckRateLimit(string key, int limit)
    {
        var now = DateTime.UtcNow;
        var windowStart = now.AddMinutes(-_options.WindowMinutes);

        if (!_requestCounts.TryGetValue(key, out var counter))
        {
            return true; // First request, allow it
        }

        // Reset counter if outside window
        if (counter.WindowStart < windowStart)
        {
            return true; // Window expired, allow request
        }

        // Check if limit exceeded
        return counter.Count < limit;
    }

    /// <summary>
    /// Increment request counter
    /// </summary>
    private void IncrementCounter(string key)
    {
        var now = DateTime.UtcNow;
        var windowStart = now.AddMinutes(-_options.WindowMinutes);

        _requestCounts.AddOrUpdate(key, 
            new RequestCounter 
            { 
                Count = 1, 
                WindowStart = now, 
                LastRequest = now 
            },
            (k, counter) =>
            {
                // Reset if window expired
                if (counter.WindowStart < windowStart)
                {
                    counter.Count = 1;
                    counter.WindowStart = now;
                }
                else
                {
                    counter.Count++;
                }
                counter.LastRequest = now;
                return counter;
            });
    }

    /// <summary>
    /// Add rate limit headers to response
    /// </summary>
    private void AddRateLimitHeaders(HttpContext context, string key, int limit)
    {
        if (_requestCounts.TryGetValue(key, out var counter))
        {
            var remaining = Math.Max(0, limit - counter.Count);
            var resetTime = counter.WindowStart.AddMinutes(_options.WindowMinutes);
            var resetSeconds = Math.Max(0, (int)(resetTime - DateTime.UtcNow).TotalSeconds);

            context.Response.Headers.Add("X-RateLimit-Limit", limit.ToString());
            context.Response.Headers.Add("X-RateLimit-Remaining", remaining.ToString());
            context.Response.Headers.Add("X-RateLimit-Reset", resetSeconds.ToString());
        }
    }

    /// <summary>
    /// Handle rate limit exceeded
    /// </summary>
    private async Task HandleRateLimitExceeded(HttpContext context, string message)
    {
        // Resolve ICurrentUserService from request scope
        var currentUserService = context.RequestServices.GetService<ICurrentUserService>();
        var userId = currentUserService?.UserId?.ToString() ?? "anonymous";
        var endpoint = GetEndpointIdentifier(context);
        var clientIp = GetClientIpAddress(context);

        _logger.LogWarning("Rate limit exceeded - User: {UserId}, Endpoint: {Endpoint}, IP: {ClientIp}, Message: {Message}",
            userId, endpoint, clientIp, message);

        context.Response.StatusCode = 429; // Too Many Requests
        context.Response.ContentType = "application/json";

        var response = new
        {
            error = "Rate limit exceeded",
            message,
            retryAfter = _options.WindowMinutes * 60 // seconds
        };

        await context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
    }

    /// <summary>
    /// Log warning when approaching rate limits
    /// </summary>
    private void LogIfApproachingLimits(string key, int limit)
    {
        if (_requestCounts.TryGetValue(key, out var counter))
        {
            var usage = (double)counter.Count / limit;
            if (usage >= 0.8) // 80% threshold
            {
                _logger.LogWarning("Rate limit approaching - Key: {Key}, Usage: {Usage:P}, Count: {Count}/{Limit}",
                    key, usage, counter.Count, limit);
            }
        }
    }

    /// <summary>
    /// Get client IP address
    /// </summary>
    private static string GetClientIpAddress(HttpContext context)
    {
        // Check for forwarded IP first (behind proxy/load balancer)
        var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(forwardedFor))
        {
            return forwardedFor.Split(',')[0].Trim();
        }

        var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(realIp))
        {
            return realIp;
        }

        return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }

    /// <summary>
    /// Perform periodic cleanup of old counters
    /// </summary>
    private void PerformCleanupIfNeeded()
    {
        var now = DateTime.UtcNow;
        
        // Cleanup every 5 minutes
        if (now - _lastCleanup < TimeSpan.FromMinutes(5))
            return;

        lock (_cleanupLock)
        {
            // Double-check pattern
            if (now - _lastCleanup < TimeSpan.FromMinutes(5))
                return;

            var cutoff = now.AddMinutes(-_options.WindowMinutes * 2); // Keep extra buffer
            var keysToRemove = _requestCounts
                .Where(kvp => kvp.Value.LastRequest < cutoff)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in keysToRemove)
            {
                _requestCounts.TryRemove(key, out _);
            }

            _lastCleanup = now;
            
            if (keysToRemove.Count > 0)
            {
                _logger.LogDebug("Cleaned up {Count} expired rate limit counters", keysToRemove.Count);
            }
        }
    }
} 