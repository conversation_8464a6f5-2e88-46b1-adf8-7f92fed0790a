using System.Net;
using System.Security;
using System.Text.Json;
using VeasyFileManager.Application.Extensions;

namespace VeasyFileManager.Api.Middleware;

/// <summary>
/// Global error handling middleware
/// </summary>
public class ErrorHandlingMiddleware(
    RequestDelegate next,
    ILogger<ErrorHandlingMiddleware> logger,
    IHostEnvironment environment)
{
    /// <summary>
    /// Process the HTTP request and handle any exceptions
    /// </summary>
    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private async Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var correlationId = context.Items["CorrelationId"]?.ToString() ?? Guid.NewGuid().ToString();
        var userId = GetUserIdFromContext(context);
        var ipAddress = GetClientIpAddress(context);

        // Log the exception with context
        logger.LogError(exception, 
            "Unhandled exception occurred. CorrelationId: {CorrelationId}, UserId: {UserId}, Path: {Path}, Method: {Method}, IP: {IpAddress}",
            correlationId, userId, context.Request.Path, context.Request.Method, ipAddress);

        // Determine response details based on exception type
        var (statusCode, error) = GetErrorResponse(exception);

        // Create error response
        var response = new ErrorResponse
        {
            CorrelationId = correlationId,
            Error = error,
            StatusCode = (int)statusCode,
            Timestamp = DateTime.UtcNow,
            Path = context.Request.Path,
            Method = context.Request.Method
        };

        // Include stack trace only in development
        if (environment.IsDevelopment())
        {
            response.Details = exception.ToString();
        }

        // Set response
        context.Response.StatusCode = (int)statusCode;
        context.Response.ContentType = "application/json";

        var jsonResponse = JsonSerializer.Serialize(response, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });

        await context.Response.WriteAsync(jsonResponse);
    }

    private static (HttpStatusCode statusCode, string error) GetErrorResponse(Exception exception)
    {
        // Handle InvalidOperationException with specific logic
        if (exception is InvalidOperationException invalidOpEx)
        {
            var message = invalidOpEx.Message.ToLowerInvariant();
            if (message.Contains("already exists"))
                return (HttpStatusCode.Conflict, "Resource already exists");
            if (message.Contains("not found"))
                return (HttpStatusCode.NotFound, "Resource not found");
            if (message.Contains("permission"))
                return (HttpStatusCode.Forbidden, "Insufficient permissions");
            if (message.Contains("limit"))
                return (HttpStatusCode.BadRequest, "Request exceeds limits");
        }

        return exception switch
        {
            ArgumentException => (HttpStatusCode.BadRequest, "Invalid request parameters"),
            FileNotFoundException => (HttpStatusCode.NotFound, "The requested resource was not found"),
            DirectoryNotFoundException => (HttpStatusCode.NotFound, "The requested directory was not found"),
            UnauthorizedAccessException => (HttpStatusCode.Forbidden, "Access denied"),
            SecurityException => (HttpStatusCode.Forbidden, "Security violation detected"),
            TimeoutException => (HttpStatusCode.RequestTimeout, "The request timed out"),
            InvalidDataException => (HttpStatusCode.BadRequest, "Invalid data format"),
            NotSupportedException => (HttpStatusCode.BadRequest, "Operation not supported"),
            TaskCanceledException => (HttpStatusCode.RequestTimeout, "Request was cancelled"),
            HttpRequestException => (HttpStatusCode.BadGateway, "External service error"),
            _ => (HttpStatusCode.InternalServerError, "An internal server error occurred")
        };
    }

    private static Guid? GetUserIdFromContext(HttpContext context)
    {
        try
        {
            var userIdClaim = context.User?.FindFirst("sub")?.Value 
                             ?? context.User?.FindFirst("user_id")?.Value
                             ?? context.User?.FindFirst("id")?.Value;

            return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
        }
        catch
        {
            return null;
        }
    }

    private string GetClientIpAddress(HttpContext context)
    {
        try
        {
            // Check for forwarded IP first (for load balancers, proxies)
            var forwardedFor = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                // Take the first IP if multiple are present
                return forwardedFor.Split(',')[0].Trim();
            }

            // Check for real IP header
            var realIp = context.Request.Headers["X-Real-IP"].FirstOrDefault();
            if (!string.IsNullOrEmpty(realIp))
            {
                return realIp;
            }

            // Fall back to connection remote IP
            return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
        }
        catch
        {
            return "Unknown";
        }
    }
}

/// <summary>
/// Standard error response model
/// </summary>
public class ErrorResponse
{
    /// <summary>
    /// Correlation ID for tracking the request
    /// </summary>
    public string CorrelationId { get; set; } = null!;

    /// <summary>
    /// HTTP status code
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// Error message
    /// </summary>
    public string Error { get; set; } = null!;

    /// <summary>
    /// Additional error details (development only)
    /// </summary>
    public string? Details { get; set; }

    /// <summary>
    /// Timestamp when the error occurred
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// Request path that caused the error
    /// </summary>
    public string Path { get; set; } = null!;

    /// <summary>
    /// HTTP method
    /// </summary>
    public string Method { get; set; } = null!;
}

/// <summary>
/// Extension methods for registering error handling middleware
/// </summary>
public static class ErrorHandlingMiddlewareExtensions
{
    /// <summary>
    /// Add global error handling middleware to the pipeline
    /// </summary>
    public static IApplicationBuilder UseGlobalErrorHandling(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ErrorHandlingMiddleware>();
    }
} 