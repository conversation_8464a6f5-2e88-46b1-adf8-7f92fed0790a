# Git files
.git
.gitignore
.gitattributes

# Visual Studio / Visual Studio Code
.vs/
.vscode/
*.swp
*.swo

# Build outputs
**/bin/
**/obj/
**/out/

# NuGet packages
**/packages/

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# Test results
TestResults/
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/
[Ll]ogs/

# MSTest test Results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# Coverage files
*.coverage
*.coveragexml

# Windows image file caches
Thumbs.db
ehthumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node modules (if any frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp

# Documentation
*.md
docs/

# Docker files (avoid recursion)
Dockerfile*
.dockerignore

# CI/CD files
Jenkinsfile
.github/
azure-pipelines.yml

# Development files
*.Development.json
appsettings.Development.json

# Logs
logs/
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local